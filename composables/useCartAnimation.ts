// composables/useCartAnimation.ts
import gsap from "gsap";

function calculateDistance(x1: number, y1: number, x2: number, y2: number) {
	// 计算 x 坐标差值的平方
	const dx = x2 - x1;
	// 计算 y 坐标差值的平方
	const dy = y2 - y1;
	// 应用欧几里得距离公式
	return Math.sqrt(dx * dx + dy * dy);
}

export const useCartAnimation = () => {
	// 核心动画方法

	const cartStore = useCartStore();

	const { addCartTipVisible } = storeToRefs(cartStore);
	const animateToCart = (options: {
		startElement: HTMLElement | null;
		imgElement: HTMLImageElement | null;
		endElement: HTMLElement | null;
		scale?: number;
		duration: number;
		isEvenRow: number;
		hideLoading?: () => void;
		onComplete?: () => void;
	}) => {
		if (process.server) return;

		const { startElement, endElement, imgElement, scale = 0.3, duration = 800, isEvenRow, onComplete, hideLoading } = options;

		if (imgElement) {
			// 有效性检查
			if (!startElement || !endElement) {
				console.error("[动画错误] 必须提供有效的起始元素和终点元素");
				return;
			}

			if (isEvenRow === 1) {
				// 计算位置
				const startRect = startElement.getBoundingClientRect();
				const endRect = endElement.getBoundingClientRect();

				// 创建新的 img
				const flyImg = document.createElement("img");
				flyImg.src = imgElement.src;
				flyImg.style.position = "fixed";
				flyImg.style.left = startRect.left + "px";
				flyImg.style.top = startRect.top + "px";
				flyImg.style.width = startRect.width + "px";
				flyImg.style.height = startRect.height + "px";
				flyImg.style.pointerEvents = "none";
				flyImg.style.border = "1px solid #eee";
				flyImg.style.zIndex = "9999";
				flyImg.style.opacity = "0";
				document.body.appendChild(flyImg);
				if (!flyImg) return;

				function startAnimation() {
					hideLoading?.();
					flyImg.style.opacity = "1";
					// 起始点中心坐标
					const startX = startRect.left + (startRect.width - 50) / 2;
					const startY = startRect.top + (startRect.height - 50) / 2;
					// 终点中心坐标
					const endX = endRect.left;
					const endY = endRect.top;
					// 动画时间线
					console.log("距离：", startX, startY, endX, endY);

					// 调用函数计算距离
					const distance = calculateDistance(endX, endY, startX, startY);
					const t = (distance * 0.8) / duration;

					console.log(console.log("距离：", distance, "px"));
					console.log(t);
					gsap
						.timeline()
						.to(flyImg, {
							scale: 50 / startRect.width, // 按比例缩放
							transformOrigin: "center center",
							duration: 0.3,
							ease: "power2.out"
						})
						.to(flyImg, {
							x: endX - startX,
							y: endY - startY,
							scale: "*=0.3",
							duration: t,
							ease: "power2.out",
							onComplete: () => {
								flyImg.remove();
								endElement?.classList.add("cartAnimation");
								setTimeout(() => {
									endElement?.classList.remove("cartAnimation");
								}, 500);
								onComplete?.();
							}
						});
				}
				if (flyImg.complete) {
					startAnimation();
				} else {
					flyImg.onload = startAnimation;
				}
			} else if (isEvenRow === 2) {
				// 计算位置
				const startRect = startElement.getBoundingClientRect();
				const endRect = endElement.getBoundingClientRect();

				// 创建新的 img
				const flyImg = document.createElement("img");
				flyImg.src = imgElement.src;
				flyImg.style.position = "fixed";
				flyImg.style.left = startRect.left + (startRect.width - 50) / 2 + "px";
				flyImg.style.top = startRect.top - 70 + "px";
				flyImg.style.width = "50px";
				flyImg.style.border = "1px solid #eee";
				flyImg.style.height = "50px";
				flyImg.style.pointerEvents = "none";
				flyImg.style.zIndex = "9999";
				flyImg.style.opacity = "0";
				document.body.appendChild(flyImg);

				if (!flyImg) return;

				function startAnimation() {
					hideLoading?.();
					flyImg.style.opacity = "1";

					setTimeout(() => {
						// 起始点中心坐标
						const startX = startRect.left + (startRect.width - 50) / 2;
						const startY = startRect.top - 70;
						// 终点中心坐标
						const endX = endRect.left;
						const endY = endRect.top;
						// 动画时间线
						console.log("距离：", startX, startY, endX, endY);

						// 调用函数计算距离
						const distance = calculateDistance(endX, endY, startX, startY);
						const t = (distance * 0.8) / duration;

						console.log(console.log("距离：", distance, "px"));
						console.log(t);
						gsap.timeline().to(flyImg, {
							x: endX - startX,
							y: endY - startY,
							scale: 0.3,
							duration: t,
							ease: "power2.out",
							onComplete: () => {
								flyImg.remove();
								onComplete?.();
								cartStore.$patch(state => {
									state.addCartTipVisible = true;
								});
								setTimeout(() => {
									cartStore.$patch(state => {
										state.addCartTipVisible = false;
									});
								}, 3000);
							}
						});
					}, 100);
				}
				if (flyImg.complete) {
					startAnimation();
				} else {
					flyImg.onload = startAnimation;
				}
			} else if (isEvenRow === 3) {
				// 计算位置
				const startRect = startElement.getBoundingClientRect();
				const endRect = endElement.getBoundingClientRect();

				// 创建新的 img
				const flyImg = document.createElement("div");

				flyImg.style.position = "fixed";
				flyImg.style.left = startRect.left + (startRect.width - 10) / 2 + "px";
				flyImg.style.top = startRect.top + (startRect.height - 10) / 2 + "px";
				flyImg.style.width = "10px";
				flyImg.style.borderRadius = "10px";
				flyImg.style.height = "10px";
				flyImg.style.pointerEvents = "none";
				flyImg.style.background = "#C00000";
				flyImg.style.zIndex = "9999";
				flyImg.style.opacity = "0";
				document.body.appendChild(flyImg);

				if (!flyImg) return;

				function startAnimation() {
					hideLoading?.();
					flyImg.style.opacity = "1";

					// 起始点中心坐标
					const startX = startRect.left + (startRect.width - 10) / 2;
					const startY = startRect.top + (startRect.height - 10) / 2;
					// 终点中心坐标
					const endX = endRect.left + (endRect.width - 10) / 2 + 2;
					const endY = endRect.top + (endRect.height - 10) / 2;
					// 动画时间线
					console.log("距离：", startX, startY, endX, endY);

					// 调用函数计算距离
					const distance = calculateDistance(endX, endY, startX, startY);
					const t = (distance * 0.8) / duration;

					console.log(console.log("距离：", distance, "px"));
					console.log(t);
					gsap.timeline().to(flyImg, {
						x: endX - startX,
						y: endY - startY,
						// scale: 0.3,
						duration: t,
						ease: "power2.out",
						onComplete: () => {
							flyImg.remove();
							endElement?.classList.add("cartAnimation");
							setTimeout(() => {
								endElement?.classList.remove("cartAnimation");
							}, 500);
							onComplete?.();
						}
					});
				}

				startAnimation();
				// if (flyImg.complete) {
				// startAnimation();
				// } else {
				// 	flyImg.onload = startAnimation;
				// }
			}
		}
	};

	return { animateToCart };
};
