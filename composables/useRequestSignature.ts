// composables/useRequestSignature.ts

import { Base64 } from "js-base64";
import CryptoJS from "crypto-js";

function genNonce(length: number = 16): string {
	const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
	let result = "";
	for (let i = 0; i < length; i++) {
		const randomIndex = Math.floor(Math.random() * chars.length);
		result += chars[randomIndex];
	}
	return result;
}

const useRequestSignature = () => {
	const timestamps = Date.now();
	const nonce = genNonce(16);
	const apiKey = "yuxuanxuanpc";
	const payload = "[]";
	const secret = "yuxuan3507";

	const rawString = `${timestamps}${nonce}${payload}`;

	const hmac = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, secret);
	hmac.update(rawString);

	const clientSignature = Base64.encode(CryptoJS.enc.Hex.stringify(hmac.finalize()));

	return {
		timestamps,
		nonce,
		apiKey,
		clientSignature
	};
};

export default useRequestSignature;
