// 公共组件库地址 https://design.fs.com/components/button.html

import { visualizer } from "rollup-plugin-visualizer";
// import { sentryVitePlugin } from "@sentry/vite-plugin";
import i18n from "./lang/i18n";
import routers from "./routers";

const NODE_ENV = process.env.NODE_ENV;
const ENV = process.env.VITE_NUXT_ENV as string;
const isProd = NODE_ENV === "production" && ["PROD", "PROD_INDEX", "PROD_CN"].includes(ENV);
const args = process.argv.splice(5);
console.log("1111_@222");
console.log(process.env.VITE_BUILD_ASSETS_DIR);

const isServerHttps = args.find(i => i === "--server-https");
const serverHttps = isServerHttps
	? {
			key: "./ssl/key.pem",
			cert: "./ssl/cert.pem"
		}
	: false;

const nuxtConfig: object = {
	devtools: { enabled: true },
	devServer: {
		port: 2023,
		host: "0.0.0.0",
		https: serverHttps
	},
	debug: true,
	sourcemap: NODE_ENV === "development",
	server: {
		fs: {
			strict: false
		}
	},
	app: {
		buildAssetsDir: "/_nuxt/",
		cdnURL: NODE_ENV === "development" ? "" : process.env.VITE_NUXT_CDN_URL || ""
	},
	css: ["@/assets/scss/reset.scss", "@/node_modules/fs-design/lib/fs-design.css", "@/assets/iconfont/iconfont.css"],
	experimental: {
		scanPageMeta: false,
		inlineSSRStyles: false // 或用于确定内联的函数
	},
	vite: {
		vue: {
			script: {
				defineModel: true
			}
		},
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `
						@use "@/assets/scss/variable.scss" as *;
						@use "@/assets/scss/mixin.scss" as *;
					`
				}
			}
		},
		build: {
			target: "chrome68",
			// ...,
			commonjsOptions: {
				transformMixedEsModules: true
			},
			minify: "terser", // 压缩方式
			// sourcemap: true,
			cssCodeSplit: true,
			chunkSizeWarningLimit: 1024,
			terserOptions: {
				compress: {
					drop_console: !!(NODE_ENV === "production" && ["PROD", "PROD_INDEX", "PROD_CN"].includes(ENV)), // 剔除console,和debugger
					drop_debugger: !!(NODE_ENV === "production" && ["PROD", "PROD_INDEX", "PROD_CN"].includes(ENV))
				},
				output: {
					// 去掉注释内容
					comments: !!(NODE_ENV === "production" && ["PROD", "PROD_INDEX", "PROD_CN"].includes(ENV))
				}
			},
			rollupOptions: {
				output: {
					manualChunks(id) {
						if (id.includes("node_modules") && !id.includes("sentry")) {
							return id.toString().split("node_modules/")[1].split("/")[0].toString();
						}
					}
				}
			}
		},
		plugins: [visualizer({ open: true, gzipSize: true, brotliSize: true })]
	},
	hooks: {
		"pages:extend": pages => {
			pages.splice(0, pages.length);
			pages.push(...routers);
		}
	},
	modules: [
		[
			"@pinia/nuxt",
			{
				autoImports: ["defineStore"]
			}
		],
		"@nuxtjs/i18n",
		"@fs-front/nuxt-fs-logger",
		"nuxt-swiper",
		"nuxt-vitalizer"
	],
	vitalizer: {
		disableStylesheets: "entry",
		disablePreloadLinks: true
	},
	fsLogger: {
		defaultMeta: { app: "商城平台分类" },
		enable: process.env.NODE_ENV === "production"
	},
	i18n,

	build: {
		analyze: {}
	},
	alias: {
		// '@definePageMeta': '@/node_modules/nuxt/dist/pages/runtime/composables',
		// '@FsDesign': '@/node_modules/fs-design/src/'
	},
	nitro: {
		devProxy: {
			"/api": {
				target: process.env.VITE_NUXT_FS_API,
				changeOrigin: true,
				prependPath: true
			},
			"/cms": {
				target: process.env.VITE_NUXT_CMS_API,
				changeOrigin: true,
				prependPath: true
			},
			"/tools": {
				target: process.env.VITE_NUXT_TOOLS_API,
				changeOrigin: true,
				prependPath: true
			},
			"/order-api": {
				target: process.env.VITE_NUXT_CMS_API,
				changeOrigin: true
			}
		}
	},
	runtimeConfig: {
		public: {
			// VITE_SERVER_TYPE: process.env.VITE_SERVER_TYPE,
			// VITE_NUXT_ENV: process.env.VITE_NUXT_ENV,
			// NODE_ENV: process.env.NODE_ENV,
			// VITE_NUXT_DOMAIN: process.env.VITE_NUXT_DOMAIN,
			// VITE_NUXT_HOST: process.env.VITE_NUXT_HOST,
			// VITE_NUXT_PROXY: process.env.VITE_NUXT_PROXY,
			// VITE_NUXT_SERVER: process.env.VITE_NUXT_SERVER,
			// VITE_NUXT_CLIENT: process.env.VITE_NUXT_CLIENT,
			// VITE_NUXT_CDN_URL: process.env.VITE_NUXT_CDN_URL,
			// VITE_NUXT_CMS_API: process.env.VITE_NUXT_CMS_API,
			// VITE_NUXT_FEISHU_BOT: process.env.VITE_NUXT_FEISHU_BOT
			VITE_SERVER_TYPE: process.env.VITE_SERVER_TYPE,
			VITE_NUXT_ENV: process.env.VITE_NUXT_ENV,
			VITE_NUXT_DOMAIN: process.env.VITE_NUXT_DOMAIN,
			VITE_NUXT_FS_API: process.env.VITE_NUXT_FS_API,
			VITE_NUXT_FS_HK_API: process.env.VITE_NUXT_FS_HK_API,
			VITE_NUXT_CMS_API: process.env.VITE_NUXT_CMS_API,
			VITE_NUXT_CN_FS_API: process.env.VITE_NUXT_CN_FS_API,
			VITE_NUXT_CN_CMS_API: process.env.VITE_NUXT_CN_CMS_API,
			VITE_NUXT_TOOLS_API: process.env.VITE_NUXT_TOOLS_API,
			VITE_NUXT_CDN_URL: process.env.VITE_NUXT_CDN_URL,
			VITE_NUXT_FEISHU_BOT: process.env.VITE_NUXT_FEISHU_BOT,
			NODE_ENV: process.env.NODE_ENV,
			isProduction: process.env.NODE_ENV === "production",
			VITE_NUXT_CRM_API: process.env.VITE_NUXT_CRM_API,
			VITE_NUXT_CN_CRM_API: process.env.VITE_NUXT_CN_CRM_API,
			VITE_NUXT_CRM_SMS_APPID: process.env.VITE_NUXT_CRM_SMS_APPID,
			VITE_NUXT_CRM_SMS_SECRET: process.env.VITE_NUXT_CRM_SMS_SECRET
		}
	}
};

// if (isProd) {
// 	nuxtConfig.vite.plugins.push(
// 		sentryVitePlugin({
// 			authToken: "****************************************************************",
// 			org: "sentry",
// 			project: "nuxt3",
// 			url: "https://sentry.fs.com/"
// 		})
// 	);
// }

export default defineNuxtConfig(nuxtConfig);
