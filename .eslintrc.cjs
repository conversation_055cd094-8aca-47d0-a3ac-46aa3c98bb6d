module.exports = {
	env: {
		browser: true,
		es2021: true
	},
	extends: [
		"plugin:nuxt/recommended",
		"plugin:@typescript-eslint/recommended",
		"plugin:vue/vue3-essential",
		"@nuxtjs/eslint-config-typescript",
		"plugin:prettier/recommended"
	],
	overrides: [
		{
			env: {
				node: true
			},
			files: [".eslintrc.{js,cjs}"],
			parserOptions: {
				sourceType: "script"
			}
		}
	],
	parserOptions: {
		ecmaVersion: "latest",
		parser: "@typescript-eslint/parser",
		sourceType: "module"
	},
	plugins: ["@typescript-eslint"],
	rules: {
		"vue/multi-word-component-names": 0,
		"vue/prefer-import-from-vue": 0,
		"@typescript-eslint/no-explicit-any": "off",
		"@typescript-eslint/no-unused-vars": "off",
		"no-lonely-if": "off",
		"lines-between-class-members": "off",
		"handle-callback-err": "off",
		"n/handle-callback-err": "off",
		"n/no-callback-literal": "off",
		"vue/no-v-html": "off",
		camelcase: "off",
		complexity: ["error", 40],
		eqeqeq: ["off"],
		"no-console": "off",
		"no-case-declarations": "off",
		"vue/v-on-event-hyphenation": "off",
		"vue/attribute-hyphenation": "off",
		"no-unused-expressions": "off",
		"@typescript-eslint/no-var-requires": "off",
		"vue/valid-define-emits": "off",
		"@typescript-eslint/no-this-alias": "off",
		"prefer-rest-params": "off",
		"prefer-promise-reject-errors": "off",
		"vue/no-multiple-template-root": "off",
		"import/no-mutable-exports": "off",
		"no-useless-escape": "off",
		'no-prototype-builtins': 'off',
		'no-irregular-whitespace':'off',
		'vue/no-mutating-props': 'off',
		'complexity': ['error', { 'max': 150 }],
		"no-fallthrough": "off"
	}
};
