import { sanitizeForUrl } from "@/utils/utils";

export default defineNuxtRouteMiddleware((to, from) => {
	let originalPath = to.fullPath;
	// 先尝试解码 originalPath
	try {
		originalPath = decodeURIComponent(originalPath);
	} catch (e) {
		// 如果解码失败，使用原始路径
		console.warn("Failed to decode originalPath:", originalPath, e);
		return navigateTo("/");
	}
	const escapedPath = sanitizeForUrl(originalPath);
	if (originalPath !== escapedPath) {
		return navigateTo(escapedPath, { replace: true });
	}
});
