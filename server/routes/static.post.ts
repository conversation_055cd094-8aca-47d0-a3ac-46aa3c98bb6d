import fs from "fs";
import { Base64 } from "js-base64";
import { taskTimeout } from "../utils";
import { objectToQueryString } from "~/utils/utils";
import header from "~/lang/au/common/header";

export default defineEventHandler(async event => {
	const NODE_ENV = process.env.NODE_ENV;
	const runtimeConfig = useRuntimeConfig();
	const body = await readBody(event);
	const req: any = event?.node?.req;
	// const clientIp: string = req?.headers["x-forwarded-for"] && req?.headers["x-forwarded-for"].split(",").length ? req?.headers["x-forwarded-for"].split(",")[0] : "";

	// console.log("static_static_static")
	// console.log(event.node.req.headers)
	// console.log(body)
	const webSiteInfo: any = JSON.parse(Base64.decode(event.node.req.headers?.websiteinfo as string));
	const moduleName = body.moduleName;
	const { url, method = "GET", params = {}, filterId } = body;
	let baseUrl = "";

	// const baseUrl = isSelfProxy ? runtimeConfig.public.VITE_NUXT_FS_API : runtimeConfig.public.VITE_NUXT_CMS_API;

	if (/^\/api/.test(url)) {
		if (runtimeConfig.public.VITE_NUXT_ENV === "PROD_CN" && webSiteInfo && ["hk", "tw", "mo"].includes(webSiteInfo.website)) {
			baseUrl = runtimeConfig.public.VITE_NUXT_FS_HK_API || runtimeConfig.public.VITE_NUXT_FS_API;
		} else {
			if (webSiteInfo.website === "cn") {
				baseUrl = runtimeConfig.public.VITE_NUXT_CN_FS_API || runtimeConfig.public.VITE_NUXT_FS_API;
			} else {
				baseUrl = runtimeConfig.public.VITE_NUXT_FS_API;
			}
		}
	} else if (/^\/tools/.test(url)) {
		baseUrl = runtimeConfig.public.VITE_NUXT_TOOLS_API;
	} else {
		if (webSiteInfo.website === "cn") {
			baseUrl = runtimeConfig.public.VITE_NUXT_CN_CMS_API || runtimeConfig.public.VITE_NUXT_CMS_API;
		} else {
			baseUrl = runtimeConfig.public.VITE_NUXT_CMS_API;
		}
	}

	// console.log("staticstaticstaticstaticstaticstatic");
	// console.log(event.node.req.headers?.websiteinfo);
	// console.log("staticstaticstaticstaticstaticstatic");
	let fileName: string = "";
	let path: string = "";
	let result: any = {};
	const headersObj: any = {};
	const combinationUrl = method === "GET" ? `${url}${objectToQueryString(params) ? `?${objectToQueryString(params)}` : ""}` : url;
	if (
		webSiteInfo.website &&
		webSiteInfo.iso_code &&
		webSiteInfo.currency &&
		["TEST", "RELEASE", "RELEASE_INDEX", "PROD", "PROD_INDEX", "PROD_CN"].includes(runtimeConfig.public.VITE_NUXT_ENV)
	) {
		fileName = `${webSiteInfo.website}_${webSiteInfo.iso_code}_${webSiteInfo.currency}_${moduleName}_${filterId}`;
		path = NODE_ENV === "development" ? `./staticCache/${fileName}.json` : `/data/wwwroot/fs-front-category/staticCache/${fileName}.json`;

		console.log("static_111");
	}
	if (path && fs.existsSync(path)) {
		console.log("static_222");
		result = JSON.parse(fs.readFileSync(path).toString());
	} else {
		const otherParms = {
			body: method === "POST" ? JSON.stringify(params) : null
		};
		const reqHeaders: any = {
			"Content-Type": "application/json",
			webSiteInfo: event.node.req.headers?.websiteinfo as string,
			clientSignature: event.node.req.headers?.clientsignature as string,
			timestamps: event.node.req.headers?.timestamps as string,
			nonce: event.node.req.headers?.nonce as string,
			apiKey: event.node.req.headers?.apikey as string,
			supportWebp: event.node.req.headers?.supportwebp as string
		};
		if (event.node.req.headers?.shareUrlId) {
			reqHeaders.shareUrlId = event.node.req.headers?.shareUrlId;
		}

		if (event.node.req.headers?.Authorization) {
			reqHeaders.Authorization = event.node.req.headers?.Authorization;
		}
		if (event.node.req.headers?.cartId) {
			reqHeaders.cartId = event.node.req.headers?.cartId;
		}

		if (event.node.req.headers["fs-client-id"]) {
			reqHeaders["fs-client-id"] = event.node.req.headers["fs-client-id"];
		}

		const fn = fetch(`${baseUrl}${combinationUrl}`, {
			method,
			mode: "cors",
			headers: reqHeaders,
			...otherParms
		})
			.then(res => {
				const headers = res.headers;

				// console.log("res_res_res_static")
				// console.log(headers)
				headers.forEach((value, key) => {
					headersObj[key] = value;
				});
				// console.log("static_file");
				// console.log(headersObj);
				return res.json();
			})
			.catch(err => {
				console.log(err);
				throw new Error(`HTTP error! Status: ${err?.status}`);
			});
		try {
			result = await taskTimeout(fn, 30000);
			if (headersObj.websiteinfo && JSON.parse(Base64.decode(headersObj.websiteinfo))) {
				const resWebSiteInfo = JSON.parse(Base64.decode(headersObj.websiteinfo));
				fileName = `${resWebSiteInfo.website}_${resWebSiteInfo.iso_code}_${resWebSiteInfo.currency}_${moduleName}_${filterId}`;
				path = NODE_ENV === "development" ? `./staticCache/${fileName}.json` : `/data/wwwroot/fs-front-category/staticCache/${fileName}.json`;
			}
			// console.log("static_post_result");
			// console.log(result);
			if (result?.code === 200 || result?.code === 1) {
				fs.writeFileSync(path, JSON.stringify(result));
			}
		} catch (error) {
			console.log("static_result_error");
			console.log(combinationUrl);
			console.log(error, "error");
		}
	}
	setResponseStatus(event, result?.code ?? 200);
	Object.keys(headersObj).forEach(key => {
		if (!["content-encoding", "content-length", "transfer-encoding", "websiteinfo", "clientip", "ip-website-info"].includes(key.toLowerCase())) {
			// console.log("header_obj")
			// console.log(key)
			setResponseHeader(event, key, headersObj[key]);
		}
	});
	return result;
});
