import { sanitizeForUrl } from "@/utils/utils";

export default defineEventHandler(event => {
	console.log("11_22_xss_111");
	const reqUrl = event.node.req.url || "";

	// 尝试解码 URL
	let decodedUrl = reqUrl;
	try {
		decodedUrl = decodeURIComponent(reqUrl);
	} catch {
		console.warn("[Security] Blocked malformed URL:", reqUrl);
	}

	// 清理 URL
	const sanitizedUrl = sanitizeForUrl(decodedUrl);

	// 如果不同则重定向
	if (decodedUrl !== sanitizedUrl) {
		console.warn("[Security] Redirected unsafe URL:", decodedUrl, "->", sanitizedUrl);
		return sendRedirect(event, sanitizedUrl, 301);
	}
});
