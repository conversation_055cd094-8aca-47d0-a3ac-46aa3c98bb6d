// 公共的常量可以放里面

// 腾讯云控制台中对应这个项目的 appid
export const TENCENT_APPID = "2088583036";

// 需要选择洲的国家code
export const SELECT_STATE_COUNTRY_CODES = ["US", "CA"];

// 不同国家洲的默认值code
export const STATE_DEFAULT_MAPS = {
	US: "NY",
	CA: "AB"
};
// 移动端宽度
export const MOBILE_WIDTH = 768;

// 需要在sg站点注册时展示手机号的国家
export const SG_AREA_COUNTRY_CODE = ["SG", "TH", "PH", "VN", "MY", "MM", "LA", "ID", "KH", "BN"];

// 认证图标
export const certifications = {
	US: [
		// {
		// 	id: "DigiCertClickID_BHVOBHPu"
		// },
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		},
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/BBBorg.png",
			toUrl: "https://www.bbb.org/us/de/new-castle/profile/fiber-optics/fscom-0251-92010981",
			label: "Better Business Bureau"
		},
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google-de-pc.png",
			toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=US&v=19",
			label: "Google Customer Reviews"
		}
	], // 美东仓
	DE: {
		uk: [
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
				toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
				label: "TrustedSite"
			},
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/ssl.svg",
				toUrl: "javascript:;",
				label: "SSL"
			},
			// {
			//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
			//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
			// },
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google.png",
				toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=DE&v=19",
				label: "Google Customer Reviews"
			},
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/iaf.svg",
				toUrl: "javascript:;",
				label: "IAF"
			},
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/ukas.svg",
				toUrl: "javascript:;",
				label: "UKAS"
			}
		],
		"de-en": [
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
				toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
				label: "TrustedSite"
			},
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/ssl.svg",
				toUrl: "javascript:;",
				label: "SSL"
			},
			// {
			//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
			//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
			// },
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google.png",
				toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=DE&v=19",
				label: "Google Customer Reviews"
			}
		],
		de: [
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
				toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
				label: "TrustedSite"
			},
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/ssl.svg",
				toUrl: "javascript:;",
				label: "SSL"
			},
			// {
			//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
			//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
			// },
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google.png",
				toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=DE&v=19",
				label: "Google Customer Reviews"
			}
		],
		other: [
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
				toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
				label: "TrustedSite"
			},
			// {
			// 	id: "DigiCertClickID_BHVOBHPu"
			// },

			// {
			//     imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/norton-poweredby.png",
			//     toUrl: "https://seal.digicert.com/seals/popup/?tag=vyT8OdM_&url=www.fs.com&lang=en&cbr=1607596402526",
			//     label: "Norton",
			// },
			// {
			//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
			//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
			// },
			{
				imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google.png",
				toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=DE&v=19",
				label: "Google Customer Reviews"
			}
		]
	}, // 德国仓
	AU: [
		{
			id: "DigiCertClickID_BHVOBHPu"
		},
		// {
		//     imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/norton-poweredby.png",
		//     toUrl: "https://seal.digicert.com/seals/popup/?tag=vyT8OdM_&url=www.fs.com&lang=en&cbr=1607596402526",
		//     label: "Norton",
		// },
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		}
		// {
		//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
		//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
		// },
		// {
		//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google-de-pc.png',
		//     toUrl: 'https://customerreviews.google.com/v/merchant?q=fs.com&c=US&v=19'
		// },
	], // 澳洲仓
	SG: [
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		},
		{
			id: "DigiCertClickID_BHVOBHPu"
		}
		// {
		//     imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/norton-poweredby.png",
		//     toUrl: "https://seal.digicert.com/seals/popup/?tag=vyT8OdM_&url=www.fs.com&lang=en&cbr=1607596402526",
		//     label: "Norton",
		// },
		// {
		//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
		//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
		// },
		// {
		//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google-de-pc.png',
		//     toUrl: 'https://customerreviews.google.com/v/merchant?q=fs.com&c=US&v=19'
		// },
	], // 新加坡仓
	RU: [
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		},
		// {
		// 	id: "DigiCertClickID_BHVOBHPu"
		// },
		// {
		//     imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/norton-poweredby.png",
		//     toUrl: "https://seal.digicert.com/seals/popup/?tag=vyT8OdM_&url=www.fs.com&lang=en&cbr=1607596402526",
		//     label: "Norton",
		// },
		// {
		//     imgUrl: 'https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Trustwave-pc.png',
		//     toUrl: 'https://sealserver.trustwave.com/cert.php?customerId=520012bf276f4118a53a48f3e6627842&size=105x54&style='
		// },
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google-de-pc.png",
			toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=US&v=19",
			label: "Google Customer Reviews"
		}
	], // 俄罗斯仓
	CN: [
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		},
		{
			id: "DigiCertClickID_BHVOBHPu"
		},
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/Google-de-pc.png",
			toUrl: "https://customerreviews.google.com/v/merchant?q=fs.com&c=US&v=19",
			label: "Google Customer Reviews"
		}
	], // 深圳仓
	UK: [
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/fs-new/ssl.png",
			label: "SSL"
		},
		{
			imgUrl: "https://img-en.fs.com/includes/templates/fiberstore/images/new-pc-img/McAfee.svg",
			toUrl: "https://www.trustedsite.com/verify?host=www.fs.com&utm_campaign=mfes_redirect&utm_medium=referral&utm_source=mcafeesecure.com",
			label: "TrustedSite"
		}
	] // 英国仓
};

// 不同站点对应支付方式
export const countrysIcon = {
	uk: ["PayPal", "VISA", "American Express", "Maestro", "Mastercard", "Bank Transfer"],
	fr: ["VISA", "PayPal", "Mastercard", "American Express", "Discover", "Bank Transfer", "Mandat"],
	de: ["PayPal", "VISA", "American Express", "Vorkasse", "Mastercard", "Giropay", "Kauf auf Rechnung"],
	"de-en": ["PayPal", "VISA", "American Express", "Mastercard", "Maestro", "Bank Transfer", "iDEAL"],
	it: ["PayPal", "VISA", "American Express", "Mastercard", "Maestro", "Bank Transfer", "SOFORT"],
	es: ["PayPal", "VISA", "Mastercard", "Bank Transfer"],
	au: ["PayPal", "VISA", "Mastercard", "American Express", "Discover", "Direct Deposit", "Dinersclub"],
	mx: ["PayPal", "VISA", "Mastercard", "American Express", "Wire Transfer"],
	sg: ["VISA", "Mastercard", "American Express", "Bank Transfer", "JCB"]
};

// 支付方式对应图标
export const payIconList = {
	"American Express": {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018s6g5iq.svg",
		href: "javascript:;"
	},
	PayPal: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018e5uijj.svg",
		href: "javascript:;"
	},
	VISA: {
		imgur: "https://resource.fs.com/mall/generalImg/202312111440186697dj.svg",
		href: "javascript:;"
	},
	Mastercard: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018v0ttyh.svg",
		href: "javascript:;"
	},
	Maestro: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018d6petg.svg",
		href: "javascript:;"
	},
	JCB: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018fs2xr6.svg",
		href: "javascript:;"
	},
	iDEAL: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018x84sh5.svg",
		href: "javascript:;"
	},
	Giropay: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018fjlwpv.svg",
		href: "javascript:;"
	},
	SOFORT: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018z5zo61.svg",
		href: "javascript:;"
	},
	Discover: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018oyng3a.svg",
		href: "javascript:;"
	},
	Dinersclub: {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018ogu64m.svg",
		href: "javascript:;"
	},
	Vorkasse: {
		imgur: "https://resource.fs.com/mall/generalImg/2023121114401828x2tk.svg",
		href: "javascript:;"
	},
	"Kauf auf Rechnung": {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018k9llut.svg",
		href: "javascript:;"
	},
	"Bank Transfer": {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018nxok9s.svg",
		href: "javascript:;"
	},
	"Direct Deposit": {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018dqtro5.svg",
		href: "javascript:;"
	},
	"Wire Transfer": {
		imgur: "https://resource.fs.com/mall/generalImg/20231211144018hc61og.svg",
		href: "javascript:;"
	},
	Mandat: {
		imgur: "https://resource.fs.com/mall/generalImg/202312111440186fxyrk.svg",
		href: "javascript:;"
	}
};

export type CertificationsKeyType = keyof typeof certifications;

export type CountryIconPayType = keyof typeof countrysIcon;

export type CertificationsItem =
	| {
			imgUrl: string;
			toUrl: string;
			label: string;
	  }
	| { id: string };
