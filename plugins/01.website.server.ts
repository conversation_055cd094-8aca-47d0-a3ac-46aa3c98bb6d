import { website_prefix_str } from "@/constants/validate";

export default defineNuxtPlugin(_nuxtApp => {
	const headers = useRequestHeaders();
	const url = useRequestURL();
	const route = useRoute();
	const runtimeConfig = useRuntimeConfig();
	const { $i18n, $switchLocalePath } = useNuxtApp();
	const { locale, setLocale } = $i18n;
	const websiteStore = useWebsiteStore();
	const meatStore = useMeatStore();

	const website = useCookies("website");
	const zx_ip = useCookies("zx_ip");
	const iso_code = useCookies("iso_code");
	const currency = useCookies("currency");
	const language = useCookies("language");
	const initRequestURL = useCookies("initRequestURL");
	const initIp = useCookies("initIp");

	const initResIp = useCookies("initResIp");
	const initResIp1 = useCookies("initResIp1");
	const initResWebsite = useCookies("initResWebsite");
	const initReqWebsite = useCookies("initReqWebsite");

	const gpcEnabled = headers["sec-gpc"] === "1";

	websiteStore.$patch(state => {
		state.gpcEnabled = gpcEnabled;
	});

	meatStore.$patch(state => {
		state.domain = url?.origin;
	});

	const clientIp: string = headers["x-forwarded-for"] && headers["x-forwarded-for"].split(",").length ? headers["x-forwarded-for"].split(",")[0] : "";

	let realLocale = url?.pathname.split("/")[1];
	if (!website_prefix_str.test(realLocale)) {
		realLocale = "";
	}
	realLocale = realLocale === "eu-en" ? "de-en" : realLocale;
	if (!realLocale && runtimeConfig.public.VITE_NUXT_ENV) {
		if (runtimeConfig.public.VITE_NUXT_ENV === "PROD_CN") {
			realLocale = "cn";
		} else {
			realLocale = "en";
		}
	}

	websiteStore.$patch(state => {
		state.urlWebsite = realLocale;
		state.clientIp = clientIp || "";
	});

	// 移除多余的cookie
	website.value = null;
	iso_code.value = null;
	currency.value = null;
	language.value = null;
	initIp.value = null;
	initRequestURL.value = null;
	zx_ip.value = null;
	initResIp.value = null;
	initResIp1.value = null;
	initResWebsite.value = null;
	initReqWebsite.value = null;

	console.log("01_plugin");
});
