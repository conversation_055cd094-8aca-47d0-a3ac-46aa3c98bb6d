import detectDevice from "@/utils/detectDevice";
import { generateUUID } from "@/utils/utils";

export default defineNuxtPlugin(nuxtApp => {
	const deviceStore = useDeviceStore();
	const gaStore = useGaStore();
	const headerStore = useHeaderStore();
	const userStore = useUserStore();
	const countryStore = useCountryStore();

	const preWebsiteInfo = useCookies("preWebsiteInfo");
	const fs_websiteinfo = useCookies("fs_websiteinfo");
	const _fs_pvid = useCookies("_fs_pvid");

	// 服务端逻辑
	if (process.server) {
		const headers = useRequestHeaders();
		const ua = headers["user-agent"] || "";
		const deviceObj = detectDevice(ua);
		const supportWebp = headers.accept?.includes("image/webp") || false;
		const isChromeLighthouse = /lighthouse/i.test(ua);
		// 使用更准确的设备检测
		deviceStore.$patch({
			browser: deviceObj.browser,
			browserZH: deviceObj.browserZH,
			browserVersion: deviceObj.browserVersion,
			os: deviceObj.os,
			osVersion: deviceObj.osVersion,
			device: deviceObj.device,
			engine: deviceObj.engine,
			supportWebp,
			screenWidth: 0, // 服务端无法获取真实尺寸
			screenHeight: 0,
			isChromeLighthouse
		});
		console.log("02_detectDevice.ts");
		// 并行执行异步请求
		countryStore.getCountryName();
		headerStore.getHeaderData();
		userStore.getUserInfo();
	}

	// 客户端逻辑
	if (process.client) {
		// 确保每个客户端有唯一 ID
		if (!_fs_pvid.value) {
			_fs_pvid.value = generateUUID();
		}

		// 立即初始化设备信息
		const initDevice = () => {
			const w = document.documentElement.clientWidth;
			const h = document.documentElement.clientHeight;

			let deviceType = "pc";
			if (w <= 768) deviceType = "mobile";
			else if (w <= 1024) deviceType = "pad";

			deviceStore.$patch({
				device: deviceType,
				screenWidth: w,
				screenHeight: h
			});
		};

		// 初始化 reCAPTCHA
		const initGRecaptchaJs = () => {
			const sitekey = "6Lf874IpAAAAACYfn7X2StpklJzoaN3JehO956Xc";

			// 防止重复加载
			if (document.querySelector(`script[src*="/recaptcha/api.js"]`)) return;

			const script = document.createElement("script");
			script.src = `https://www.google.com/recaptcha/api.js?render=${sitekey}`;
			script.async = true;
			script.defer = true;

			script.onerror = () => {
				const fallback = document.createElement("script");
				fallback.src = `https://www.recaptcha.net/recaptcha/api.js?render=${sitekey}`;
				document.head.appendChild(fallback);
			};

			document.head.appendChild(script);
		};

		// 设置 GA 信息
		gaStore.$patch({
			pageLocation: window.location.href
		});

		// 立即执行初始化
		initDevice();
		initGRecaptchaJs();

		// 添加响应式监听
		const onResize = () => {
			initDevice();
			// 如果有 header 高度相关的逻辑
			if (typeof useHeaderHeight === "function") useHeaderHeight();
		};

		window.addEventListener("resize", onResize);
		// 在页面卸载前保存信息
		window.addEventListener("beforeunload", () => {
			preWebsiteInfo.value = fs_websiteinfo.value;
		});

		console.log("02_detectDevice.ts_end");
	}
});
