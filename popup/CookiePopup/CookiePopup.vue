<template>
	<FsDialog v-model="showDialog" className="cookie_popup" :width="isMobile ? '100%' : '750px'" Bottom @close="handelClose">
		<template #title>
			<div class="logo_box"></div>
		</template>
		<div class="popup_content">
			<div class="popup_content_main">
				<h1>{{ getCookiePopText("title") }}</h1>
				<p>{{ getCookiePopText("desc01") }}</p>
				<template v-if="isEuropeCountry">
					<a class="policy_more" :href="localeLink('/policies/privacy_policy.html')">{{ getCookiePopText("desc02") }}</a>
				</template>
				<template v-else>
					<p class="policy_tips" v-html="replaceCookieLinks(getCookiePopText('desc02'))"></p>
				</template>
				<h1>{{ getCookiePopText("manage") }}</h1>
				<div class="cookies_content">
					<ul>
						<li v-for="item in cookies_list" :key="item.id">
							<div class="cookies_item">
								<div class="label">
									<span v-show="item.isShow" class="iconfont iconfs_2020111801icon" @click="handleDescShow(item)"></span>
									<span v-show="!item.isShow" class="iconfont iconfs_2020111802icon" @click="handleDescShow(item)"></span>
									<b>{{ item.name }}</b>
								</div>
								<div>
									<span v-if="item.isAlways" class="isActive">{{ getCookiePopText("btn01") }}</span>
									<SwitchBox v-else v-model:value="item.isOpen" />
								</div>
							</div>
							<slide-down>
								<p v-if="item.isShow" class="des_box">{{ item.desc }}</p>
							</slide-down>
						</li>
					</ul>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="footer_btn">
				<FsButton v-if="!isEuropeCountry" type="gray" size="mediumSmall" @click="handleAgreeCookie">{{ getCookiePopText("btn02") }}</FsButton>
				<FsButton type="gray" size="mediumSmall" @click="handleAllow">{{ getCookiePopText("btn03") }}</FsButton>
				<FsButton type="black" size="mediumSmall" @click="handleConfirm">{{ getCookiePopText("btn04") }}</FsButton>
			</div>
		</template>
	</FsDialog>
</template>

<script setup>
import { FsDialog, FsButton } from "fs-design";
import SlideDown from "../../component/SlideDown/SlideDown";
import SwitchBox from "./SwitchBox";

const deviceStore = useDeviceStore();
const websiteStore = useWebsiteStore();
const isMobile = computed(() => deviceStore.isMobile);
const { website } = storeToRefs(websiteStore);

const localeLang = useLocaleLang();
const localeLink = useLocaleLink();

// 添加处理多个占位符替换的函数
const replaceCookieLinks = text => {
	return text.replace(/XXXX/g, localeLink("/policies/privacy_policy.html")).replace(/YYYY/g, localeLink("/policies/cookie_notice.html"));
};

const emits = defineEmits(["update:show", "close", "agree-cookie"]);

const props = defineProps({
	show: {
		type: Boolean,
		default: false
	},
	isEuropeCountry: {
		type: Boolean,
		default: false
	}
});
const fs_marketing_sdk = useCookies("fs_marketing_sdk", {
	maxAge: 60 * 60 * 24 * 365
});

// console.log(fs_marketing_sdk, "fs_marketing_sdk");

// mx的语言包
const cookieTipPop_mx = {
	title: `Cookie Preference Center`,
	desc01: `Cuando visitas cualquier sitio web, este puede almacenar o recuperar información en tu navegador, principalmente en forma de cookies. Esta información puede referirse a ti, tus preferencias o tu dispositivo, y se utiliza principalmente para que el sitio funcione como esperas. Esta información generalmente no te identifica directamente, pero puede brindarte una experiencia web más personalizada. Como respetamos tu derecho a la privacidad, puedes optar por no permitir ciertos tipos de cookies. Haz clic en los encabezados de las diferentes categorías para obtener más información y cambiar nuestra configuración predeterminada. Sin embargo, bloquear algunos tipos de cookies puede afectar tu experiencia en el sitio y los servicios que podemos ofrecemos.`,
	desc02: `Puedes aceptar o rechazar todas las cookies excepto las estrictamente necesarias, o personalizar tu configuración de cookies a continuación. Puedes cambiar tu configuración de cookies en cualquier momento. Para obtener más información sobre cómo FS trata los datos personales, visita nuestra <a href="XXXX">política de cookies</a>.`,
	manage: `Administrar preferencias de consentimiento`,
	option1: `Cookies estrictamente necesarias`,
	content1: `Estas cookies son necesarias para que el sitio funcione correctamente, como permitir el inicio de sesión seguro o recordar el progreso de un pedido. Son cookies imprescindibles para que podamos ofrecerte los servicios de la sociedad de la información que solicites. Estas cookies no almacenan ninguna información que permita identificarte personalmente.`,
	btn01: `Siempre activas`,
	option2: `Cookies de rendimiento`,
	content2: `Estas cookies nos permiten contar las visitas y las fuentes de tráfico para poder medir y mejorar el rendimiento de nuestro sitio. Nos ayudan a saber qué páginas son las más y las menos populares y a ver cómo se mueven los visitantes dentro del sitio. Toda la información que recopilan estas cookies se agrupa y, por lo tanto, es anónima. Si no permites estas cookies, no sabremos cuándo has visitado nuestro sitio ni podremos monitorear su rendimiento.`,
	btn02: `Permitir todas`,
	btn03: `Rechazar todas`,
	btn04: `Confirmar mi elección`
};

// 添加获取Cookie弹窗文本的方法
const getCookiePopText = key => {
	if (website.value === "mx") {
		return cookieTipPop_mx[key] || "";
	}
	return localeLang(`cookieTipPop.${key}`);
};

const cookies_list = ref([
	{ id: 1, name: getCookiePopText("option1"), isShow: false, isAlways: true, isOpen: true, desc: getCookiePopText("content1") },
	{ id: 2, name: getCookiePopText("option2"), isShow: false, isAlways: false, isOpen: Boolean(fs_marketing_sdk.value), desc: getCookiePopText("content2") }
]);
const showDialog = computed({
	get() {
		return props.show;
	},
	set(val) {
		emits("update:show", val);
	}
});

const handleDescShow = item => {
	cookies_list.value = cookies_list.value.map(i => {
		return item.id === i.id ? { ...i, isShow: !i.isShow } : i;
	});
};

const handelClose = () => {
	emits("update:show", false);
};

const handleAllow = () => {
	console.log("handelAllow");
	cookies_list.value[1].isOpen = true;
	updateCookies();
};

const handleConfirm = () => {
	console.log("handleConfirm");
	updateCookies();
};

// 更新cookies并重新加载页面
const updateCookies = () => {
	const fs_marketing_sdk_value = cookies_list.value[1].isOpen;
	// 更新cookies
	const fs_marketing_sdk = useCookies("fs_marketing_sdk", {
		maxAge: 60 * 60 * 24 * 365
	});
	fs_marketing_sdk.value = fs_marketing_sdk_value ? "yes" : null;
	const cookies_tip_hidden = useCookies("cookies_tip_hidden", {
		maxAge: 60 * 60 * 24 * 365
	});
	cookies_tip_hidden.value = "yes";
	handelClose();
	window && window.location.reload();
};

// 新增方法：触发父组件的agreeCookie方法
const handleAgreeCookie = () => {
	emits("update:show", false);
	emits("agree-cookie");
};
</script>

<style lang="scss" scoped>
.logo_box {
	display: inline-block;
	flex-shrink: 0;
	height: 36px;
	margin-right: 0;
	width: 76px;
	display: inline-block;
	width: 58px;
	height: 28px;
	@include bgContain("https://img-en.fs.com/includes/templates/fiberstore/images/fs-new/common/logo.svg");
}
.popup_content {
	max-width: 750px;
	.popup_content_main {
		@include font12;
		h1 {
			@include font14;
			font-weight: 600;
			margin-bottom: 4px;
		}
		p {
			color: $textColor2;
		}
		.policy_tips {
			margin-top: 20px;
			margin-bottom: 16px;
		}
		.policy_more {
			display: inline-block;
			margin-top: 4px;
			margin-bottom: 16px;
		}
	}
	.cookies_content {
		border: 1px solid #ededf0;
		border-radius: 8px;
		margin-top: 8px;
		> ul {
			> li {
				padding: 16px 20px;
				&:not(:last-of-type) {
					border-bottom: 1px solid #ededf0;
				}
			}
			.cookies_item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.label {
					color: $textColor1;
					display: flex;
					align-items: center;
					column-gap: 12px;
					@include font13;
					.iconfont {
						color: $textColor2;
						cursor: pointer;
						font-size: 14px;
						display: block;
						line-height: 1;
						&::before {
							display: block;
						}
					}
				}
			}
			.des_box {
				margin-top: 12px;
				@include font12;
			}
			.isActive {
				color: #10a300;
				font-weight: 600;
			}
		}
	}
}
.footer_btn {
	display: flex;
	justify-content: end;
	@media (max-width: 768px) {
		flex-direction: column-reverse;
		row-gap: 12px;
		.fs-button {
			margin: 0;
			height: 42px;
		}
	}
}
</style>
<style lang="scss">
.cookie_popup {
	.fs-dialog__contentM--box {
		padding-bottom: 16px !important;
	}
	.fs-dialog__footerM {
		padding: 16px 16px 24px !important;
		box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 0.2);
	}
}
</style>
