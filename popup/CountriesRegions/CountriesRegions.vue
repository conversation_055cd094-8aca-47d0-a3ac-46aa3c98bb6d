<template>
	<FsDialog v-model="show" :width="isMobile ? `100%` : `480px`" :Bottom="isMobile" className="countries_regions_popup" :title="localeLang('CountriesRegions.COUNTRIES_REGIONS')">
		<p class="desc2">{{ desc1 }} {{ desc2 }}</p>
		<template #footer>
			<span class="dialog_footer">
				<FsButton v-show="isMobile" type="gray" :size="isMobile ? 'medium' : 'mediumSmall'" @click="close">{{ btn1 }}</FsButton>
				<FsButton v-show="!isMobile" type="white" :size="isMobile ? 'medium' : 'mediumSmall'" @click="close">{{ btn1 }}</FsButton>
				<FsButton type="red" :size="isMobile ? 'medium' : 'mediumSmall'" :loading="loading" @click="changeWebsite">{{ btn2 }}</FsButton>
			</span>
		</template>
	</FsDialog>
</template>

<script setup lang="ts">
import { Ref, ref } from "vue";
import { storeToRefs } from "pinia";
import { FsButton, FsDialog } from "fs-design";

// 假设 cookie 数据结构
interface WebsiteInfo {
	website: string;
	iso_code?: string;
	country_name?: string;
}

type UpdateWebsiteInfoFlag = string | null;

// 假设你的 useCookies 是泛型函数
declare function useCookies<T>(name: string): Ref<T | null>;

// localeLang 假设签名
declare function useLocaleLang(): (key: string, params?: Record<string, string>) => string;

const localeLang = useLocaleLang();
const runtimeConfig = useRuntimeConfig();
const deviceStore = useDeviceStore();
const websiteStore = useWebsiteStore();
const countryStore = useCountryStore();

const preWebsiteInfo = useCookies<WebsiteInfo>("preWebsiteInfo");
const ipWebsiteInfo = useCookies<WebsiteInfo>("ipWebsiteInfo");
const fs_websiteinfo = useCookies<WebsiteInfo>("fs_websiteinfo");

const preWebsiteInfoCopy = preWebsiteInfo.value ? JSON.parse(JSON.stringify(preWebsiteInfo.value)) : {};

const { countryNameList } = storeToRefs(countryStore) as {
	countryNameList: Ref<Record<string, string>>;
};
const { isMobile } = storeToRefs(deviceStore) as {
	isMobile: Ref<boolean>;
};

const show = ref(false);
const desc1 = ref<string>("");
const desc2 = ref<string>("");
const btn1 = ref<string>("");
const btn2 = ref<string>("");
const type = ref<"" | "ip" | "pre">("");
const loading = ref(false);

const close = (): void => {
	show.value = false;
};

const init = (): void => {
	const updateWebsiteInfoFlag = useCookies<UpdateWebsiteInfoFlag>("updateWebsiteInfoFlag");

	if (!updateWebsiteInfoFlag.value) {
		if (!preWebsiteInfo.value) {
			// 如果当前站点和ip站点不一致，则弹出提示框
			if (websiteStore.website !== ipWebsiteInfo.value?.website) {
				const countryName = countryNameList.value[ipWebsiteInfo.value?.iso_code ?? ""] || ipWebsiteInfo.value?.country_name || "";

				desc1.value = localeLang("CountriesRegions.Seems_like_you_are_coming_from", { country: countryName });
				desc2.value = localeLang("CountriesRegions.Do_you_want_to_visit_FS_website_in_your_country_region", { country: countryName });
				btn1.value = localeLang("CountriesRegions.Stay_at", {
					country: websiteStore.country_name
				});
				btn2.value = localeLang("CountriesRegions.Go_to", {
					country: countryName
				});
				type.value = "ip";
				show.value = true;
			}
		} else {
			// 如果当前站点的和之前访问站点不一致，则弹出提示框
			if (websiteStore.urlWebsite !== preWebsiteInfo.value?.website) {
				const countryName = countryNameList.value[preWebsiteInfo.value?.iso_code ?? ""] || preWebsiteInfo.value?.country_name || "";

				desc1.value = localeLang("CountriesRegions.Seems_like_you_have_recently_visited_our_website_in", { country: countryName });
				desc2.value = localeLang("CountriesRegions.Do_you_want_to_visit_FS_website_in", { country: countryName });
				btn1.value = localeLang("CountriesRegions.Stay_at", {
					country: websiteStore.country_name
				});
				btn2.value = localeLang("CountriesRegions.Go_to", {
					country: countryName
				});
				type.value = "pre";
				show.value = true;
			}
		}
		preWebsiteInfo.value = fs_websiteinfo.value;
	} else {
		updateWebsiteInfoFlag.value = null;
	}
};

const changeWebsite = (): void => {
	let obj: WebsiteInfo | null = null;

	if (type.value) {
		if (type.value === "ip") {
			obj = ipWebsiteInfo.value;
		} else if (type.value === "pre") {
			obj = preWebsiteInfoCopy;
		}
		if (obj) {
			loading.value = true;
			websiteStore.updateWebsite(obj, false);
		}
	}
};

if (process.client) {
	setTimeout(() => {
		init();
	}, 0);
}
</script>
