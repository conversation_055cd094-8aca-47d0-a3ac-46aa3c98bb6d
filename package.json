{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"dev:test": "nuxt dev --dotenv .env.test", "build:test": "nuxt build --dotenv .env.test", "pm2-start-test": "pm2 start ecosystem.config.cjs --only fs-front-cate-test", "dev:auth": "nuxt dev --dotenv .env.auth", "build:auth": "nuxt build --dotenv .env.auth", "pm2-start-auth": "pm2 start ecosystem.config.cjs --only fs-front-cate-auth", "dev:sap": "nuxt dev --dotenv .env.sap", "build:sap": "nuxt build --dotenv .env.sap", "pm2-start-sap": "pm2 start ecosystem.config.cjs --only fs-front-cate-sap", "dev:website": "nuxt dev --dotenv .env.website", "build:website": "nuxt build --dotenv .env.website", "pm2-start-website": "pm2 start ecosystem.config.cjs --only fs-front-cate-website", "dev:release": "nuxt dev --dotenv .env.release", "build:release": "nuxt build --dotenv .env.release", "pm2-start-release": "pm2 start ecosystem.config.cjs --only fs-front-cate-release", "dev:release-index": "nuxt dev --dotenv .env.release.index", "build:release-index": "nuxt build --dotenv .env.release.index", "pm2-start-release-index": "pm2 start ecosystem.config.cjs --only fs-front-cate-release-index", "dev:prod": "nuxt dev --dotenv .env.prod", "build:prod": "nuxt build --dotenv .env.prod", "pm2-start-prod": "pm2 start ecosystem.config.cjs --only fs-front-cate-prod", "dev:prod-cn": "nuxt dev --dotenv .env.prod.cn", "build:prod-cn": "nuxt build --dotenv .env.prod.cn", "pm2-start-prod-cn": "pm2 start ecosystem.config.cjs --only fs-front-cate-prod-cn", "dev:prod-index": "nuxt dev --dotenv .env.prod.index", "build:prod-index": "nuxt build --dotenv .env.prod.index", "pm2-start-prod-index": "pm2 start ecosystem.config.cjs --only fs-front-cate-prod-index", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prepare": "husky install", "lint:style": "stylelint src/**/*.{css,scss,vue} --cache --fix", "preinstall": "npx only-allow pnpm"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@nuxt/devtools": "^1.7.0", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^9.33.0", "husky": "^8.0.3", "lint-staged": "^15.5.2", "postcss": "^8.5.4", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.77.2", "sass-loader": "13.3.3", "stylelint": "^16.20.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-standard": "^35.0.0", "stylelint-config-standard-scss": "^12.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.4", "stylelint-scss": "^6.12.0", "terser": "^5.42.0", "typescript": "^5.8.3", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression": "^0.5.1"}, "dependencies": {"@adyen/adyen-web": "5.68.1", "@aws-sdk/client-s3": "^3.864.0", "@fs-front/monitor": "^1.0.7", "@fs-front/nuxt-fs-logger": "^1.0.7", "@fs-front/site-redirect": "1.0.19", "@nuxtjs/i18n": "8.3.1", "@pinia/nuxt": "0.5.1", "@sentry/tracing": "7.120.3", "@sentry/vite-plugin": "2.23.0", "@sentry/vue": "7.120.3", "@types/crypto-js": "4.2.2", "chinese-simple2traditional": "1.2.0", "crypto-js": "4.2.0", "fs-design": "5.0.118-beta.0", "fs-design-swiper": "^0.0.6", "fs-gpt-sdk": "^0.7.3-beta.1", "gsap": "^3.13.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "jspdf": "^2.5.2", "katex": "^0.16.22", "libphonenumber-js": "^1.12.9", "nuxt": "3.12.2", "nuxt-swiper": "1.2.2", "nuxt-vitalizer": "0.10.0", "pinia": "2.1.7", "qrcode.vue": "3.6.0", "rollup": "4.29.2", "sass": "1.77.6", "sass-loader": "^16.0.5", "vue": "3.4.30", "vue-baidu-map-3x": "^1.0.40", "vue-i18n": "9.14.4", "vue-router": "4.3.2", "vue-slider-component": "4.1.0-beta.7", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-lazy": "1.0.0-alpha.1", "xgplayer-hls": "^3.0.22", "xss": "^1.0.15"}, "overrides": {"rollup": "4.29.2"}, "pnpm": {"overrides": {"magic-string": "0.26.2", "@intlify/shared": "9.14.4", "@intlify/core-base": "9.14.4", "@intlify/message-compiler": "9.14.4", "@intlify/vue-devtools": "9.14.4"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{scss,less,css,html,md}": ["prettier --write"], "package.json": ["prettier --write"], "{!(package)*.json,.!(browserslist)*rc}": ["prettier --write--parser json"]}}