// 存放  首页，分类页，搜索结果页，商品详情页路由
import { resolve } from "path";

let basic_router: any = [];
if (process.env.VITE_NUXT_ENV && ["RELEASE_INDEX", "PROD_INDEX"].includes(process.env.VITE_NUXT_ENV)) {
	basic_router = [
		{
			path: "/",
			name: "home",
			file: resolve(__dirname, "../pages/index/index.vue"),
			meta: {
				pageGroup: "Home Page"
			}
		}
	];
} else {
	basic_router = [
		{
			path: "/",
			name: "home",
			file: resolve(__dirname, "../pages/index/index.vue"),
			meta: {
				pageGroup: "Home Page"
			}
		},
		// sap分类路由
		{
			path: "/c/:id",
			name: "category",
			file: resolve(__dirname, "../pages/Category/Category.vue"),
			meta: {
				pageGroup: ""
			}
		},
		{
			path: "/test_category/:id",
			name: "test_category",
			file: resolve(__dirname, "../pages/TestCategory/Category.vue"),
			meta: {
				pageGroup: ""
			}
		},
		{
			path: "/case-study/:id.html",
			name: "case-study",
			file: resolve(__dirname, "../pages/CaseStudy/CaseStudy.vue"),
			meta: {
				pageGroup: "Case Study Page"
			}
		},
		{
			path: "/case-study.html",
			name: "case-study-list",
			file: resolve(__dirname, "../pages/CaseStudyList/CaseStudyList.vue"),
			meta: {
				pageGroup: "Case Study Page Detail"
			}
		},
		{
			name: "search_result",
			path: "/search_result",
			file: resolve(__dirname, "../pages/SearchResult/index.vue"),
			meta: {
				pageGroup: "Search Result Page"
			}
		},
		// {
		// 	path: "/testCom",
		// 	name: "testCom",
		// 	file: resolve(__dirname, "../pages/TestCom/index.vue")
		// },
		{
			path: "/share_url/:code/:id",
			name: "share_url",
			file: resolve(__dirname, "../pages/TransferPage/index.vue"),
			meta: {
				pageGroup: "Share Url Page"
			}
		}
	];
}

export default basic_router;
