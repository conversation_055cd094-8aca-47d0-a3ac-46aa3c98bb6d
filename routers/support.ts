// 专题、政策单页面、表单页面、解决方案、datasheet 等路由
import { resolve } from "path";
export default [
	{
		path: "/tool/:id",
		name: "tool",
		file: resolve(__dirname, "../pages/Tools/index.vue"),
		meta: {
			pageGroup: "Customer Service_Tool"
		}
	},
	{
		path: "/fscare-customer-services.html",
		name: "customer-services",
		file: resolve(__dirname, "../pages/Service/Service.vue"),
		meta: {
			pageGroup: "Customer Service_Customer Services"
		}
	},
	{
		path: "/picos-software-customized.html",
		name: "software-customized",
		file: resolve(__dirname, "../pages/Supports/PicosSoftware/PicosSoftware.vue"),
		meta: {
			pageGroup: "Customer Service_Software Customized"
		}
	},
	{
		path: "/contact_sales_mail.html",
		name: "contact_sales_mail",
		file: resolve(__dirname, "../pages/Supports/ContactSalesMail/ContactSalesMail.vue"),
		meta: {
			pageGroup: "Customer Service_Contact Sales Mail"
		}
	},
	{
		path: "/how-to-buy.html",
		name: "how-to-buy",
		file: resolve(__dirname, "../pages/HowToBuy/HowToBuy.vue"),
		meta: {
			pageGroup: "How To Buy Page"
		}
	},
	{
		path: "/products_support.html",
		name: "products-support",
		file: resolve(__dirname, "../pages/TechnicalDocuments/ProductSupport/ProductSupport.vue"),
		meta: {
			pageGroup: "Customer Service_Technical Documents"
		}
	},
	{
		path: "/products_support/preview_sheet.html",
		name: "preview_sheet",
		file: resolve(__dirname, "../pages/TechnicalDocuments/ProductSupport/components/PreviewCmsSheet/Index.vue"),
		meta: {
			pageGroup: ""
		}
	},
	{
		path: "/technical_documents.html",
		name: "technical_documents",
		file: resolve(__dirname, "../pages/TechnicalDocuments/TechnicalDocuments.vue"),
		meta: {
			pageGroup: "Customer Service_Technical Documents"
		}
	},
	{
		path: "/products_support/search.html",
		name: "technical_documents_search",
		file: resolve(__dirname, "../pages/TechnicalDocuments/Search.vue"),
		meta: {
			pageGroup: "Technical Documents Search Page"
		}
	},
	{
		name: "specials",
		path: "/specials/:id.html",
		file: resolve(__dirname, "../pages/Specials/Specials.vue"),
		meta: {
			pageGroup: "Customer Service_Technical Documents"
		}
	},
	{
		name: "supports",
		path: "/support/:id.html",
		file: resolve(__dirname, "../pages/Specials/Specials.vue"),
		meta: {
			pageGroup: "Specials Page"
		}
	},
	{
		path: "/blog.html",
		name: "blog",
		file: resolve(__dirname, "../pages/Blog/Blog.vue"),
		meta: {
			pageGroup: "Blog Page"
		}
	},
	{
		path: "/blog/:id.html",
		name: "blog_detail",
		file: resolve(__dirname, "../pages/BlogDetail/BlogDetail.vue"),
		meta: {
			pageGroup: "Blog Page Detail"
		}
	},
	{
		path: "/blog_preview.html",
		name: "blog_preview",
		file: resolve(__dirname, "../pages/BlogDetail/BlogPreview.vue"),
		meta: {
			pageGroup: ""
		}
	},
	{
		path: "/create-new-support",
		name: "create_new_support",
		file: resolve(__dirname, "../pages/Supports/CreateNewCase/CreateNewCase.vue"),
		meta: {
			pageGroup: "Create New Support Page"
		}
	},
	{
		path: "/site_map.html",
		name: "site-map",
		file: resolve(__dirname, "../pages/SiteMap/SiteMap.vue")
	},
	{
		path: "/solution-demo-test.html",
		name: "solution_demo_test",
		file: resolve(__dirname, "../pages/Supports/SolutionDemoTest/SolutionDemoTest.vue"),
		meta: {
			pageGroup: "Customer Service_Solution Demo Test"
		}
	},
	{
		path: "/data-center-solution-demo-test.html",
		name: "data_center_solution_demo_test",
		file: resolve(__dirname, "../pages/Supports/SolutionDemoTest/SolutionDemoTest.vue"),
		meta: {
			pageGroup: "Customer Service_Data Center Demo Test"
		}
	},
	{
		path: "/service/rma_checklist.html",
		name: "rma_checklist",
		file: resolve(__dirname, "../pages/Supports/RMAChecklist/RMAChecklist.vue"),
		meta: {
			pageGroup: "Personal Hub_Order History Page_Popup Tech Support Application"
		}
	},
	{
		path: "/glossary.html",
		name: "glossary",
		file: resolve(__dirname, "../pages/Glossary/Glossary.vue"),
		meta: {
			pageGroup: "Customer Service_Glossary"
		}
	},
	{
		path: "/glossary/:id.html",
		name: "glossary_detail",
		file: resolve(__dirname, "../pages/GlossaryDetail/GlossaryDetail.vue"),
		meta: {
			pageGroup: "Customer Service_Glossary"
		}
	},
	{
		path: "/glossary_preview.html",
		name: "glossary_preview",
		file: resolve(__dirname, "../pages/GlossaryDetail/GlossaryPreview.vue"),
		meta: {
			pageGroup: ""
		}
	},
	{
		path: "/test_service.html",
		name: "test_service",
		file: resolve(__dirname, "../pages/Supports/TestService/TestService.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	{
		path: "/solution_test.html",
		name: "solution_test",
		file: resolve(__dirname, "../pages/Supports/SolutionTest/SolutionTest.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	{
		path: "/reliability_test.html",
		name: "reliability_test",
		file: resolve(__dirname, "../pages/Supports/ReliabilityTest/ReliabilityTest.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	{
		path: "/compatibility_test.html",
		name: "compatibility_test",
		file: resolve(__dirname, "../pages/Supports/CompatibilityTest/CompatibilityTest.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	{
		path: "/performance_test.html",
		name: "performance_test",
		file: resolve(__dirname, "../pages/Supports/PerformanceTest/PerformanceTest.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	// {
	// 	path: "/free_design_solution.html",
	// 	name: "free_design_solution",
	// 	redirect: "/solution-services.html"
	// },
	{
		path: "/solution-services.html",
		name: "free_design_solution",
		file: resolve(__dirname, "../pages/Specials/FreeDesignSolution/FreeDesignSolution.vue"),
		meta: {
			pageGroup: "pageGroup"
		}
	},
	{
		path: "/fiber_performance_calculator.html",
		name: "fiber_performance_calculator",
		file: resolve(__dirname, "../pages/Specials/FiberPerformanceCalculator/index.vue"),
		meta: {
			pageGroup: "fiber_performance_calculator"
		}
	},
	{
		path: "/calculator_pdf.html",
		name: "calculator_pdf",
		file: resolve(__dirname, "../pages/Specials/CalculatorPdf/index.vue"),
		meta: {
			pageGroup: "calculator_pdf"
		}
	},
	{
		path: "/mtp-polarity-detection-tool.html",
		name: "mtp-polarity-detection-tool",
		file: resolve(__dirname, "../pages/MTPTool/MTPTool.vue"),
		meta: {
			pageGroup: "mtp-polarity-detection-tool"
		}
	},
	{
		path: "/mtp-polarity-detection-tool-pdf",
		name: "mtp-polarity-detection-tool-pdf",
		file: resolve(__dirname, "../pages/MTPTool/MTPToolPdf.vue"),
		meta: {
			pageGroup: "mtp-polarity-detection-tool-pdf"
		}
	},
	// {
	// 	path: "/calculator_pdf.html",
	// 	name: "calculator_pdf",
	// 	file: resolve(__dirname, "../pages/Specials/CalculatorPdf/index.vue"),
	// 	meta: {
	// 		pageGroup: "calculator_pdf"
	// 	}
	// },
	{
		path: "/media.html",
		name: "media",
		file: resolve(__dirname, "../pages/Video/Video.vue"),
		meta: {
			pageGroup: "media"
		}
	},
	{
		path: "/media/:id.html",
		name: "media_detail",
		file: resolve(__dirname, "../pages/VideoDetail/VideoDetail.vue"),
		meta: {
			pageGroup: "media_detail"
		}
	},
	{
		path: "/report-vulnerability.html",
		name: "report_vulnerability",
		file: resolve(__dirname, "../pages/ReportVulnerability/ReportVulnerability.vue")
	},
	{
		path: "/400g-ai-roce-solution-inquiry.html",
		name: "400g-ai-roce-solution-inquiry.html",
		file: resolve(__dirname, "../pages/Supports/SolutionInquiry/SolutionInquiry.vue"),
		meta: {
			pageGroup: "400G Solution  Inquiry Page"
		}
	},
	{
		path: "/400g-ai-roce-solution-inquiry_success.html",
		name: "400g-ai-roce-solution-inquiry_success.html",
		file: resolve(__dirname, "../pages/Supports/SolutionInquiry/SolutionSuccess.vue")
	},
	{
		path: "/careers.html",
		name: "careers",
		file: resolve(__dirname, "../pages/Careers/Careers.vue"),
		meta: {
			pageGroup: "careers"
		}
	},
	{
		path: "/dc-fabric-scaling-tool.html",
		name: "dc_fabric_scaling_tool",
		file: resolve(__dirname, "../pages/DCFabricScalingTool/DCFabricScalingTool.vue"),
		meta: {
			pageGroup: "dc_fabric_scaling_tool"
		}
	},
	{
		path: "/service/search_feedback.html",
		name: "search_feedback",
		file: resolve(__dirname, "../pages/Supports/SearchFeedback/SearchFeedback.vue"),
		meta: {
			pageGroup: "SearchFeedback Page"
		}
	},
	{
		path: "/communication_service.html",
		name: "communication_service",
		file: resolve(__dirname, "../pages/Supports/CommunicationService/CommunicationService.vue"),
		meta: {
			pageGroup: "Communication Service Page"
		}
	},
	{
		path: "/service_satisfaction_survey.html",
		name: "Service Satisfaction Survey",
		file: resolve(__dirname, "../pages/Supports/SatisfactionSurvey/SatisfactionSurvey.vue")
	},
	{
		path: "/policies/policy_submit.html",
		name: "PolicySubmit",
		file: resolve(__dirname, "../pages/Supports/PolicySubmit/PolicySubmit.vue"),
		meta: {
			pageGroup: "PolicySubmit Page"
		}
	},
	{
		path: "/feature_update.html",
		name: "feature_update",
		file: resolve(__dirname, "../pages/Supports/CloseShowPage/index.vue"),
		meta: {
			pageGroup: "feature_update"
		}
	}
	// DC Fabric Scaling Tool
];
