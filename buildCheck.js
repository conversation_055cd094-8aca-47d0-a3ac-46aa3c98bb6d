import fs from "fs";
import path from "path";
import crypto from "crypto";
import { S3Client, GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";

function generateFileHash(dir) {
	const manifest = {};

	function walk(currentPath) {
		const entries = fs.readdirSync(currentPath);
		entries.forEach(entry => {
			const fullPath = path.join(currentPath, entry);
			const stat = fs.statSync(fullPath);
			if (stat.isDirectory()) {
				walk(fullPath);
			} else {
				const fileContent = fs.readFileSync(fullPath);
				const relativePath = path.relative(dir, fullPath);
				const hash = crypto.createHash("sha256").update(fileContent).update(relativePath).digest("hex");
				manifest[relativePath] = hash;
			}
		});
	}

	walk(dir);
	return manifest;
}

// AWS S3 配置
const s3Client = new S3Client({
	region: "us-west-2",
	credentials: {
		accessKeyId: "********************",
		secretAccessKey: "9DjT8PRQce1mUEMrIpwpYiSQCwCVqeAzFF4hM7NA"
	}
});

const S3_BUCKET = "feisu-resources";
const S3_MANIFEST_KEY = `fs-front/fs-front-category/${process.env.NODE_ENV}/_nuxt`;
const MANIFEST_FILE_NAME = "file-manifest.json";

// 从 S3 下载旧的 manifest 文件
async function downloadManifestFromS3() {
	try {
		const fullKey = `${S3_MANIFEST_KEY}/${MANIFEST_FILE_NAME}`;
		const command = new GetObjectCommand({
			Bucket: S3_BUCKET,
			Key: fullKey
		});
		const response = await s3Client.send(command);
		const manifestContent = await response.Body.transformToString();
		return JSON.parse(manifestContent);
	} catch (error) {
		if (error.name === "NoSuchKey") {
			console.log("S3 中没有找到之前的清单文件，将视为首次构建");
		} else {
			console.log("从 S3 下载清单文件失败:", error.message);
		}
		return {};
	}
}

// 获取文件的 Content-Type
function getContentType(filePath) {
	const ext = path.extname(filePath).toLowerCase();
	const contentTypes = {
		".js": "application/javascript",
		".css": "text/css",
		".html": "text/html",
		".json": "application/json",
		".png": "image/png",
		".jpg": "image/jpeg",
		".jpeg": "image/jpeg",
		".gif": "image/gif",
		".svg": "image/svg+xml",
		".ico": "image/x-icon",
		".woff": "font/woff",
		".woff2": "font/woff2",
		".ttf": "font/ttf",
		".eot": "application/vnd.ms-fontobject"
	};
	return contentTypes[ext] || "application/octet-stream";
}

// 上传单个文件到 S3
async function uploadFileToS3(absolutePath, buildDir) {
	try {
		const relativePath = path.relative(buildDir, absolutePath);

		// 跳过 builds 文件夹中的文件，因为 uploadBuildsFolder 函数会专门处理
		if (relativePath.includes("builds/")) {
			console.log(`  ⏭️  跳过 builds 文件: ${relativePath} (由 uploadBuildsFolder 处理)`);
			return;
		}

		// 上传其他文件，保持完整路径结构
		const buildsRelativePath = relativePath.replace("_nuxt/", "");
		const s3Key = `${S3_MANIFEST_KEY}/${buildsRelativePath}`;
		const fileContent = fs.readFileSync(absolutePath);
		const contentType = getContentType(absolutePath);

		const command = new PutObjectCommand({
			Bucket: S3_BUCKET,
			Key: s3Key,
			Body: fileContent,
			ContentType: contentType
		});

		await s3Client.send(command);
		console.log(`  ✅ 已上传: ${s3Key}`);
	} catch (error) {
		console.error(`  ❌ 上传失败 ${path.relative(buildDir, absolutePath)}: ${error.message}`);
		process.exit(1);
	}
}

// 批量上传文件到 S3
async function uploadFilesToS3(filePaths, buildDir) {
	if (filePaths.length === 0) return;

	console.log(`📤 开始上传 ${filePaths.length} 个文件到 S3...`);

	// 并发上传，但限制并发数量避免过载
	const concurrency = 5;
	for (let i = 0; i < filePaths.length; i += concurrency) {
		const batch = filePaths.slice(i, i + concurrency);
		await Promise.allSettled(batch.map(filePath => uploadFileToS3(filePath, buildDir)));
	}

	console.log(`✅ 所有文件上传完成`);
}

// 上传新的 manifest 文件到 S3
async function uploadManifestToS3(manifest) {
	try {
		const fullKey = `${S3_MANIFEST_KEY}/${MANIFEST_FILE_NAME}`;
		const command = new PutObjectCommand({
			Bucket: S3_BUCKET,
			Key: fullKey,
			Body: JSON.stringify(manifest, null, 2),
			ContentType: "application/json"
		});
		await s3Client.send(command);
		console.log("✅ 新的清单文件已上传到 S3");
	} catch (error) {
		console.error("❌ 上传清单文件到 S3 失败:", error.message);
		process.exit(1);
	}
}

// 直接上传整个 builds 文件夹
async function uploadBuildsFolder(buildDir) {
	const buildsPath = path.join(buildDir, "_nuxt/builds");

	if (!fs.existsSync(buildsPath)) {
		console.log("⚠️  builds 文件夹不存在，跳过上传");
		return;
	}

	console.log("📤 开始上传整个 builds 文件夹...");

	// 收集 builds 文件夹中的所有文件
	const buildsFiles = [];

	function walk(currentPath) {
		const entries = fs.readdirSync(currentPath);
		entries.forEach(entry => {
			const fullPath = path.join(currentPath, entry);
			const stat = fs.statSync(fullPath);
			if (stat.isDirectory()) {
				walk(fullPath);
			} else {
				buildsFiles.push(fullPath);
			}
		});
	}

	walk(buildsPath);

	if (buildsFiles.length > 0) {
		console.log(`📁 builds 文件夹包含 ${buildsFiles.length} 个文件`);

		// 并发上传 builds 文件夹中的所有文件
		const concurrency = 5;
		for (let i = 0; i < buildsFiles.length; i += concurrency) {
			const batch = buildsFiles.slice(i, i + concurrency);
			await Promise.all(
				batch.map(async filePath => {
					try {
						const relativePath = path.relative(buildDir, filePath);
						const buildsRelativePath = relativePath.replace("_nuxt/", "");
						const s3Key = `${S3_MANIFEST_KEY}/${buildsRelativePath}`;
						const fileContent = fs.readFileSync(filePath);
						const contentType = getContentType(filePath);

						const command = new PutObjectCommand({
							Bucket: S3_BUCKET,
							Key: s3Key,
							Body: fileContent,
							ContentType: contentType
						});

						await s3Client.send(command);
						console.log(`  ✅ 已上传 builds 文件: ${s3Key}`);
					} catch (error) {
						console.error(`  ❌ 上传 builds 文件失败 ${path.relative(buildDir, filePath)}: ${error.message}`);
						process.exit(1);
					}
				})
			);
		}

		console.log("✅ builds 文件夹上传完成");
	} else {
		console.log("📁 builds 文件夹为空");
	}
}

async function main() {
	const buildDir = "./.output/public";

	// 生成新的文件清单
	const newManifest = generateFileHash(buildDir);

	// 从 S3 读取之前的文件清单
	const oldManifest = await downloadManifestFromS3();

	// 如果是首次构建（S3 中没有找到文件），直接创建并上传新的 manifest
	if (Object.keys(oldManifest).length === 0) {
		console.log("🆕 首次构建，创建新的清单文件");
		console.log(`📁 总共扫描到 ${Object.keys(newManifest).length} 个文件`);

		// 保存到本地（可选）
		fs.writeFileSync("./file-manifest.json", JSON.stringify(newManifest, null, 2));
		console.log("✅ 本地清单文件已创建");

		// 首次构建，上传所有文件
		const allFiles = Object.keys(newManifest).map(relativePath => path.resolve(buildDir, relativePath));
		if (allFiles.length > 0) {
			await uploadFilesToS3(allFiles, buildDir);
		}

		// 单独上传整个 builds 文件夹
		await uploadBuildsFolder(buildDir);

		// 上传 manifest 到 S3
		await uploadManifestToS3(newManifest);
		return;
	}

	// 比较新旧清单，找出修改或新增的文件，并将修改或新增的文件的绝对路径写到一个数组里面
	const changedFiles = [];
	const newFiles = [];
	for (const filePath of Object.keys(newManifest)) {
		const absolutePath = path.resolve(buildDir, filePath);
		if (!oldManifest[filePath]) {
			newFiles.push(absolutePath);
		} else if (oldManifest[filePath] !== newManifest[filePath]) {
			changedFiles.push(absolutePath);
		}
	}

	// 找出删除的文件
	const deletedFiles = [];
	for (const filePath of Object.keys(oldManifest)) {
		if (!newManifest[filePath]) {
			const absolutePath = path.resolve(buildDir, filePath);
			deletedFiles.push(absolutePath);
		}
	}

	// 打印结果
	if (changedFiles.length > 0) {
		console.log("🔄 修改的文件:");
		changedFiles.forEach(file => console.log(`  ${file}`));
	}

	if (newFiles.length > 0) {
		console.log("✅ 新增的文件:", newFiles.length);
		newFiles.forEach(file => console.log(`  ${file}`));
	}

	if (deletedFiles.length > 0) {
		console.log("❌ 删除的文件:");
		// deletedFiles.forEach(file => console.log(`  ${file}`));
	}

	if (changedFiles.length === 0 && newFiles.length === 0 && deletedFiles.length === 0) {
		console.log("✨ 没有文件变更");
	} else {
		// 上传变更的文件（新增 + 修改）
		const filesToUpload = [...changedFiles, ...newFiles];
		if (filesToUpload.length > 0) {
			await uploadFilesToS3(filesToUpload, buildDir);
		}
	}

	// 无论是否有变更，都上传整个 builds 文件夹（因为 builds 文件夹不在 manifest 中跟踪）
	await uploadBuildsFolder(buildDir);

	// 更新本地清单文件（可选）
	fs.writeFileSync("./file-manifest.json", JSON.stringify(newManifest, null, 2));

	// 上传新的清单文件到 S3
	await uploadManifestToS3(newManifest);
}

// 运行主函数
main().catch(error => {
	console.error("❌ 主函数执行失败:", error.message);
	process.exit(1);
});
