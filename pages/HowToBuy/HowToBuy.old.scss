.big_title {
	@include font20;
	font-weight: 600;
	letter-spacing: 0em;
	color: #19191a;
	font-family: Open Sans;
}

.step_title {
	font-family: Open Sans;
	@include font16;
	font-weight: 600;
	letter-spacing: 0em;
	color: #19191a;
}

.step_desc {
	font-family: Open Sans;
	@include font14;
	font-weight: 400;
	letter-spacing: 0em;
	color: #707070;
}

.how_to_buy {
	display: flex;
	flex-direction: column;
	align-items: center;
	.banner {
		width: 100%;
		height: 200px;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		display: flex;
		justify-content: center;
		align-items: center;
		p {
			font-family: Open Sans;
			@include font32;
			font-weight: 600;
			text-align: center;
			letter-spacing: 0em;
			color: #ffffff;
		}
	}
	.content {
		@include width1420;
		max-width: 1200px;
		display: flex;
		flex-direction: row;
		margin-top: 16px;
		.left {
			width: 24%;
			display: flex;
			flex-direction: column;
			margin-bottom: 20px;
			position: relative;
			.left_box {
				position: sticky;
				z-index: 10;
				overflow: auto;
				top: 16px;
				background: #f7f7f7;
				padding: 20px 0px 20px 16px;
				border-radius: 4px;
				.navItem {
					// &:nth-of-type(2) {
					// 	margin-top: 0px;
					// }
					width: 100%;
					display: flex;
					flex-direction: column;
					.category_title {
						height: 44px;
						p {
							margin: 11px 16px;
						}
					}

					.linkItem {
						display: flex;
						a {
							position: relative;
							margin: 11px 16px;
							color: #19191a;
							text-decoration: none;
						}
						&:hover {
							background-color: #fff;
							border-radius: 4px 0 0 4px;
							cursor: pointer;
							a {
								&::after {
									content: " ";
									position: absolute;
									left: 0;
									right: 0;
									bottom: -2px;
									width: 100%;
									height: 2px; /* 下划线的厚度 */
									border-radius: 1px;
									background: rgb(178, 38, 26); /* 下划线的颜色 */
									transition: all 0.3s ease-in-out; /* 可选的过渡效果 */
								}
							}
						}
					}
				}
			}
		}
		.right {
			margin-top: 0px;
			margin-bottom: 36px;
			width: 76%;
			display: flex;
			flex-direction: column;
			.ask {
				margin-top: 0px;
				margin-left: 36px;
				margin-right: 0px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				border-radius: 4px;
				.ask_left {
					display: flex;
					flex-direction: row;
					margin-top: 19px;
					margin-left: 20px;
					margin-bottom: 19px;
					align-items: center;
					gap: 16px;
					.first {
						font-family: Open Sans;
						@include font14;
						font-weight: 600;
						text-align: center;
						letter-spacing: 0em;
						color: #19191a;
					}
					.second {
						font-family: Open Sans;
						@include font13;
						font-weight: 600;
						text-align: center;
						letter-spacing: 0em;
						color: #707070;
					}
				}
				.ask_right {
					display: flex;
					flex-direction: row;
					margin-top: 12px;
					margin-bottom: 12px;
					margin-right: 20px;
					align-items: center;
					&:hover {
						cursor: pointer;
						span {
							text-decoration: underline;
						}
					}
					img {
						width: 36px;
						height: 36px;
					}
					span {
						margin-left: 16px;
						color: $textColor5;
						font-family: Open Sans;
						@include font12;
						font-weight: 400;
						text-align: left;
						letter-spacing: 0em;
					}
					.iconfont {
						transform: rotate(-90deg);
						color: $textColor5;
						font-size: 10px;
						margin-left: 4px;
					}
				}
			}
			.buyer_guide {
				margin-top: 32px;
				margin-left: 36px;
				margin-right: 0px;
				display: flex;
				flex-direction: column;
				h3 {
					margin-top: 0px;
					margin-left: 0px;
					width: 100%;
				}
				p {
					&:nth-of-type(1) {
						margin-top: 12px;
					}
				}
			}
			.line {
				background: #e5e5e5;
				margin-top: 32px;
				margin-left: 36px;
				margin-right: 0px;
				height: 1px;
			}
			.buy_online {
				margin-top: 32px;
				margin-left: 36px;
				margin-right: 0px;
				display: flex;
				flex-direction: column;
				.stepIndex {
					width: 100%;
					margin-top: 24px;
					.note {
						display: flex;
						.note_title {
							font-family: Open Sans;
							@include font14;
							font-weight: 600;
							letter-spacing: 0em;
							color: #19191a;
							margin-top: 0px;
							margin-left: 0px;
						}
						.desc_margin {
							margin-left: 4px;
						}
					}
					p {
						margin-top: 2px;
						&:nth-of-type(1) {
							margin-top: 12px;
						}
					}
					.image {
						width: 66%;
						height: auto;
						margin-top: 24px;
						border-radius: 4px;
					}
				}
			}
			.buy_offline {
				margin-top: 32px;
				margin-left: 36px;
				display: flex;
				flex-direction: column;
				.stepIndex {
					width: 100%;
					margin-top: 24px;
					p {
						margin-top: 12px;
						width: 100%;
					}
				}
			}
		}
	}
}
@include pad {
	.how_to_buy {
		.content {
			width: 94vw;
			.left {
				min-width: 232px;
				.left_box {
					top: 64px;
				}
			}
		}
	}
}

@include mobile {
	.how_to_buy {
		.banner {
			height: 233px;
		}
		.content {
			width: 100vw;
			flex-direction: column;
			margin-top: 16px;
			.left {
				display: none;
			}
			.right {
				width: 100%;
				.ask {
					margin-left: 16px;
					width: calc(100% - 32px);
					.ask_left {
						flex-direction: column;
						margin-top: 12px;
						margin-left: 16px;
						margin-bottom: 12px;
						align-items: stretch;
						gap: 0px;
						.first {
							text-align: left;
						}
						.second {
							text-align: left;
							margin-top: 4pt;
						}
					}
					.ask_right {
						margin-top: 19px;
						margin-bottom: 19px;
						margin-right: 16px;
						img {
							width: 32px;
							height: 32px;
						}
					}
				}
				.buyer_guide {
					margin-top: 24px;
					margin-left: 16px;
					width: calc(100% - 32px);
				}
				.line {
					margin-top: 24px;
					margin-left: 16px;
					margin-right: 16px;
				}
				.buy_online {
					margin-top: 24px;
					margin-left: 16px;
					width: calc(100% - 32px);
					.stepIndex {
						margin-top: 20px;
						.image {
							width: 100%;
						}
					}
				}
				.buy_offline {
					margin-top: 24px;
					margin-left: 16px;
					width: calc(100% - 32px);
					.stepIndex {
						margin-top: 20px;
					}
				}
			}
		}
	}
}
