<template>
	<div class="how_to_buy">
		<div
			v-if="false"
			class="banner"
			:style="{
				backgroundImage: `url(${screenWidth <= 768 ? `${resData?.data.banner.img_m}` : screenWidth <= 1024 ? `${resData?.data.banner.img_pad}` : `${resData?.data.banner.img_pc}`})`
			}">
			<p>{{ resData?.data.banner.title }}</p>
		</div>
		<div v-if="false" class="content">
			<div class="left">
				<div class="left_box">
					<div v-for="(navItem, index) in resData?.data.nav" :key="index" class="navItem">
						<div class="category_title">
							<p class="step_title">{{ navItem.title }}</p>
						</div>
						<div v-for="(linkItem, linkIndex) in navItem.links" :key="linkIndex" class="linkItem">
							<a :href="linkItem.url" class="step_desc"> {{ linkItem.title }}</a>
						</div>
					</div>
				</div>
			</div>
			<div class="right">
				<div
					class="ask"
					:style="{
						backgroundImage: `url(${screenWidth <= 768 ? `${resData?.data.banner.btn.img_m}` : screenWidth <= 1024 ? `${resData?.data.banner.btn.img_pad}` : `${resData?.data.banner.btn.img_pc}`})`
					}">
					<div class="ask_left">
						<p class="first">{{ resData?.data.banner.ask.first }}</p>
						<p class="second">{{ resData?.data.banner.ask.second }}</p>
					</div>
					<div class="ask_right" @click="openLinks(resData?.data.banner.btn.url)">
						<img :src="resData?.data.banner.btn.img" :alt="resData?.data.banner.btn.title" />
						<span v-html="resData?.data.banner.btn.title"></span>
						<i class="iconfont iconfs_2020091149icon" />
					</div>
				</div>
				<div class="buyer_guide">
					<h3 class="big_title">{{ resData?.data.content.howToBy.title }}</h3>
					<p v-for="(item, index) in resData?.data.content.howToBy.desc" :key="index" class="step_desc">{{ item }}</p>
				</div>
				<div class="line"></div>
				<div class="buy_online">
					<h3 class="big_title">{{ resData?.data.content.byOnline.title }}</h3>
					<div v-for="(item, index) in resData?.data.content.byOnline.steps" :key="index" class="stepIndex">
						<template v-if="Array.isArray(item.desc) && item.desc.length == 1">
							<div class="note">
								<p class="note_title" v-html="item.title"></p>
								<p class="step_desc desc_margin" v-html="item.desc[0]"></p>
							</div>
						</template>
						<template v-else>
							<h4 class="step_title" v-html="item.title"></h4>
							<template v-if="Array.isArray(item.desc)">
								<p v-for="(str, stepIndex) in item.desc" :key="stepIndex" class="step_desc" v-html="str"></p>
							</template>
							<template v-else>
								<p class="step_desc" v-html="item.desc"></p>
							</template>
						</template>
						<img v-if="item.img" class="image" :src="item.img" alt="image" />
						<img v-if="item.img1" class="image" :src="item.img1" alt="image" />
						<img v-if="item.img2" class="image" :src="item.img2" alt="image" />
					</div>
				</div>
				<div class="line"></div>
				<div class="buy_offline">
					<h3 class="big_title">{{ resData?.data.content.byOffline.title }}</h3>
					<div v-for="(item, index) in resData?.data.content.byOffline.steps" :key="index" class="stepIndex">
						<h4 class="step_title" v-html="item.title"></h4>
						<p class="step_desc" v-html="item.desc"></p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { dataInfo } from "./types";
definePageMeta({
	layout: "common"
});
const useDevice = useDeviceStore();

const { screenWidth } = storeToRefs(useDevice);

const resData = ref<dataInfo | null>(null);

const getData = async () => {
	const { data } = await useRequest.get("/api/specific/index/165");
	resData.value = data.value;
	// console.log("resData666===" + JSON.stringify(data.value));
};

getData();

function openLinks(str: string | undefined) {
	window.open(str, "_blank");
}
onMounted(() => {
	setTimeout(() => {
		useRequest.get("/api/specific/index/165");
	}, 1000);
});
</script>

<style lang="scss" scoped>
@import url("./HowToBuy.scss");
</style>
