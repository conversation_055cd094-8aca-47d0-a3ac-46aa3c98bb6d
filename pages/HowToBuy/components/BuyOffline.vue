<template>
	<div class="buy_offline_page">
		<div class="bottom">
			<div v-for="(item, index) in contentData" :key="index" class="bottom_item">
				<h3 class="tit">{{ item.tit }}</h3>
				<p v-if="item.tit1" class="tit1" v-html="item.tit1"></p>
				<ul v-if="item.des && item.des.length" class="list">
					<li v-for="(d_item, d_index) in item.des" :key="d_index" v-html="d_item"></li>
				</ul>
				<div v-if="item.overview" class="tit2_box">
					<p v-for="(d_item, d_index) in item.overview" :key="d_index" class="tit2" v-html="d_item"></p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { contentData } = props;
</script>

<style scoped lang="scss">
.buy_offline_page {
	.top {
		padding: 32px 0;
		> h3 {
			@include font20;
			margin-bottom: 12px;
		}
		> div {
			p {
				@include font14;
				color: $textColor2;
			}
		}
	}
	.line {
		width: 100%;
		height: 1px;
		background: #dee0e3;
	}
	.bottom {
		.bottom_item {
			padding: 32px 0;
			border-bottom: 1px solid #dee0e3;
			&:last-child {
				border-bottom: none;
			}
			.tit {
				@include font16;
				margin-bottom: 12px;
			}
			.tit1 {
				@include font14;
				color: $textColor1;
				margin-bottom: 8px;
			}
			.tit2_box {
				margin-top: 12px;
				display: flex;
				flex-direction: column;
				gap: 4px;
				.tit2 {
					@include font14;
					color: $textColor2;
				}
			}

			.list {
				display: flex;
				flex-direction: column;
				gap: 4px;
				> li {
					padding-left: 12px;
					@include font14;
					color: $textColor2;
					position: relative;
					&::before {
						content: "";
						position: absolute;
						left: 0;
						top: 9px;
						width: 4px;
						height: 4px;
						border-radius: 50%;
						background: $textColor2;
					}
				}
			}
			.img {
				padding: 24px 0;
				display: flex;
				flex-direction: column;
				gap: 24px;
				img {
					display: block;
					border-radius: 4px;
					max-width: 580px;
				}
			}
		}
	}
}
@include pad {
}
@include mobile {
}
</style>
