<template>
	<div class="buy_online_page">
		<div class="top">
			<h3>{{ contentData.overviewDes }}</h3>
			<div>
				<p v-for="(item, index) in contentData.overview" :key="index" v-html="item"></p>
			</div>
		</div>
		<div class="line"></div>
		<div class="bottom">
			<div class="bottom_item bottom_item_step1">
				<h3 class="tit">{{ contentData.step1.tit }}</h3>
				<p class="tit1">{{ contentData.step1.imgsHead.tit1 }}</p>
				<ul class="list">
					<li v-for="(item, index) in contentData.step1.imgsHead.des" :key="index" v-html="item"></li>
				</ul>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step1.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step1.imgs" :key="index" :src="item" alt="" /> -->
				</div>
				<p v-for="(item, index) in contentData.step1.imgsFoot.tit1" :key="index" class="tit2" v-html="item"></p>
			</div>
			<div class="line"></div>

			<div class="bottom_item bottom_item_step2">
				<h3 class="tit">{{ contentData.step2.tit }}</h3>
				<p v-for="(item, index) in contentData.step2.imgsHead.tit2" :key="index" class="tit2" v-html="item"></p>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step2.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step2.imgs" :key="index" :src="item" alt="" /> -->
				</div>
			</div>
			<div class="line"></div>

			<div class="bottom_item bottom_item_step3">
				<h3 class="tit">{{ contentData.step3.tit }}</h3>
				<p v-for="(item, index) in contentData.step3.imgsHead.tit2" :key="index" class="tit2" v-html="item"></p>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step3.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step3.imgs" :key="index" :src="item" alt="" /> -->
				</div>
				<h4 class="list_tit">{{ contentData.step3.imgsFoot.tit }}</h4>
				<ul class="list">
					<li v-for="(item, index) in contentData.step3.imgsFoot.des" :key="index" v-html="item"></li>
				</ul>
			</div>
			<div class="line"></div>

			<div class="bottom_item bottom_item_step4">
				<h3 class="tit">{{ contentData.step4.tit }}</h3>
				<p v-for="(item, index) in contentData.step4.imgsHead.tit2" :key="index" class="tit2" v-html="item"></p>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step4.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step4.imgs" :key="index" :src="item" alt="" /> -->
				</div>
				<div class="note">
					<span>{{ contentData.step4.note.tit }}</span>
					<div>
						<p v-for="(item, index) in contentData.step4.note.des" :key="index" v-html="item"></p>
					</div>
				</div>
				<div class="note2">
					<span>{{ contentData.step4.checkOut.tit }}</span>
					<div>
						<p v-for="(item, index) in contentData.step4.checkOut.des" :key="index">
							<i>{{ `${index + 1}. ` }}</i>
							<span v-html="item"></span>
						</p>
					</div>
				</div>
				<div class="note2">
					<span>{{ contentData.step4.moreTips.tit }}</span>
					<div>
						<p v-for="(item, index) in contentData.step4.moreTips.des" :key="index">
							<i>{{ `${index + 1}. ` }}</i>
							<span v-html="item"></span>
						</p>
					</div>
				</div>
			</div>
			<div class="line"></div>

			<div class="bottom_item bottom_item_step5">
				<h3 class="tit">{{ contentData.step5.tit }}</h3>
				<p v-for="(item, index) in contentData.step5.imgsHead.tit2" :key="index" class="tit2" v-html="item"></p>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step5.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step5.imgs" :key="index" :src="item" alt="" /> -->
				</div>
			</div>
			<div class="line"></div>

			<div class="bottom_item bottom_item_step6">
				<h3 class="tit">{{ contentData.step6.tit }}</h3>
				<p v-for="(item, index) in contentData.step6.imgsHead.tit2" :key="index" class="tit2" v-html="item"></p>
				<div class="img">
					<PhotoGallery v-for="(item, index) in contentData.step6.imgs" :key="index" :images="[item]" />
					<!-- <img v-for="(item, index) in contentData.step6.imgs" :key="index" :src="item" alt="" /> -->
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import PhotoGallery from "@/component/PhotoGallery/PhotoGallery.vue";

const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { contentData } = props;
</script>

<style scoped lang="scss">
.buy_online_page {
	.top {
		padding: 32px 0;
		> h3 {
			@include font20;
			margin-bottom: 12px;
		}
		> div {
			p {
				@include font14;
				color: $textColor2;
			}
		}
	}
	.line {
		width: 100%;
		height: 1px;
		background: #dee0e3;
	}
	.bottom {
		.bottom_item {
			padding: 32px 0;
			&.bottom_item_step1,
			&.bottom_item_step2 {
				.img {
					:deep(.photo_gallery_box) {
						&:nth-child(2) {
							border-radius: 5px;
						}
					}
				}
			}
			&.bottom_item_step4 {
				.img {
					:deep(.photo_gallery_box) {
						border-radius: 5px;
					}
				}
			}
			> div {
				&:last-child {
					margin-bottom: 0;
				}
			}
			.tit {
				@include font16;
				margin-bottom: 12px;
			}
			.tit1 {
				@include font14;
				color: $textColor1;
				margin-bottom: 8px;
			}
			.tit2 {
				@include font14;
				color: $textColor2;
			}
			.list_tit {
				@include font14;
				color: $textColor1;
				margin-bottom: 8px;
			}
			.list {
				display: flex;
				flex-direction: column;
				gap: 4px;
				> li {
					padding-left: 12px;
					@include font14;
					color: $textColor2;
					position: relative;
					&::before {
						content: "";
						position: absolute;
						left: 0;
						top: 9px;
						width: 4px;
						height: 4px;
						border-radius: 50%;
						background: $textColor2;
					}
				}
			}
			.img {
				margin: 24px 0;
				display: flex;
				flex-direction: column;
				gap: 24px;
				:deep(.photo_gallery_box) {
					border-radius: 4px;
					overflow: hidden;
					max-width: 580px;
					@include mobile {
						max-width: 100%;
					}
				}
				img {
					display: block;
					border-radius: 4px;
					max-width: 580px;
				}
			}
			.note {
				margin-top: 12px;
				display: flex;
				gap: 4px;
				span {
					@include font14;
					color: $textColor1;
					flex-shrink: 0;
				}
				div {
					p {
						@include font14;
						color: $textColor2;
					}
				}
			}
			.note2 {
				margin-top: 12px;
				display: flex;
				flex-direction: column;
				gap: 8px;
				> span {
					@include font14;
					color: $textColor1;
					font-weight: 600;
				}
				div {
					p {
						@include font14;
						color: $textColor2;
						display: flex;
						gap: 4px;
						i {
							font-style: normal;
						}
					}
				}
			}
		}
	}
}
@include pad {
}
@include mobile {
	.buy_online_page .bottom .bottom_item .img img {
		max-width: 100%;
	}
}
</style>
