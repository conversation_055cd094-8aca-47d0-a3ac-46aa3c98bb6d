<template>
	<div>
		<div class="banner">
			<div class="banner_txt">
				<div class="text">
					<h1 v-html="banner.title"></h1>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	banner: {
		type: Object,
		default() {
			return {};
		}
	}
});
const bannerUrlPc = `url(${props.banner.image})`;
const bannerUrlPad = `url(${props.banner.padImage})`;
const bannerUrlMobile = `url(${props.banner.mobileImage})`;
</script>

<style lang="scss" scoped>
.banner {
	width: 100%;
	height: 200px;
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	background-image: v-bind(bannerUrlPc);
	.banner_txt {
		@include contentWidth;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.text {
		color: $textColor1;
	}
	h1 {
		@include font32;
		font-weight: 600;
		color: #fff;
	}
	p {
		@include font16;
		max-width: 560px;
	}
}
@include pad {
	.banner {
		background-image: v-bind(bannerUrlPad);
	}
}
@include mobile {
	.banner {
		background-image: v-bind(bannerUrlMobile);
		height: 233px;
		// padding-top: 56px;
		.banner_txt {
			margin: 0 24px;
			width: calc(100% - 48px);
		}
		h1 {
			@include font32;
			max-width: unset;
		}
	}
}
</style>
