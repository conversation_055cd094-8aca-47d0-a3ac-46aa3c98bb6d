<template>
	<div class="contact_us_page">
		<h3>{{ contentData.tit }}</h3>
		<a :href="contentData.url">
			<FsButton type="black" plain tabindex="0">
				<span>{{ contentData.buttonDes }}</span>
				<i class="iconfont">&#xe747;</i>
			</FsButton>
		</a>
	</div>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";

const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { contentData } = props;
</script>

<style lang="scss" scoped>
.contact_us_page {
	height: 200px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 20px;
	@include bgCover("https://resource.fs.com/mall/generalImg/20221102153011kb9p33.jpg");
	h3 {
		@include font20;
	}
	:deep(.fs-button) {
		@include font14;
		> span {
			display: flex;
			align-items: center;
			gap: 4px;
		}
	}
}
@include pad {
	.contact_us_page {
		background-image: url(https://resource.fs.com/mall/generalImg/202211021530115u1bza.jpg);
	}
}
@include mobile {
	.contact_us_page {
		height: 190px;
		background-image: url(https://resource.fs.com/mall/generalImg/202211021530112casj5.jpg);
		padding: 16px;
		text-align: center;
	}
}
</style>
