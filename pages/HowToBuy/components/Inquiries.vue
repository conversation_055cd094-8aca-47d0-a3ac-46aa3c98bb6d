<template>
	<div class="inquiries_page">
		<div class="top">
			<h3>{{ contentData.overviewDes }}</h3>
			<div>
				<p v-for="(item, index) in contentData.overview" :key="index" class="tit2" v-html="item"></p>
			</div>
		</div>
		<div class="line"></div>

		<div class="bottom">
			<div class="card_content">
				<h3>{{ contentData.tit1 }}</h3>
				<p class="tit2" v-html="contentData.tit2"></p>
				<ul>
					<li v-for="(item, index) in detailsData.contactUs.content" :key="index" @click="handleClickContact(item)">
						<img :src="item.icon" alt="" />
						<div class="card_item">
							<h4>{{ item.title }}</h4>
							<p v-html="item.content"></p>
							<a href="javascript:;">
								<span>{{ item.href.text }}</span>
								<i class="iconfont">&#xe747;</i>
							</a>
						</div>
					</li>
				</ul>
			</div>
			<div class="form_content">
				<h3 class="tit">{{ detailsData.contactSales.title }}</h3>
				<p class="desc">{{ detailsData.contactSales.description }}</p>
				<div v-if="!isSuccess" class="form">
					<FsForm ref="formRef" :rules="rules" :model="model" layout="vertical">
						<FsFlex :gap="isMobile ? 0 : 16" :vertical="isMobile">
							<FsFormItem prop="entry_firstname" :label="`${localeLang('ContactSales.first_name')}`">
								<FsInput v-model="model.entry_firstname" newRegular />
							</FsFormItem>
							<FsFormItem prop="entry_lastname" :label="`${localeLang('ContactSales.last_name')}`">
								<FsInput v-model="model.entry_lastname" newRegular />
							</FsFormItem>
						</FsFlex>
						<template v-if="isCn">
							<FsFormItem prop="entry_telephone" :label="`${localeLang('ContactSales.phone_business')}`">
								<FsInput v-model="model.entry_telephone" newRegular />
							</FsFormItem>
						</template>
						<FsFormItem prop="email_address" :label="`${emailLabel}`">
							<FsInput v-model="model.email_address" newRegular />
						</FsFormItem>
						<FsFormItem prop="comments" class="comments_box" :label="`${localeLang('ContactSales.comment')} `">
							<FsInput v-model="model.comments" type="textarea" newRegular> </FsInput>
						</FsFormItem>
						<FsFormItem prop="policy">
							<div class="check_box">
								<FsCheckbox v-model="model.policy" size="small">
									<span class="check_box_text" v-html="policyText"></span>
								</FsCheckbox>
							</div>
						</FsFormItem>
					</FsForm>
					<div class="contact_sales">
						<FsButton id="contact_sales" type="red" :loading="submitLoading" @click.prevent="submit">
							{{ localeLang("ContactSales.submit") }}
						</FsButton>
					</div>
				</div>
				<div v-else class="success_box">
					<div class="success-logo iconfont">&#xe710;</div>
					<div class="success-tit">{{ localeLang("ContactSales.SubmittedSuccessfully") }}</div>
					<div class="success-des" v-html="subStr(localeLang('ContactSales.success.txt'))"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsButton, FsFlex, FsForm, FsFormItem, FsMessage, FsInput, FsCheckbox } from "fs-design";
import type { FormInstance } from "fs-design";
import { email_valdate, cn_all_phone } from "~/constants/validate";
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const route = useRoute();
const bdRequest = useBdRequest();

const { isLogin, userInfo } = useUserStore();
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
const websiteStroe = useWebsiteStore();
const { website, isCn, isSg } = toRefs(websiteStroe);
const formRef = ref<FormInstance>();
const isSuccess = ref(false);
const submitLoading = ref(false);
const model = ref({
	entry_firstname: "",
	entry_lastname: "",
	email_address: "",
	entry_telephone: "",
	company_name: "",
	comments: "",
	policy: false
});
const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	},
	detailsData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { contentData, detailsData } = props;
const defaultRule = {
	required: true,
	trigger: "blur",
	transform(value: string) {
		return value.trim();
	},
	message: localeLang("ContactSales.fieldRequired")
};
const rules: { [x: string]: any } = ref({
	entry_firstname: [{ ...defaultRule }],
	entry_lastname: [{ ...defaultRule }],
	// company_name: [{ ...defaultRule }],
	comments: [
		{
			...defaultRule,
			message: isCn.value ? "请填写您的需求。" : localeLang("ContactSales.fieldRequired")
		}
	],
	entry_telephone: [
		{
			validator: () => {
				const val = model.value.entry_telephone;
				let msg = "";
				if (!val) msg = localeLang("ContactSales.errors.entry_telephone_error");
				else {
					if (!cn_all_phone.test(val)) msg = localeLang("ContactSales.errors.entry_telephone_error01");
				}
				return msg;
			},
			trigger: "blur",
			required: true,
			isFunctionValidator: true
		}
	],
	email_address: [
		{
			validator: () => {
				const val = model.value.email_address;
				let msg = "";
				if (!val) msg = localeLang("ContactSales.errors.email_address_error");
				else {
					if (!email_valdate.test(val)) msg = localeLang("ContactSales.errors.email_address_error01");
				}
				return msg;
			},
			trigger: "blur",
			required: true,
			isFunctionValidator: true
		}
	],
	policy: [
		{
			require: true,
			message: localeLang("formValidate.form.errors.check2_error"),
			trigger: "change",
			validator: (_: any, val: boolean) => val == true
		}
	]
});
const { getRecaptchaToken } = useGrecaptcha();

const emailLabel = computed(() => {
	return `${localeLang("ContactSales.email_business")}${isCn.value ? localeLang("ContactSales.optional") : ""}`;
});
const submit = async () => {
	try {
		submitLoading.value = true;
		!isSuccess.value && (await formRef?.value?.validate());
		const { recaptchaTp = false, headers = {} } = await getRecaptchaToken();
		if (!recaptchaTp) {
			submitLoading.value = false;
			return;
		}
		const { data, error } = await useRequest.post("/api/contact_sales", {
			data: {
				...model.value
			},
			headers
		});
		submitLoading.value = false;
		if (data?.value?.code === 200) {
			if (data?.value?.status === "sensiWords" && website.value === "cn") {
				for (const key in data?.value?.errors) {
					Object.keys(rules.value).forEach(ele => {
						if (key === ele) {
							rules.value[key].push({
								validator: () => Promise.reject(localeLang("ContactSales.errors.sensiWords")),
								required: true
							});
						}
					});
					await formRef?.value?.validateField(key);
				}
			}
			isSuccess.value = true;
			bdRequest([
				{
					logidUrl: location.href,
					newType: 3
				}
			]);
		} else if (error.value && error.value.data) {
			const { errors, code } = error.value.data;
			if (code === 422 && errors) {
				if (errors.email_address) {
					formRef.value?.setFormItemErrorMsg("email_address", errors.email_address[0]);
					FsMessage({ message: errors.email_address[0] });
				}
			}
		} else {
			FsMessage({ message: data?.value?.message });
		}
	} catch (e: any) {
		submitLoading.value = false;
		// FsMessage({ message: e?.length > 0 ? e[0].message : " " });
	}
};
const policyText = computed(() => {
	if (isSg) {
		return localeLang("formValidate.form.aggree_policy").replace("AAAA", localeLink("/policies/privacy_policy.html")).replace("BBBB", localeLink("/policies/terms_of_use.html"));
	} else {
		return localeLang("formValidate.form.aggree_policy").replace("XXXX1", localeLink("/policies/privacy_policy.html")).replace("XXXX2", localeLink("/policies/terms_of_use.html"));
	}
});
const fsLiveChat = useLiveChat();
const handleClickContact = (item: any) => {
	const type = item.href.href;
	switch (type) {
		case "CHAT_US":
			window.open(`tel:${item.title}`, "_self");
			break;
		case "EMAIL_NOW":
			window.open(`mailto:<EMAIL>`, "_self");
			break;
		case "CHAT_NOW":
			fsLiveChat.showLiveChat();
			break;
		default:
			break;
	}
};
const subStr = (str: string) => {
	return str.replace("%XXXX%", `<a class="case_btn" href="${localeLink(`/support_ticket`)}">`).replace("%ZZZZ%", "</a>");
};
onMounted(() => {
	isSuccess.value = false;
	model.value = { entry_firstname: "", entry_lastname: "", email_address: "", entry_telephone: "", company_name: "", comments: "", policy: false };
});
</script>

<style scoped lang="scss">
.inquiries_page {
	.line {
		width: 100%;
		height: 1px;
		background: #dee0e3;
	}
	.top {
		display: flex;
		flex-direction: column;
		gap: 12px;
		padding: 32px 0;
		h3 {
			@include font20;
			font-weight: 600;
			color: $textColor1;
		}
		div {
			p {
				@include font14;
				color: $textColor2;
			}
		}
	}
	.bottom {
		display: flex;
		flex-direction: column;
		// gap: 12px;
		padding: 32px 0;
		h3 {
			@include font20;
			font-weight: 600;
			color: $textColor1;
		}
		.card_content {
			.tit2 {
				@include font14;
				color: $textColor1;
				margin-top: 12px;
			}
			ul {
				margin-top: 24px;
				margin-bottom: 32px;
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 12px;
				li {
					border-radius: 8px;
					border: 1px solid #dee0e3;
					transition: all 0.3s;
					padding: 20px 24px;
					display: flex;
					gap: 12px;
					cursor: pointer;
					img {
						width: 28px;
						height: 28px;
						display: block;
					}
					.card_item {
						display: flex;
						flex-direction: column;
						position: relative;
						h4 {
							@include font14;
							font-weight: 600;
							color: $textColor1;
						}
						p {
							margin-top: 4px;
							@include font12;
							color: $textColor2;
							padding-bottom: 32px;
						}
						a {
							position: absolute;
							bottom: 0;
							display: flex;
							gap: 4px;
							align-items: center;
							color: $textColor1;
							text-decoration: none;
							span {
								@include font13;
							}
							.iconfont {
								font-size: 12px;
								line-height: 1;
							}
						}
					}
					&:hover {
						box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
						.card_item {
							a {
								span {
									text-decoration: underline;
								}
							}
						}
					}
				}
			}
		}
		.form_content {
			.tit {
				@include font20;
				font-weight: 600;
				color: $textColor1;
			}
			.desc {
				@include font14;
				color: $textColor2;
				margin-top: 12px;
			}
			.form {
				margin-top: 24px;
				.contact_sales {
					text-align: left;
					padding-bottom: 0;
				}
			}
			.success_box {
				margin-top: 24px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				text-align: center;
				padding: 100px 36px;
				.success-logo {
					font-size: 50px;
					color: #329a34;
				}

				.success-tit {
					@include font16;
					font-weight: 600;
					margin: 16px 0 8px 0;
				}

				.success-des {
					padding: 0 84px;
					@include font14;
					color: $textColor3;
				}
			}
		}
	}
}
@include pad {
	.inquiries_page {
		.bottom {
			.form_content .success_box .success-des {
				padding: 0;
			}
		}
	}
}
@include mobile {
	.inquiries_page {
		.bottom {
			.card_content {
				ul {
					grid-template-columns: repeat(1, 1fr);
				}
			}
			.form_content .success_box {
				padding: 8px 0 0;
				.success-des {
					padding: 0;
				}
			}
		}
	}
}
</style>
