// import products from "./components/products";

const useMtpData = () => {
	const localeLang = useLocaleLang();

	const productsData = ref<any>({});

	const imagesData = ref<any>({});
	const attrsData = ref<any>({});

	const stepsData = ref<any[]>([]);

	const getData = async () => {
		const { data, error } = await useRequest.post("/api/getPdtData");
		if (data.value?.code == 200) {
			productsData.value = data.value?.data.fpc_products;
			imagesData.value = data.value?.data.fpc_img;
			attrsData.value = data.value?.data.mtp_products;
			initStepsData();
			console.log("getdata_getdata");
			console.log(Array.isArray(attrsData.value["31012-24F,F-F,Type A"]));
			console.log(attrsData.value["31012-24F,F-F,Type A"]);
		}
	};

	const modulesData = ref({
		title: `${localeLang("MTPTool.modulesTitle")}`,
		selected: "1",
		module_type: "modules_select",
		children: [
			{
				label: `${localeLang("MTPTool.modulesOption1")}`,
				value: "1",
				tip: `${localeLang("MTPTool.noticeGeneral")}`
			},
			{
				label: `${localeLang("MTPTool.modulesOption2")}`,
				tip: `${localeLang("MTPTool.noticeMTP")}`,
				value: "2"
			},
			{
				label: `${localeLang("MTPTool.modulesOption3")}`,
				tip: `${localeLang("MTPTool.noticeBreakout")}`,
				value: "3"
			}
		]
	});

	const initStepsData = () => {
		console.log("mtp_data_1111");
		// console.log(productsData.value)
		console.log(attrsData.value["30962-12F,M-M,,Type B"]);
		stepsData.value = [
			{
				title: `${localeLang("MTPTool.Step1")}${localeLang("MTPTool.cassetteStep1")}`,
				selected: "",
				showError: false,
				error: `${localeLang("MTPTool.cassetteStep1")}`,
				type: "radio",
				value: "1",
				module_type: "cassette",
				children: [
					{
						label: "24F",
						value: "24",
						type: "radio",
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						module_type: "core_number",
						children: [
							{
								label: "SM",
								value: "sm",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: productsData?.value["57023"]?.product_model || "",
										module_type: "product",
										productId: "57023",
										value: "57023",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "68549",
										mtp_line: "31012",
										lc_line: "40191",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "31012-24F,F-F,Type A",
										replace_mtp_line_attrs: "",
										result: [
											{
												id: "57023",
												mId: "68549",
												l: [
													{
														productId: "31012",
														attrs: "31012-24F,F-F,Type A"
													},
													{
														productId: "40191"
													}
												]
											}
										],
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["68549"]?.product_model || "",
										module_type: "product",
										productId: "68549",
										value: "68549",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "57023",
										mtp_line: "31012",
										lc_line: "40191",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "31012-24F,F-F,Type A",
										replace_mtp_line_attrs: "",
										result: [
											{
												id: "68549",
												mId: "57023",
												l: [
													{
														productId: "31012",
														attrs: "31012-24F,F-F,Type A"
													},
													{
														productId: "40191"
													}
												]
											}
										],
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									}
								]
							},
							{
								label: "MM",
								value: "mm",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										module_type: "product",
										productId: "57024",
										label: productsData?.value["57024"]?.product_model || "",
										value: "57024",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "68550",
										mtp_line: "31014",
										lc_line: "40180",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "31014-24F,F-F,Type A",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "68550",
										label: productsData?.value["68550"]?.product_model || "",
										value: "68550",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "57024",
										mtp_line: "31014",
										lc_line: "40180",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "31014-24F,F-F,Type A",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174250",
										label: productsData?.value["174250"]?.product_model || "",
										value: "174250",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "174251",
										mtp_line: "183675",
										lc_line: "69091",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "183675",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174251",
										label: productsData?.value["174251"]?.product_model || "",
										value: "174251",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "174250",
										mtp_line: "183675",
										lc_line: "69091",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "183675",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									}
								]
							}
						]
					},
					{
						label: "12F",
						value: "12",
						type: "radio",
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						module_type: "core_number",
						children: [
							{
								label: "SM",
								value: "sm",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: productsData?.value["57016"]?.product_model || "",
										module_type: "product",
										productId: "57016",
										value: "57016",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "57037",
										mtp_line: "30976",
										lc_line: "40191",
										replace_mtp_box: "57016",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["57037"]?.product_model || "",
										module_type: "product",
										productId: "57037",
										value: "57037",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "57016",
										mtp_line: "30976",
										lc_line: "40191",
										replace_mtp_box: "57037",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["57341"]?.product_model || "",
										module_type: "product",
										productId: "57341",
										value: "57341",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "68540",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "57341",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["68540"]?.product_model || "",
										module_type: "product",
										productId: "68540",
										value: "68540",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "57341",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "68540",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["105333"]?.product_model || "",
										module_type: "product",
										productId: "105333",
										value: "105333",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "105334",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "105333",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["105334"]?.product_model || "",
										module_type: "product",
										productId: "105334",
										value: "105334",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "105333",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "105334",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},

									{
										label: productsData?.value["238165"]?.product_model || "",
										module_type: "product",
										productId: "238165",
										value: "238165",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "238167",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "238165",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["238167"]?.product_model || "",
										module_type: "product",
										productId: "238167",
										value: "238167",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "238165",
										mtp_line: "30976",
										lc_line: "40191",

										replace_mtp_box: "238167",
										replace_mtp_line: "30976",
										replace_lc_line: "40191",
										mtp_line_attrs: "30976-12F,F-F,Type A",
										replace_mtp_line_attrs: "30976-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										label: productsData?.value["238169"]?.product_model || "",
										module_type: "product",
										productId: "238169",
										value: "238169",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "238169",
										mtp_line: "30976",
										lc_line: "40191",
										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "30976-12F,F-F,Type B",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									}
								]
							},
							{
								label: "MM",
								value: "mm",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										module_type: "product",
										productId: "57017",
										label: productsData?.value["57017"]?.product_model || "",
										value: "57017",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										mtp_loss: 0.35,
										lc_loss: 0.2,
										mtp_box: "57038",
										mtp_line: "30962",
										lc_line: "40180",
										replace_mtp_box: "57017",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "57038",
										label: productsData?.value["57038"]?.product_model || "",
										value: "57038",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "57017",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "57038",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "57342",
										label: productsData?.value["57342"]?.product_model || "",
										value: "57342",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "68541",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "57342",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "68541",
										label: productsData?.value["68541"]?.product_model || "",
										value: "68541",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "57342",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "68541",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",

										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "105335",
										label: productsData?.value["105335"]?.product_model || "",
										value: "105335",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "105336",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "105335",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "105336",
										label: productsData?.value["105336"]?.product_model || "",
										value: "105336",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "105335",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "105336",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",

										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},

									{
										module_type: "product",
										productId: "238171",
										label: productsData?.value["238171"]?.product_model || "",
										value: "238171",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "238173",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "238171",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "238173",
										label: productsData?.value["238173"]?.product_model || "",
										value: "238173",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "238171",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "238173",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174246",
										label: productsData?.value["174246"]?.product_model || "",
										value: "174246",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "174247",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "174246",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174247",
										label: productsData?.value["174247"]?.product_model || "",
										value: "174247",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "174246",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "174247",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174248",
										label: productsData?.value["174248"]?.product_model || "",
										value: "174248",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "174249",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "174248",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "174249",
										label: productsData?.value["174249"]?.product_model || "",
										value: "174249",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "174248",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: "174249",
										replace_mtp_line: "30962",
										replace_lc_line: "40180",
										mtp_line_attrs: "30962-12F,F-F,Type A",
										replace_mtp_line_attrs: "30962-12F,F-F,Type C",

										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									},
									{
										module_type: "product",
										productId: "238175",
										label: productsData?.value["238175"]?.product_model || "",
										value: "238175",
										type: "input",
										showError: false,
										error: `${localeLang("MTPTool.Enter_a_Length")}`,
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",

										mtp_loss: 0.35,
										lc_loss: 0.2,

										mtp_box: "238175",
										mtp_line: "30962",
										lc_line: "40180",

										replace_mtp_box: 0,
										replace_mtp_line: 0,
										replace_lc_line: 0,
										mtp_line_attrs: "30962-12F,F-F,Type B",
										replace_mtp_line_attrs: "",
										children: [
											{
												suffix: "M",
												module_type: "length"
											}
										]
									}
								]
							}
						]
					},
					{
						label: "8F",
						value: "8",
						type: "radio",
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						module_type: "core_number",
						children: [
							{
								label: "SM",
								value: "sm",
								type: "radio",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: `${localeLang("MTPTool.cassetteConnOption1")}`,
										value: "Direct Connection",
										type: "select",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep5")}`,
										selected: "",
										showError: false,
										error: `${localeLang("MTPTool.cassetteStep5")}`,
										module_type: "connection_method",
										children: [
											{
												label: productsData?.value["68401"]?.product_model || "",
												module_type: "product",
												productId: "68401",
												value: "68401",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												selected: "",

												mtp_loss: 0.35,
												lc_loss: 0.2,
												mtp_box: "68401",
												mtp_line: "30976",
												lc_line: "40191",
												replace_mtp_box: "68401",
												replace_mtp_line: "30976",
												replace_lc_line: "40191",
												mtp_line_attrs: "30976-12F,F-F,Type B",
												replace_mtp_line_attrs: "30976-8F,F-F,Type B",
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									},
									{
										label: `${localeLang("MTPTool.cassetteConnOption2")}`,
										value: "Breakout Connection",
										type: "select",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep5")}`,
										selected: "",
										showError: false,
										module_type: "connection_method",
										error: `${localeLang("MTPTool.cassetteStep5")}`,
										children: [
											{
												label: productsData?.value["68401"]?.product_model || "",
												module_type: "product",
												productId: "68401",
												value: "68401",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												selected: "",
												mtp_loss: 0.35,
												lc_loss: 0.2,
												mtp_box: "68402",
												mtp_line: "30962",
												lc_line: "40180",
												replace_mtp_box: "68402",
												replace_mtp_line: "30962",
												replace_lc_line: "40180",
												mtp_line_attrs: "30976-12F,F-F,Type B",
												replace_mtp_line_attrs: "30976-8F,F-F,Type B",
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									}
								]
							},
							{
								label: "MM",
								value: "mm",
								type: "radio",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: `${localeLang("MTPTool.cassetteConnOption1")}`,
										value: "Direct Connection",
										type: "select",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep5")}`,
										selected: "",
										showError: false,
										error: `${localeLang("MTPTool.cassetteStep5")}`,
										children: [
											{
												label: productsData?.value["68402"]?.product_model || "",
												module_type: "product",
												productId: "68402",
												value: "68402",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												selected: "",
												mtp_loss: 0.35,
												lc_loss: 0.2,
												mtp_box: "68402",
												mtp_line: "30976",
												lc_line: "40191",
												replace_mtp_box: "68402",
												replace_mtp_line: "30962",
												replace_lc_line: "40180",
												mtp_line_attrs: "30976-12F,F-F,Type B",
												replace_mtp_line_attrs: "30976-8F,F-F,Type B",

												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									},
									{
										label: `${localeLang("MTPTool.cassetteConnOption2")}`,
										value: "Breakout Connection",
										type: "select",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep5")}`,
										selected: "",
										showError: false,
										error: `${localeLang("MTPTool.cassetteStep5")}`,
										children: [
											{
												label: productsData?.value["68402"]?.product_model || "",
												module_type: "product",
												productId: "68402",
												value: "68402",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												selected: "",
												mtp_loss: 0.35,
												lc_loss: 0.2,
												mtp_box: "68402",
												mtp_line: "30976",
												lc_line: "40191",
												replace_mtp_box: "68402",
												replace_mtp_line: "30976",
												replace_lc_line: "40180",
												mtp_line_attrs: "30962-12F,M-F,Type B",
												replace_mtp_line_attrs: "30962-12F,M-F,Type B",
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									}
								]
							}
						]
					}
				]
			},
			{
				title: `${localeLang("MTPTool.Step1")}${localeLang("MTPTool.adapterStep1")}`,
				selected: "",
				type: "radio",
				module_type: "cable",
				showError: false,
				error: `${localeLang("MTPTool.adapterStep1")}`,
				value: "2",
				children: [
					{
						label: `${localeLang("MTPTool.adapterOption1")}`,
						type: "radio",
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						value: "up-up",
						module_type: "panel_model",
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						children: [
							{
								label: "SM - FHD-FAP12MTPHA",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								value: "sm",
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: productsData?.value["35510"]?.product_model || "",
										module_type: "product",
										showError: false,
										error: `${localeLang("MTPTool.adapterStep3")}`,
										productId: "35510",
										value: "35510",
										type: "radio",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.adapterStep3")}`,
										selected: "",
										children: [
											{
												label: "1",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.cassetteStep4")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "1",
												selected: "",
												solution1: [
													{
														productId: "30976",
														attrs: "30976-12F,F-F,Type A"
													},
													{
														productId: "30976",
														attrs: "30976-12F,M-F,Type B"
													}
												],
												solution2: [],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											},
											{
												label: "2",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.cassetteStep4")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "2",
												selected: "",
												solution1: [
													{
														productId: "30976",
														attrs: "30976-12F,F-F,Type B"
													},
													{
														productId: "30976",
														attrs: "30976-12F,M-M,,Type B"
													},
													{
														productId: "30976",
														attrs: "30976-12F,F-F,Type B"
													}
												],
												solution2: [
													{
														productId: "30976",
														attrs: "30976-12F,F-F,Type B"
													},
													{
														productId: "30976",
														attrs: "30976-12F,M-M,,Type A"
													},
													{
														productId: "30976",
														attrs: "30976-12F,F-F,Type A"
													}
												],
												children: [
													{
														suffix: "M",
														module_type: "length",
														value: "1"
													}
												]
											}
										]
									}
								]
							},
							{
								label: "MM - FHD-FAP12MTPHA",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep3")}`,
								value: "mm",
								selected: "",
								module_type: "transmission_mode",
								showError: false,
								error: `${localeLang("MTPTool.cassetteStep3")}`,
								children: [
									{
										label: productsData?.value["35510"]?.product_model || "",
										module_type: "product",
										productId: "35510",
										value: "35510",
										type: "radio",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.adapterStep3")}`,
										selected: "",
										children: [
											{
												label: "1",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "1",
												selected: "",
												solution1: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type A"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type B"
													}
												],
												solution2: [],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											},
											{
												label: "2",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "2",
												selected: "",
												solution1: [
													{
														productId: 30926,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30926,
														attrs: "30962-12F,M-M,,Type B"
													},
													{
														productId: 30926,
														attrs: "30962-12F,F-F,Type B"
													}
												],
												solution2: [
													{
														productId: 30926,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30926,
														attrs: "30962-12F,M-M,,Type A"
													},
													{
														productId: 30926,
														attrs: "30962-12F,F-F,Type B"
													}
												],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									},
									{
										label: productsData?.value["52022"]?.product_model || "",
										module_type: "product",
										productId: "52022",
										value: "52022",
										type: "radio",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										children: [
											{
												label: "1",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "1",
												selected: "",
												solution1: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type B"
													}
												],
												solution2: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type A"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type A"
													}
												],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											},
											{
												label: "2",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "2",
												selected: "",
												solution1: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type B"
													}
												],
												solution2: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type A"
													},
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type B"
													}
												],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									}
								]
							}
						]
					},
					{
						label: `${localeLang("MTPTool.adapterOption2")}`,
						type: "radio",
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						value: "up-down",
						module_type: "panel_model",
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						children: [
							{
								label: "MM - FHD-FAP12MTPHA",
								type: "select",
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.adapterStep3")}`,
								value: "mm",
								selected: "",
								showError: false,
								error: `${localeLang("MTPTool.adapterStep3")}`,
								module_type: "transmission_mode",
								children: [
									{
										label: productsData?.value["35510"]?.product_model || "",
										module_type: "product",
										productId: "35510",
										value: "35510",
										type: "radio",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										children: [
											{
												label: "1",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step5")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "1",
												selected: "",
												solution1: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type A"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type B"
													}
												],
												solution2: [],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									},
									{
										label: productsData?.value["52022"]?.product_model || "",
										module_type: "product",
										productId: "52022",
										value: "52022",
										type: "radio",
										title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
										selected: "",
										children: [
											{
												label: "1",
												type: "input",
												showError: false,
												error: `${localeLang("MTPTool.Enter_a_Length")}`,
												title: `${localeLang("MTPTool.Step4")}${localeLang("MTPTool.cassetteStep4")}`,
												value: "1",
												selected: "",
												solution1: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type B"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type B"
													}
												],
												solution2: [
													{
														productId: 30962,
														attrs: "30962-12F,F-F,Type A"
													},
													{
														productId: 30962,
														attrs: "30962-12F,M-F,Type A"
													}
												],
												children: [
													{
														suffix: "M",
														module_type: "length"
													}
												]
											}
										]
									}
								]
							}
						]
					}
				]
			},
			{
				title: `${localeLang("MTPTool.Step1")}${localeLang("MTPTool.breakoutStep1")}`,
				selected: "",
				type: "radio",
				value: "3",
				showError: false,
				module_type: "breakout",
				error: `${localeLang("MTPTool.breakoutStep1")}`,
				children: [
					{
						label: `${localeLang("MTPTool.breakoutOption1")}`,
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						value: "400",
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						type: "radio",
						module_type: "transmission_type",
						children: [
							{
								label: "SM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "sm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: 68401,
									productsList: [
										{
											productId: 30976,
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: 40191,
											attrs: ""
										}
									]
								},
								solution2: {
									productId: 74298,
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							},
							{
								label: "MM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "mm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: "68402",
									productsList: [
										{
											productId: "30962",
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: "40180",
											attrs: ""
										}
									]
								},
								solution2: {
									productId: "74297",
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							}
						]
					},
					{
						label: `${localeLang("MTPTool.breakoutOption2")}`,
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						value: "100",
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						type: "radio",
						module_type: "transmission_type",
						children: [
							{
								label: "SM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "sm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: 68401,
									productsList: [
										{
											productId: 30976,
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: 40191,
											attrs: ""
										}
									]
								},
								solution2: {
									productId: 74298,
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							},
							{
								label: "MM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "mm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: 68402,
									productsList: [
										{
											productId: 30962,
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: 40180,
											attrs: ""
										}
									]
								},
								solution2: {
									productId: 74297,
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							}
						]
					},
					{
						label: `${localeLang("MTPTool.breakoutOption3")}`,
						title: `${localeLang("MTPTool.Step2")}${localeLang("MTPTool.cassetteStep2")}`,
						value: "40",
						selected: "",
						showError: false,
						error: `${localeLang("MTPTool.cassetteStep2")}`,
						type: "radio",
						module_type: "transmission_type",
						children: [
							{
								label: "SM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "sm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: 68401,
									productsList: [
										{
											productId: 30976,
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: 40191,
											attrs: ""
										}
									]
								},
								solution2: {
									productId: 74298,
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							},
							{
								label: "MM",
								type: "input",
								showError: false,
								error: `${localeLang("MTPTool.Enter_a_Length")}`,
								title: `${localeLang("MTPTool.Step3")}${localeLang("MTPTool.cassetteStep4")}`,
								value: "mm",
								selected: "",
								module_type: "transmission_mode",
								solution1: {
									productId: 68402,
									productsList: [
										{
											productId: 30962,
											attrs: "30976-12F,F-F,Type B"
										},
										{
											productId: 40180,
											attrs: ""
										}
									]
								},
								solution2: {
									productId: 74297,
									productsList: []
								},
								children: [
									{
										suffix: "M",
										module_type: "length"
									}
								]
							}
						]
					}
				]
			}
		];

		// 递归为每一层级添加唯一id（id从1开始，不带root）
		function addUniqueIds(arr: any[], parentId: string = "") {
			arr.forEach((item: any, idx: number) => {
				const currentId = parentId ? `${parentId}-${idx + 1}` : `${idx + 1}`;
				item.id = currentId;
				if (item.children && Array.isArray(item.children)) {
					addUniqueIds(item.children, currentId);
				}
			});
		}
		addUniqueIds(stepsData.value);
	};

	// onMounted(() => {
	// 	console.log("mtp_data_1111")
	// 	console.log(productsData.value)
	// 	console.log(attrsData.value["31012-24F,F-F,Type A"])
	// });
	return { getData, productsData, imagesData, attrsData, modulesData, stepsData };
};

export default useMtpData;
