<template>
	<div class="fs-list" :class="{ no_result: !contentData.records.length }">
		<div class="common">
			<ul v-if="contentData?.records?.length">
				<li v-for="(item, index) in contentData.records" :key="index">
					<a
						:href="
							localeLink(
								`/media/${item.videoName.replaceAll(' | FS', '').replaceAll(',', '-').replaceAll('&', '-').replaceAll('/', '-').replaceAll(' ', '-').replaceAll('--', '-').toLowerCase()}-${item.videoId}.html${hasCate ? '?categoryName=true' : ''}`
							)
						"
						target="_blank">
						<div class="top" :style="{ backgroundImage: `url(${item.videoCover})` }">
							<FsButton type="video" size="small">{{ item.videoTime }}</FsButton>

							<!-- <div class="top"> -->
							<!-- <img :src="item.videoCover" :alt="item.title" /> -->
						</div>
						<div class="bottom">
							<h3 class="tit">{{ item.videoName }}</h3>
							<!-- <p class="desc">{{ item.videoDescription }}</p> -->
							<div class="tag">
								<div>
									<span class="line"></span> <span>{{ `${item.views} ${localeLang("media.views")}` }}</span>
								</div>
								<div>
									<span class="line"></span> <span>{{ item.updatedTime }}</span>
								</div>
							</div>
						</div>
					</a>
				</li>
			</ul>
			<p v-else class="no_result_p">{{ localeLang("media.NoResult") }}</p>
			<div v-if="contentData.records.length" class="category__pagination">
				<FsPagination v-bind="{ ...pageConfig }" @change="changePage" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsButton, FsPagination, FsTag } from "fs-design";
const route = useRoute();
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();

const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	}
});

const pageConfig = ref();

watch(
	() => props.contentData,
	() => {
		pageConfig.value = {
			pageSize: props.contentData.pageSize,
			current: route.query.current ? Number(route.query.current) : 1,
			total: props.contentData.total
		};
		console.log("pageConfig.value", pageConfig.value);
	},
	{ immediate: true, deep: true }
);
const changePage = (n: number) => {
	if (n === pageConfig.value.current) return;
	return navigateTo({ query: { ...route.query, current: n } });
};

const hasCate = ref(false);
watch(
	() => route.fullPath,
	() => {
		if (route.query && route.query.cId && route.query.tagId) {
			hasCate.value = true;
		} else {
			hasCate.value = false;
		}
	},
	{ immediate: true }
);

onMounted(() => {
	console.log("contentDatacontentData", pageConfig.value);
});
</script>

<style scoped lang="scss">
.fs-list {
	padding: 20px 0 64px;
	@include pad {
		padding-bottom: 40px;
	}
	@include mobile {
		padding-bottom: 40px;
	}
	&.no_result {
		padding-bottom: 40px;
	}
	img {
		display: block;
	}
	.common {
		@include contentWidth;
		> ul {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 20px;
			padding-bottom: 20px;
			> li {
				display: flex;
				> a {
					flex: 1;
					display: flex;
					flex-direction: column;
					text-decoration: none;
					transition: 0.3s all;
					.top {
						padding-top: calc(min(56.2%, 160px));
						@include mobile {
							padding-top: 216px;
						}
						overflow: hidden;
						border-radius: 8px 8px 0 0;
						background-size: cover;
						background-repeat: no-repeat;
						background-position: center;
						position: relative;
						.fs-button {
							position: absolute;
							z-index: 1;
							right: 8px;
							bottom: 8px;
							width: 63px;
						}
						img {
							width: 100%;
							display: block;
						}
					}
					.bottom {
						flex: 1;
						border: 1px solid $borderColor1;
						border-top: none;
						border-radius: 0 0 8px 8px;
						padding: 20px;

						.tit {
							color: $textColor1;
							@include font14;
							font-weight: 600;
							margin-bottom: 4px;
							@include txt-more-hid;
							height: 44px;
						}
						.desc {
							color: $textColor2;
							@include font12;
							font-weight: 400;
							@include txt-more-hid;
							// margin-bottom: 4px;
						}
						.tag {
							color: $textColor2;
							display: flex;
							align-items: center;
							flex-flow: wrap;
							@include font12;
							max-height: 20px;
							overflow: hidden;
							margin-top: 12px;
							> div {
								display: flex;
								align-items: center;
								&:first-child {
									.line {
										display: none;
									}
								}
							}
							.line {
								margin: 0 8px;
								width: 1px;
								height: 10px;
								background-color: $borderColor1;
							}
						}
					}
					&:hover {
						box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
						.bottom .tit {
							text-decoration: underline;
						}
					}
				}
			}
		}
		.no_result_p {
			text-align: center;
			margin-top: 40px;
			color: $textColor1;
			@include font12;
		}
	}
}
@include pad {
	.fs-list .common > ul {
		grid-template-columns: repeat(3, 1fr);
	}
}
@include mobile {
	.fs-list {
		padding-bottom: 40px;
		.common > ul {
			grid-template-columns: repeat(1, 1fr);
		}
	}
}
</style>
