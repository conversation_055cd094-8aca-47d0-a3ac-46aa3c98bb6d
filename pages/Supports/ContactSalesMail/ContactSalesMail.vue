<template>
	<div>
		<div class="banner">
			<p class="banner-text">{{ localeLang("contactSales.SalesSupport") }}</p>
		</div>
		<main class="main">
			<section v-if="isSuccessStatus" class="success wrap">
				<div><i class="icon iconfont">&#xe710;</i></div>
				<h2>{{ localeLang("contactSales.SubmittedSuccessfully") }}</h2>
				<p v-html="subStr(localeLang('formValidate.form.success.txt1'))"></p>
				<a type="gray" :href="localeLink('/')" @click="toHomeGio">{{ localeLang("contactSales.ReturnHomepage") }}</a>
			</section>
			<div v-else class="content_wrap">
				<aside class="pc_left">
					<span class="call_txt">
						<span>{{ localeLang("contactSales.callTxt") }}</span>
						<span v-if="!isLogin" class="no-pad" v-html="localeLang('contactSales.noLoginTips').replace('XXX', localeLink(`/login.html?redirect=${fullPath}`))"></span>
					</span>
					<ContactAside></ContactAside>
				</aside>
				<!-- <aside class="pc_left">
					<p>{{ localeLang("contactSales.callTxt") }}</p>
					<p
						v-if="!isLogin"
						class="no-pad"
						v-html="
							localeLang('contactSales.noLoginTips').replace('XXX', localeLink(`/login.html?redirect=${fullPath}`))
						"></p>
				</aside> -->
				<div class="pc_right">
					<h3>{{ localeLang("contactSales.LetUs") }}</h3>
					<p @click="buriedLink($event)"><span v-html="localeLang('contactSales.LetUsTxt')"></span></p>
					<contact-form @success="handleSuccess"></contact-form>
				</div>
			</div>
		</main>
	</div>
</template>

<script lang="ts" setup>
import ContactAside from "./components/ContactAside.vue";
import ContactForm from "./components/ContactForm.vue";
const { isShowMore, isLogin, managerInfo } = storeToRefs(useUserStore());
definePageMeta({
	layout: "common"
});
const { fullPath } = useRoute();
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const route = useRoute();
const setMeta = useSetMeta();

const isSuccessStatus = ref<boolean>(false);

function toHomeGio() {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Send A Message Page",
			eventAction: "home_entrance",
			eventLabel: "Return to homepage",
			nonInteraction: false
		});
}
function subStr(str: string) {
	return str.replace("%XXXX%", `<a class="case_btn" href="${localeLink(`/support_ticket`)}">`).replace("%ZZZZ%", "</a>");
}

function handleSuccess() {
	isSuccessStatus.value = true;
}
function buriedLink(e: Event) {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Send A Message Page",
			eventAction: "Contact_Sales",
			eventLabel: "WhatsApp Clicks",
			nonInteraction: false
		});
}

const getMetaData = async () => {
	const { data, error } = await useRequest.post(`/api/metaTag/getMetaTag`, {
		data: {
			meta_type: 7,
			link: "contact_sales_mail"
		}
	});
	if (data.value) {
		setMeta(data?.value?.data);
	}
};

await getMetaData();
useHeadMeta();
</script>

<style lang="scss" scoped>
.banner {
	width: 100%;
	height: 200px;
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: 50%;
	background-image: url(https://img-en.fs.com/images/single/fs_form/contact_sales_mail/banner-pc.jpg);
	.banner-text {
		@include font32;
		max-width: 750px;
		position: absolute;
		color: #fff;
		font-weight: 600;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
}
.main {
	background-color: white;
	.content_wrap {
		padding: 36px 0;
		margin: 0 auto;
		display: flex;
		column-gap: 36px;
		justify-content: center;
		max-width: 900px;
		width: 94%;
		flex-direction: column;
	}
	.pc_left {
		h3 {
			@include font16;
			font-weight: 400;
			margin-bottom: 28px;
		}
		> p {
			color: #19191a;
			@include font14;
			padding-bottom: 12px;
		}
	}
	.call_txt {
		span {
			color: #19191a;
			@include font14;
		}
	}
	.pc_right {
		flex: 1;
		background-color: #fff;
		h3 {
			margin-bottom: 4px;
			@include font16;
			font-weight: 600;
		}
		> p {
			@include font14;
			color: $textColor2;
		}
	}
}
.wrap {
	max-width: 1200px;
	width: 94%;
	margin: 0 auto;
}
.success {
	padding: 160px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	.icon {
		font-size: 50px;
		width: 50px;
		height: 50px;
		line-height: 50px;
		display: block;
		color: #10a300;
	}
	h2 {
		@include font24;
		margin-bottom: 8px;
		margin-top: 16px;
	}
	p {
		@include font14;
		color: $textColor3;
		margin-bottom: 32px;
	}
}

@media (max-width: 1024px) and (min-width: 768px) {
	.banner {
		background-image: url(https://img-en.fs.com/images/single/fs_form/contact_sales_mail/banner-pad.jpg);
	}
}
@media (max-width: 960px) {
	.success {
		padding: 120px 16px;
		h2 {
			@include font20;
		}
	}
}
@media (max-width: 768px) {
	.banner {
		background-image: url(https://img-en.fs.com/images/single/fs_form/contact_sales_mail/banner-m.jpg);
		height: 233px;
		.banner-text {
			@include font24;
		}
	}
	.main {
		padding-bottom: 20px;
		.pc_left {
			padding: 0 16px;
			margin-top: 8px;
			flex: 0 0 auto;
		}
		.content_wrap {
			padding: 12px 0;
			flex-direction: column-reverse;
			background-color: #fff;
			width: 100%;
		}
		.pc_right {
			padding: 0 16px;
			padding-bottom: 24px;
		}
	}
	.success {
		padding: 36px 16px;
		h2 {
			@include font16;
		}
	}
}
</style>
