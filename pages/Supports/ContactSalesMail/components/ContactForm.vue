<template>
	<fs-form ref="formRef" :rules="rules" :model="formData" @keydown.enter.capture="enterSubmit">
		<fs-form-item v-if="!isCn" :label="localeLang('formValidate.form.first_name')" class="inline_item mr12" prop="first_name">
			<fs-input v-model="formData.first_name" newRegular></fs-input>
		</fs-form-item>
		<fs-form-item :label="localeLang('formValidate.form.last_name')" class="inline_item" prop="last_name" :class="{ mr12: isCn }">
			<fs-input v-model="formData.last_name" newRegular></fs-input>
		</fs-form-item>
		<fs-form-item v-if="isCn" :label="localeLang('formValidate.form.first_name')" class="inline_item" prop="first_name">
			<fs-input v-model="formData.first_name" newRegular></fs-input>
		</fs-form-item>
		<fs-form-item v-if="isCn" :label="localeLang('formValidate.form.phone_business')" prop="phone">
			<fs-input v-model="formData.phone" newRegular />
		</fs-form-item>
		<fs-form-item :label="localeLang('contactSales.BusinessEmail')" prop="business_email" class="inline_item">
			<fs-input v-model="formData.business_email" newRegular />
		</fs-form-item>
		<fs-form-item :label="localeLang('formValidate.form.comment')" prop="comments">
			<fs-input v-model="formData.comments" type="textarea" :maxlength="5000" :placeholder="localeLang('formValidate.form.whatProducts')" newRegular></fs-input>
		</fs-form-item>
		<fs-form-item v-if="isSg" prop="reviews_newImg" class="upload_file">
			<fs-upload v-model="formData.reviews_newImg" multiple :limit="5" accept=".pdf,image/jpeg,image/jpg,image/png">
				<template #tip>
					<fs-tooltip placement="right">
						<span class="iconfont iconicon_48px_infohebing"></span>
						<template #content>
							<p>{{ localeLang("formValidate.form.allow_files_of_type") }}</p>
							<p>{{ localeLang("formValidate.form.maximum_size_5M") }}</p>
						</template>
					</fs-tooltip>
				</template>
			</fs-upload>
		</fs-form-item>
		<!-- <fs-form-item prop="policy">
			<fs-checkbox v-model="formData.policy" class="check_box">
				<span v-html="policyText"></span>
			</fs-checkbox>
		</fs-form-item> -->
		<!-- <fs-form-item v-else>
			<p class="policy_tips" @click.stop="clickLink($event)" v-html="policyText"></p>
		</fs-form-item> -->
		<fs-form-item prop="policy">
			<div class="agreement">
				<FsCheckbox v-model="formData.policy" size="small" type="checkbox" class="chk">
					<div class="agreement_wrap" v-html="policyText"></div>
				</FsCheckbox>
			</div>
		</fs-form-item>
	</fs-form>
	<fs-button type="red" class="submit_btn" :loading="loading" @click="handleSubmit">{{ localeLang("contactSales.Submit") }}</fs-button>
</template>

<script lang="ts" setup>
import { FsForm, FsFormItem, FsInput, FsButton, FsTooltip, FsCheckbox, FsUpload, FsFlex } from "fs-design";
import type { FormDateType, FormRulesType } from "../types";
import { onEnterSubmit } from "@/utils/utils";
import UseValidate from "@/composables/useValidate";

const localeLang = useLocaleLang();
const { website, isCn, countries_id, isSg } = useWebsiteStore();
const { isLogin } = storeToRefs(useUserStore());

const { cn_all_phone } = UseValidate();

const emits = defineEmits(["success"]);

const rules = reactive<FormRulesType>({
	first_name: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.entry_firstname_error"),
			trigger: "blur"
		}
	],
	last_name: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.entry_lastname_error"),
			trigger: "blur"
		}
	],
	business_email: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.email_business_error"),
			trigger: "blur"
		},
		{
			type: "email",
			message: localeLang("formValidate.form.errors.email_business_error01"),
			trigger: "blur"
		}
	],
	comments: [
		{
			required: true,
			message: localeLang("contactSales.fieldRequired"),
			trigger: "blur"
		}
	],
	policy: [
		{
			required: true,
			trigger: "change",
			message: "This field is required.",
			validator: (_: any, val: boolean) => val === true
		}
	]
});

const loading = ref<boolean>(false);

watch(
	() => website,
	val => {
		if (website === "sg") {
			rules.policy = [
				{
					require: true,
					message: localeLang("formValidate.form.errors.check2_error"),
					trigger: "change",
					validator: (_: any, val: boolean) => val == true
				}
			];
		} else if (website === "cn") {
			rules.phone = [
				{
					validator: telPhoneValidate,
					isFunctionValidator: true,
					trigger: "blur"
				}
			];
			rules.business_email = [
				{
					required: false,
					message: localeLang("formValidate.form.errors.email_business_error"),
					trigger: "blur"
				},
				{
					type: "email",
					message: localeLang("formValidate.form.errors.email_business_error01"),
					trigger: "blur"
				}
			];
		}
	},
	{
		immediate: true
	}
);
const localeLink = useLocaleLink();

const policyText = computed(() => {
	return `I agree to FS's <a href='AAAA' target="_blank">Privacy Notice</a> and <a href='BBBB' target="_blank">Terms of Use</a>.`
		.replace("AAAA", localeLink("/policies/privacy_policy.html"))
		.replace("BBBB", localeLink("/policies/terms_of_use.html"));
});

const formRef = ref<any>();

const formData = ref<FormDateType>({
	first_name: "",
	last_name: "",
	business_email: "",
	comments: "",
	phone: "",
	reviews_newImg: [],
	policy: false
});

const userStore = useUserStore();

/**
 * cn站点电话号码验证
 */
function telPhoneValidate() {
	const value = formData.value.phone;
	// 判断电话号码是否为空
	if (!(value && value.replace(/^\s+|\s+$/g, ""))) {
		console.log(1);
		return localeLang("formValidate.form.errors.entry_telephone_error");
	} else {
		if (!isCn) {
			return "";
		} else {
			if (!cn_all_phone.test(value)) {
				return localeLang("formValidate.form.errors.entry_telephone_error01");
			} else {
				if (isLogin.value) {
					return "";
				}
				return new Promise((resolve, reject) => {
					useRequest
						.post("/api/user/isHasRegister", { customers_name: value })
						.then(({ data }: any) => {
							const { is_has } = data.value?.data || {};
							if (is_has) {
								const resultMessage = `账户已存在，单击此处<a href='${localeLink("/login.html")}' title='登录'>登录</a>。`;
								resolve(resultMessage);
							} else {
								resolve("");
							}
						})
						.catch((err: any) => {
							console.log(err);
							resolve(err);
						});
				});
			}
		}
	}
}
const { getRecaptchaToken } = useGrecaptcha();

const handleSubmit = () => {
	formRef.value?.validate().then(async (val: any) => {
		if (val) {
			loading.value = true;
			const { recaptchaTp = false, headers = {} } = await getRecaptchaToken();
			if (!recaptchaTp) {
				loading.value = false;
				return;
			}
			const data = new FormData();
			data.append("entry_firstname", formData.value.first_name);
			data.append("entry_lastname", formData.value.last_name);
			if (formData.value.business_email) {
				data.append("email_address", formData.value.business_email);
			}
			if (formData.value.phone) {
				data.append("phone", formData.value.phone);
			}
			if (formData.value.reviews_newImg.length && isSg) {
				for (let i = 0; i < formData.value.reviews_newImg.length; i++) {
					data.append("reviews_newImg[]", formData.value.reviews_newImg[i]);
				}
			}
			data.append("comments", formData.value.comments);
			data.append("resource_page", "3");
			const { data: resData, error } = await useRequest.post("/api/contact_sales", { data, headers });
			console.log(resData.value, "1111111111");
			if (resData.value && resData.value.code === 200) {
				emits("success");
			}
			if (error.value && error.value.data) {
				const { errors, code } = error.value.data;
				if (code === 422 && errors) {
					if (errors.entry_firstname) {
						formRef.value?.setFormItemErrorMsg("first_name", errors.entry_firstname[0]);
					}
					if (errors.entry_lastname) {
						formRef.value?.setFormItemErrorMsg("last_name", errors.entry_lastname[0]);
					}
				}
			}
			loading.value = false;
		}
	});
};

const enterSubmit = (e: { isComposing: any; keyCode: number; shiftKey: any; ctrlKey: any; altKey: any; metaKey: any; target: { tagName: any }; preventDefault: () => void }) => {
	onEnterSubmit(e, handleSubmit);
};

watch(
	() => userStore.userInfo,
	val => {
		if (val) {
			const { customers_firstname = "", customers_lastname = "", customers_email_address = "" } = val;
			formData.value.first_name = customers_firstname;
			formData.value.last_name = customers_lastname;
			formData.value.business_email = customers_email_address;
		}
	},
	{
		immediate: true,
		deep: true
	}
);

function clickLink(e: any) {
	if (!isSg) return;
	console.log(e.target.href, "e.target.href");
	if (e.target && e.target.href && e.target.href.includes("privacy_policy")) {
		window.dataLayer &&
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory: "Send A Message Page",
				eventAction: "contact_sales",
				eventLabel: "Privacy Notice",
				nonInteraction: false
			});
	} else {
		window.dataLayer &&
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory: "Send A Message Page",
				eventAction: "contact_sales",
				eventLabel: "Terms of Use",
				nonInteraction: false
			});
	}
}
</script>

<style lang="scss" scoped>
.submit_tip {
	@include font12;
	padding-top: 16px;
	color: #707070;
}
.fs-form {
	margin-top: 12px;
}
.inline_item {
	width: calc(50% - 6px);
	display: inline-block;
	&.mr12 {
		margin-right: 12px;
	}
}
.upload_file {
	&:deep(.fs-form__item--content) {
		flex-direction: column;
		align-items: flex-start;
	}
}
.policy_box {
	width: 100%;
}
.policy_tips {
	@include font12;
	padding-top: 16px;
	color: #707070;
}
.check_box {
	margin-top: 32px;
}
.agreement {
	display: flex;
	align-items: center;
}
.agreement_wrap {
	@include font12;
	color: $textColor2;
}
:deep(.fs-textarea__wrapper) {
	@include font13;
}
:deep(.fs-form__item--label) {
	width: max-content;
}
:deep(.fs-tooltip) {
	margin-left: 12px;
}
@media (max-width: 768px) {
	.inline_item {
		width: 100%;
	}
	.submit_btn {
		width: 100%;
	}
}
</style>
