<template>
	<!-- 联系我们的方式 -->
	<ul class="contact_method">
		<li v-for="(item, index) in contactMethodInfo" :key="index" :style="{ ...(item.style || {}) }" @click="handleAsideItemClick">
			<span class="iconfont" :class="item.icon"></span> <span :class="{ contact_method_font: index === 1 }" v-html="item.content"></span>
		</li>
		<li v-if="isLogin && isShowMore">
			<i class="iconfont iconfs_2023102513icon"></i>
			<div class="more-row">
				<p>{{ managerInfo.admin_name }}</p>
				<p>
					<a :href="`mailto:${managerInfo.admin_email}`">{{ managerInfo.admin_email }}</a>
				</p>
			</div>
		</li>
	</ul>
</template>

<script lang="ts" setup>
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();

interface ContactMethodsType {
	icon: string;
	content: string;
	style?: Object;
}
const { isShowMore, isLogin, managerInfo } = storeToRefs(useUserStore());

const { website, countries_id, isJp, isJpEn } = useWebsiteStore();

const contactMethodInfo = computed(() => {
	const { phone: web_phone } = useHeaderStore();
	const prefixPhone = (website: string, countries_id: number, phone: string) => {
		if (website === "ru") {
			return `<span>Позвоните нам</span><a href="tel:${phone}">${phone}</a>`;
		} else if (countries_id === 107) {
			return `<a href="tel:${phone}">${website === "jp" ? "電話番号:" : "TEL:"} ${phone}</a>`;
		} else {
			return `<a href="tel:${phone}">${phone}</a>`;
		}
	};
	let iconArr: ContactMethodsType[] = [
		{
			icon: "icona-lianxiwomen_Callus",
			content: prefixPhone(website, countries_id, web_phone)
		}
	];
	let supportTime = localeLang("contactUs.time");
	if (website === "sg") {
		supportTime = `Mon - Fri<span class="separator"></span>24h Phone Support`;
	} else if (isJpEn) {
		supportTime = "Monday to Friday 10:00 – 17:00 (GMT+9)";
	}
	if (website === "jp" || countries_id === 107) {
		iconArr.push({
			icon: "iconchuanzhen_Fax1",
			content: `<a href="javascript:;">FAX: 03-5763-5625</a>`,
			style: { paddingBottom: "4px" }
		});
	}
	iconArr.push({
		icon: "iconfs_2023103001icon",
		content: supportTime
	});
	if (website === "sg") {
		iconArr = iconArr.concat([
			{
				icon: "iconfs_2023110801",
				content: `<span>Chat with sales via <a href="https://wa.me/6564437951" target="_blank" class='whatsApp'>WhatsApp</a>.</span>`,
				style: { paddingTop: "12px" }
			},
			{
				icon: "iconfs_2023103001icon",
				content: `<span>Mon - Fri<span class="separator"></span>9: 00am - 6: 00pm SGT</span>`,
				style: { paddingBottom: "12px" }
			}
		]);
	}
	return iconArr;
});

const gaEventHandel = ({ eventAction, eventCategory }: any) => {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Send A Message Page",
			eventAction: eventAction || "Contact_Sales",
			eventLabel: eventCategory || "WhatsApp Clicks",
			nonInteraction: false
		});
};

const handleAsideItemClick = ({ target }: any) => {
	try {
		target as any;
		if (target.nodeName === "A" && target.className === "whatsApp") {
			gaEventHandel({ eventAction: "Contact_Sales", eventCategory: "WhatsApp Clicks" });
		}
	} catch (error) {
		console.log(error, "err");
	}
};
</script>

<style lang="scss" scoped>
.contact_method {
	display: flex;
	flex-direction: column;
	row-gap: 4px;
	@include font14;
	margin-bottom: 16px;
	margin-top: 12px;
	li {
		display: flex;
		column-gap: 4px;
		margin-bottom: 4px;
		:deep(.separator) {
			display: inline-block;
			width: 1px;
			height: 10px;
			background-color: #ccc;
			margin: 0 8px;
		}
		&:last-of-type {
			margin-bottom: 0;
		}
	}
	&_font {
		color: #3d3d3d;
	}
}
</style>
