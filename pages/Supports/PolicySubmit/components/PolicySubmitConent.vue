<template>
	<div class="policy-form">
		<div class="title">
			<p>{{ localeLang("policiesSubmit.title") }}</p>
			<p>{{ localeLang("policiesSubmit.secondTitle") }}</p>
		</div>

		<div class="desc">
			<p>{{ localeLang("policiesSubmit.des.des01") }}</p>
			<p>{{ localeLang("policiesSubmit.des.des02") }}</p>
			<p>{{ localeLang("policiesSubmit.des.des03") }}</p>
		</div>

		<fs-form ref="formDateRef" class="form_box" :rules="rules" :model="formData">
			<!-- 1. Name and Contact Information -->
			<div class="step-form">
				<div class="form-title">{{ localeLang("policiesSubmit.nameInformation.title") }}</div>
				<div class="form-des" v-html="localeLang('policiesSubmit.nameInformation.des')"></div>
				<div class="form-content">
					<fs-form-item prop="name" :label="localeLang('policiesSubmit.nameInformation.name') + ' (' + localeLang('policiesSubmit.required') + ')'">
						<fs-input v-model="formData.name" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `name input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="address" :label="localeLang('policiesSubmit.nameInformation.address')" class="full-row">
						<fs-input v-model="formData.address" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `Address input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="phone" :label="localeLang('policiesSubmit.nameInformation.phone')">
						<fs-input v-model="formData.phone" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `Telephone number input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="email" :label="localeLang('policiesSubmit.nameInformation.email') + ' (' + localeLang('policiesSubmit.required') + ')'">
						<fs-input v-model="formData.email" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `Email address input`)"></fs-input>
					</fs-form-item>
				</div>
			</div>

			<!-- 2. Proof of Resident’s Identity -->
			<div class="step-form small">
				<div class="form-title">{{ localeLang("policiesSubmit.proofIdentity.title") }}</div>
				<div class="form-des bottom-des">{{ localeLang("policiesSubmit.proofIdentity.des") }}</div>
			</div>

			<!-- 3. Requests Made by an Authorized Agent on a Resident's Behalf -->
			<div class="step-form">
				<div class="form-title">{{ localeLang("policiesSubmit.requestsBehalf.title") }}</div>
				<div class="form-des">{{ localeLang("policiesSubmit.requestsBehalf.des") }}</div>
				<div class="form-content">
					<fs-form-item prop="agentName" :label="localeLang('policiesSubmit.requestsBehalf.name')">
						<fs-input v-model="formData.agentName" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', 'Agent name input')"></fs-input>
					</fs-form-item>
					<fs-form-item prop="agentAddress" :label="localeLang('policiesSubmit.nameInformation.address')" class="full-row">
						<fs-input v-model="formData.agentAddress" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `agentAddress input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="agentPhone" :label="localeLang('policiesSubmit.nameInformation.phone')">
						<fs-input v-model="formData.agentPhone" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `agentPhone input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="agentEmail" :label="localeLang('policiesSubmit.nameInformation.email')">
						<fs-input v-model="formData.agentEmail" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `agentEmail address input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="agentRelation" :label="localeLang('policiesSubmit.requestsBehalf.relationship')" class="full-row">
						<fs-input v-model="formData.agentRelation" :readonly="isReadonly" newRegular @focus="buriedPointWrapper('policy_submit', `agentRelation input`)"></fs-input>
					</fs-form-item>
					<fs-form-item prop="agentHasAuthority" :label="localeLang('policiesSubmit.requestsBehalf.personalinformation')" class="full-row">
						<FsRadioGroup v-model="formData.agentHasAuthority" :disabled="isReadonly">
							<FsRadio label="yes">{{ localeLang("searchFeedback.form.yes") }}</FsRadio>
							<FsRadio label="no">{{ localeLang("searchFeedback.form.no") }}</FsRadio>
						</FsRadioGroup>
					</fs-form-item>
				</div>
				<div class="form-des bottom-des">{{ localeLang("policiesSubmit.requestsBehalf.des01") }}</div>
			</div>

			<!-- 4. Resident Request -->
			<div class="step-form">
				<div class="form-title">{{ localeLang("policiesSubmit.residentRequest.title") }}</div>
				<div class="form-content">
					<fs-form-item prop="residentNameCountry" class="personal-info-row" :label="localeLang('policiesSubmit.residentRequest.countryTitle')">
						<fs-input v-model="formData.residentNameCountry" :readonly="isReadonly" newRegular></fs-input>
					</fs-form-item>
					<fs-form-item
						prop="requestType"
						:label="localeLang('policiesSubmit.residentRequest.selectCategories') + ' (' + localeLang('policiesSubmit.required') + ')'"
						class="full-row">
						<fs-checkbox-group v-model="formData.requestType" :disabled="isReadonly" class="checkbox-list" @change="handleChange">
							<FsCheckbox v-for="(item, index) in checkOption" :key="index" :model-value="item.value">{{ item.label }}</FsCheckbox>
						</fs-checkbox-group>
					</fs-form-item>
					<!-- 选中 Other 时显示输入框 -->
					<fs-form-item v-if="showRequestOther" prop="otherRequest" class="personal-info-row">
						<fs-input v-model="formData.otherRequest" :readonly="isReadonly" newRegular></fs-input>
					</fs-form-item>
					<fs-form-item prop="personalInformation" class="full-row personal-info-row input-textarea" :label="localeLang('policiesSubmit.residentRequest.personalInformationTitle')">
						<fs-input v-model="formData.personalInformation" :readonly="isReadonly" type="textarea" newRegular></fs-input>
					</fs-form-item>
				</div>
			</div>

			<div class="desc">
				<p>{{ localeLang("policiesSubmit.des.des04") }}</p>
				<p>
					{{ localeLang("policiesSubmit.des.des05").replace("legalXXXfs.com", "<EMAIL>") }}
				</p>
				<p>{{ localeLang("policiesSubmit.des.des06") }}</p>
				<p v-html="localeLang('policiesSubmit.des.des07').replace('XXXX', 'https://www.fs.com/policies/privacy_policy.html')"></p>
			</div>

			<fs-form-item prop="policy" class="full-row agree_box">
				<fs-checkbox v-model="formData.policy" :disabled="isReadonly" class="check_box">
					<span>{{ localeLang("policiesSubmit.policies") }}</span>
				</fs-checkbox>
			</fs-form-item>
			<FsButton v-if="!isReadonly && !route.query.id" type="red" :loading="loading" @click="handleSubmit">{{ localeLang("header.linkuser.btn.submit") }}</FsButton>
		</fs-form>
	</div>
</template>

<script lang="ts" setup>
import { FsForm, FsFormItem, FsRadioGroup, FsRadio, FsCheckboxGroup, FsCheckbox, FsInput, FsButton } from "fs-design";
import { ref } from "vue";
import aes from "@/utils/aes";
const isReadonly = ref(false);
const showRequestOther = computed(() => Array.isArray(formData.value.requestType) && formData.value.requestType.includes("3"));
const localeLang = useLocaleLang();
const loading = ref(false);
const formDateRef = ref();
const props = defineProps<{ onSuccess?: (data: any) => void }>();
const userStore = useUserStore();
const { isLogin } = storeToRefs(userStore);
const localeLink = useLocaleLink();
const route = useRoute();
const emit = defineEmits(["success"]);
interface PolicyFormData {
	name: string;
	address: string;
	phone: string;
	email: string;
	agentName: string;
	agentAddress: string;
	agentPhone: string;
	agentEmail: string;
	agentRelation: string;
	agentHasAuthority: string;
	residentNameCountry: string;
	requestType: string[];
	otherRequest: string;
	personalInformation: string;
	policy: boolean;
}
onMounted(async () => {
	await nextTick();
	const id = route.query.id;
	if (id) {
		await fetchFormData(id as string);
	}
});

const fetchFormData = async (id: string) => {
	try {
		const { data, error } = await useRequest.get(`/api/user/privacyPolicyDetail?id=${id}`);
		if (error.value) return;

		if (data.value && data.value.data && data.value.data.form) {
			const form = data.value.data.form;
			// 填充表单数据
			formData.value = {
				name: form.customers_name || "",
				address: form.customers_address || "",
				phone: form.customers_telephone || "",
				email: form.customers_email_address || "",
				agentName: form.agent_name || "",
				agentAddress: form.agent_address || "",
				agentPhone: form.agent_telephone || "",
				agentEmail: form.agent_email_address || "",
				agentRelation: form.agent_relationship || "",
				agentHasAuthority: form.has_authority === 1 ? "yes" : "no",
				residentNameCountry: form.resident_state || "",
				requestType: form.request_type ? form.request_type.split(",") : [],
				otherRequest: form.request_comment || "",
				personalInformation: form.comment || "",
				policy: form.has_check === 1
			};
			// 设置为只读模式
			isReadonly.value = true;
		}
	} catch (error) {
		console.error("Failed to fetch form data:", error);
	}
};

const formData = ref<PolicyFormData>({
	name: "",
	address: "",
	phone: "",
	email: "",
	agentName: "",
	agentAddress: "",
	agentPhone: "",
	agentEmail: "",
	agentRelation: "",
	agentHasAuthority: "",
	residentNameCountry: "",
	requestType: [],
	otherRequest: "",
	personalInformation: "",
	policy: false
});
const checkOption = [
	{
		label: localeLang("policiesSubmit.residentRequest.selectOption[0]"),
		value: "1"
	},
	{
		label: localeLang("policiesSubmit.residentRequest.selectOption[1]"),
		value: "2"
	},
	{
		label: localeLang("policiesSubmit.residentRequest.selectOption[2]"),
		value: "3"
	}
];
const rules = computed(() => {
	const result = {
		name: [
			{
				required: true,
				message: localeLang("policiesSubmit.enterName"),
				trigger: "blur"
			}
		],
		requestType: [
			{
				required: true,
				validator: (_: any, value: string[]) => value && value.length > 0,
				trigger: "change",
				message: "please select at least one request type"
			}
		],
		policy: [
			{
				required: true,
				validator: (_: any, value: boolean) => value === true,
				trigger: "change",
				message: localeLang("formValidate.form.errors.interest_type_error")
			}
		],
		email: [
			{
				required: true,
				message: localeLang("formValidate.form.errors.email_address_error"),
				trigger: "blur"
			},
			{
				type: "email",
				message: localeLang("formValidate.form.errors.email_address_error01"),
				trigger: "blur"
			}
		],
		agentEmail: [
			{
				type: "email",
				message: localeLang("formValidate.form.errors.email_address_error01"),
				trigger: "blur"
			}
		]
	};
	return result;
});
const handleChange = () => {
	// 判断是否选中了 "Other"
	if (!formData.value.requestType.includes("3")) {
		// 如果未选中 "Other"，清空输入框内容
		formData.value.otherRequest = "";
	}
	// 其它逻辑...
	console.log("item====", formData.value.requestType);
};

const handleSubmit = async () => {
	const val = await formDateRef.value?.validate();
	console.log("val====", formDateRef.value);
	if (val) {
		loading.value = true;
		const data = new FormData();
		data.append("customers_name", formData.value.name);
		data.append("customers_address", formData.value.address);
		data.append("customers_telephone", formData.value.phone);
		data.append("customers_email_address", formData.value.email);
		data.append("agent_name", formData.value.agentName);
		data.append("agent_address", formData.value.agentAddress);
		data.append("agent_telephone", formData.value.agentPhone);
		data.append("agent_email_address", formData.value.agentEmail);
		data.append("agent_relationship", formData.value.agentRelation);
		data.append("has_authority", formData.value.agentHasAuthority === "yes" ? "1" : "0");
		data.append("resident_state", formData.value.residentNameCountry);
		data.append("request_type", formData.value.requestType.join(","));
		data.append("request_comment", formData.value.otherRequest);
		data.append("comment", formData.value.personalInformation);
		data.append("has_check", formData.value.policy ? "1" : "0");
		data.append("isLogin", isLogin.value ? "1" : "0");
		for (const [key, value] of (data as any).entries()) {
			console.log(key, value);
		}

		if ((isLogin.value && formData.value.requestType.length === 1 && formData.value.requestType[0] === "1") || formData.value.agentPhone || formData.value.agentEmail) {
			// 已登录并且是数据提取类型
			const { data: resData, error } = await useRequest.post("/api/user/submit_privacy_policy", { data });
			if (resData.value && resData.value.code === 200) {
				console.log("resData====", resData);
				props.onSuccess?.(resData.value);
			}
		} else {
			// 将 FormData 转为普通对象
			const obj: Record<string, any> = {};
			for (const [key, value] of (data as any).entries()) {
				obj[key] = value;
			}
			// 使用AES加密压缩数据
			const jsonString = JSON.stringify(obj);
			const encryptedData = aes.encrypt(jsonString, `_-yu_xuan_3507-_`, `fs_com_phone2016`);
			// 拼接到新窗口的 URL
			location.href = localeLink(`/verify_identity.html?data=${encryptedData}`);
		}
		loading.value = false;
	}
};
const buriedPointWrapper = (eventAction: string, eventLabel: string) => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Form Page_Policy PolicySubmit Page",
			eventAction,
			eventLabel,
			nonInteraction: false
		});
	}
};
</script>

<style lang="scss" scoped>
.policy-form {
	max-width: 800px;
	margin: 40px auto;
	background: #fff;
	font-size: 15px;
	color: #19191a;
	@media (max-width: 768px) {
		margin: 36px auto;
	}
}
.policy-form .title {
	color: #19191a;
	font-size: 20px;
	font-weight: 600;
	p {
		margin-bottom: 4px;
	}
	margin-bottom: 12px;
}
.policy-form .desc {
	color: #707070;
	font-size: 14px;
	margin-bottom: 16px;
	line-height: 24px;
	p {
		margin-bottom: 8px;
	}
}

.step-form {
	margin-top: 16px;
	.form-content {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 0 12px;
		:deep(.fs-radio-box) {
			.fs-radio__icon {
				font-size: 14px !important;
			}
			.fs-radio__label {
				color: $textColor2;
				@include font13;
			}
		}
		:deep(.fs-form__item) {
			margin-bottom: 12px !important;
		}
		@media (max-width: 768px) {
			grid-template-columns: repeat(1, 1fr);
		}
		.checkbox-list {
			margin-top: 6px;
			gap: 8px;
			:deep(.fs-checkbox) {
				margin-right: 0;
				height: 20px;
				.fs-checkbox-box {
					align-items: center;
					.fs-checkbox__label {
						@include font13;
					}
					.fs-checkbox__icon {
						font-size: 14px !important;
					}
				}
			}
		}

		.full-row {
			grid-column: 1 / -1;
		}
	}
	.form-title {
		color: #19191a;
		@include font16;
		font-weight: 600;
		margin-bottom: 8px;
	}
	.form-des {
		color: #707070;
		@include font14;
		font-weight: 400;
		margin-bottom: 12px;
	}
	.bottom-des {
		margin-bottom: 16px;
	}
}
.small {
	margin-top: 4px;
}
.form_box {
	margin-top: 12px;
}
.form_box .personal-info-row :deep(.fs-form__item--label) {
	margin-bottom: 4px;
}
:deep(.fs-textarea__wrapper) {
	margin-top: 12px;
	resize: both !important;
}
:deep(.fs-textarea__number) {
	display: none;
}
.input-textarea {
	:deep(.fs-form__item--label) {
		font-size: 14px;
	}
}
.checkbox-list {
	display: flex;
	flex-direction: column;
}
.agree_box {
	display: flex;
	:deep(.fs-checkbox) {
		margin-right: 0;
		height: auto;
		.fs-checkbox-box {
			align-items: flex-start;
			.fs-checkbox__icon {
				margin-top: 4px;
				font-size: 14px !important;
			}
		}
	}
	span {
		padding-top: 1px;
		@include font12;
		color: $textColor2;
	}
}
.agreement-item {
	.agreement {
		width: 100%;
		display: flex;
		align-items: flex-start;
		input {
			margin: 0 8px 0 0;
			width: 12px;
			height: 12px;
			font-size: 14px;
			@media (max-width: 960px) {
				margin-top: 1px;
			}
		}
		> p {
			@include font12;
			color: $textColor2;
		}
		&:hover {
			cursor: pointer;
			input[type="checkbox"] {
				&:before {
					color: #707070;
				}
			}
		}
	}
}
</style>
