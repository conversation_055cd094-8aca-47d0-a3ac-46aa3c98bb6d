<template>
	<div class="picos_software_wrap">
		<!-- banner图 -->
		<div class="banner_div">
			<div class="banner">
				<div class="banner-text wrap">
					<h2 class="banner-title" v-html="banner.title"></h2>
				</div>
			</div>
		</div>
		<div v-show="isSuccessStatus" class="success wrap">
			<div><i class="icon iconfont">&#xe710;</i></div>
			<h2>Submitted Successfully</h2>
			<p v-html="subSuccessStr()"></p>
			<fs-button type="gray" @click="handleToHome">Return to Homepage</fs-button>
		</div>
		<!-- 内容表单 -->
		<main v-show="!isSuccessStatus" class="wrap">
			<div class="content">
				<div class="content_title">
					<span>
						Within 24 hours, technical experts will discuss technical solutions with you to significantly accelerate the completion time of your network solution.
						<span v-if="!isLogin">
							Already have an account?
							<span :href="localeLink(`/login.html?redirect=${route.fullPath}`)">Sign in</span> or<a :href="localeLink(`/regist.html?redirect=${route.fullPath}`)">
								Create an account
							</a>
						</span>
					</span>
				</div>
				<!-- 表单 -->
				<fs-form ref="formRef" :model="userForm" :rules="rules" @keydown.enter.capture="enterSubmit">
					<FsFormItem prop="firstName" label="First Name">
						<FsInput v-model="userForm.firstName" newRegular />
					</FsFormItem>
					<FsFormItem prop="lastName" label="Last Name">
						<FsInput v-model="userForm.lastName" newRegular />
					</FsFormItem>
					<FsFormItem prop="emailAddress" label="Email address">
						<FsInput v-model="userForm.emailAddress" newRegular />
					</FsFormItem>
					<FsFormItem prop="phoneNumber" label="Phone Number">
						<TelCode v-model:telPrefix="userForm.tel" v-model="userForm.phoneNumber" @change="telChange" />
					</FsFormItem>
					<FsFormItem prop="country" label="Country" :class="showState ? 'country_box' : 'flex1'">
						<FsSelect v-model="userForm.country" searchPlaceholder="Search" placeholder="Select Country" filterable :options="countryList" newRegular />
					</FsFormItem>
					<FsFormItem v-if="showState" prop="state" label="State/Province/Region" :class="showState ? 'country_box' : ''">
						<FsSelect v-model="userForm.state" placeholder="Select State/Province/Region" filterable searchPlaceholder="Search" :options="stateList" newRegular />
					</FsFormItem>
					<FsFormItem prop="indic" :class="showState ? 'flex1' : 'flex1'" label="Indicative budget">
						<FsInput v-model="userForm.indic" :placeholder="placeholder.indic" newRegular />
					</FsFormItem>
					<FsFormItem prop="currentSwitch" class="flex1" label="Current switch device configuration information">
						<FsInput v-model="userForm.currentSwitch" type="textarea" :placeholder="placeholder.currentSwitch" newRegular />
					</FsFormItem>
					<FsFormItem prop="describe" class="flex1" label="Please describe the features you would like to customize in PicOS.">
						<FsInput v-model="userForm.describe" type="textarea" :placeholder="placeholder.describe" newRegular />
					</FsFormItem>
					<FsFormItem class="flex1">
						<fs-upload text="Upload file" :multiple="true" :limit="5" :maxSize="5 * 1024 * 1024" accept=".pdf,image/jpeg,image/jpg,image/png" @change="fileChange">
							<template #tip>
								<fs-tooltip placement="right">
									<span class="iconfont iconicon_48px_infohebing" :style="{ color: '#707070' }"></span>
									<template #content>
										<p>Allow files of type PDF, JPG, PNG.</p>
										<p>Maximum size 5M.</p>
									</template>
								</fs-tooltip>
							</template>
						</fs-upload>
					</FsFormItem>
					<FsFormItem class="flex1" prop="policy">
						<div class="policy_box">
							<FsCheckbox v-model="userForm.policy" size="small">
								<span class="agree" v-html="policyText"></span>
							</FsCheckbox>
						</div>
					</FsFormItem>
					<FsFormItem>
						<fs-button type="red" :loading="loading" @click="submit">Submit</fs-button>
					</FsFormItem>
				</fs-form>
			</div>
		</main>
	</div>
</template>

<script lang="ts" setup>
import { FsForm, FsFormItem, FsInput, FsButton, FsUpload, FsCheckbox, FsSelect, FsTooltip } from "fs-design";
import type { SelectOptionType, StateItemType } from "./types";
import { onEnterSubmit } from "@/utils/utils";
import SelectCountry from "@/component/SelectCountry/SelectCountry.vue";
import TelCode from "@/component/TelCode/TelCode.vue";
definePageMeta({
	layout: "common"
});
const banner = ref({
	title: "PicOS® Software Customized"
});
const userStore = useUserStore();
const websiteStore = useWebsiteStore();

const { countries_id } = storeToRefs(websiteStore);

const { isLogin } = storeToRefs(userStore);

const route = useRoute();

const isSuccessStatus = ref(false);

const placeholder = {
	indic: "Enter budget (e.g., $10,000)",
	currentSwitch:
		"Please provide the brand and model of the switch you are currently using and the main configuration information such as network topology, port configuration, VLAN, and QoS configurations.",
	describe: "Describe more details about your request."
};
const countryStore = useCountryStore();

const countryList = computed(() => {
	return countryStore.countryList.map(item => {
		return {
			label: item.countries_name,
			value: item.countries_id,
			iso_code: item.iso_code
		};
	});
});

const stateList = computed(() => {
	if (userForm.value.country) {
		let code = "";
		countryStore.countryList.forEach(item => {
			if (item.countries_id === userForm.value.country) {
				code = item.iso_code;
			}
		});
		if (code && countryStore.stateList && countryStore.stateList[code]) {
			return countryStore.stateList[code].map((item: any) => {
				return {
					label: item.states,
					value: item.states_code
				};
			});
		}
	} else {
		return [];
	}
});

const userForm = ref({
	firstName: "",
	lastName: "",
	emailAddress: "",
	phoneNumber: "",
	tel: "+1",
	country: 0,
	state: "",
	indic: "",
	currentSwitch: "",
	describe: "",
	policy: false
});

const loading = ref(false);
const files = ref<File[]>();

const showState = computed(() => {
	// 美国223  加拿大38  墨西哥138  澳大利亚13  新西兰153  展示state选项
	const arr = [223, 38, 138, 13, 153];
	return arr.includes(userForm.value.country);
});

const validateCountry = () => {
	if (!userForm.value.country) {
		return "This field is required.";
	} else {
		return "";
	}
};

const validateState = () => {
	let errorStr = "";
	if (showState.value) {
		if (!userForm.value.state) {
			errorStr = "This field is required.";
		}
	}
	return errorStr;
};

const rules = ref({
	firstName: [
		{
			required: true,
			message: "This field is required.",
			trigger: "blur"
		}
	],
	lastName: [
		{
			required: true,
			message: "This field is required.",
			trigger: "blur"
		}
	],
	emailAddress: [
		{
			required: true,
			message: "This field is required.",
			trigger: "blur"
		},
		{
			type: "email",
			message: "Please enter a valid email address. (eg:<EMAIL>)",
			trigger: "blur"
		}
	],
	phoneNumber: [
		{
			required: true,
			message: "This field is required.",
			trigger: "blur"
		}
	],
	country: [
		{
			validator: validateCountry,
			isFunctionValidator: true,
			trigger: "change"
		}
	],
	state: [
		{
			validator: validateState,
			isFunctionValidator: true,
			trigger: "change"
		}
	],
	describe: [
		{
			required: true,
			type: "string",
			message: "This field is required.",
			trigger: "blur"
		}
	],
	policy: [
		{
			required: true,
			trigger: "change",
			message: "This field is required.",
			validator: (_: any, val: boolean) => val === true
		}
	]
});

const formRef = ref();

const localeLink = useLocaleLink();

const policyText = computed(() => {
	return `I agree to FS's <a href='AAAA' target="_blank">Privacy Notice</a> and <a href='BBBB' target="_blank">Terms of Use</a>.`
		.replace("AAAA", localeLink("/policies/privacy_policy.html"))
		.replace("BBBB", localeLink("/policies/terms_of_use.html"));
});
function subSuccessStr() {
	const str = 'We will reply you within 1 working day. You can also discussthe case with our team online, and trace updates in "<a href="AAAA">Cases</a>" with FS account.';
	return str.replace("AAAA", localeLink(`/support_ticket`));
}

/**
 * 提交case
 */

const submitForm = async () => {
	loading.value = true;
	const paramsData = new FormData();
	paramsData.append("first_name", userForm.value.firstName);
	paramsData.append("last_name", userForm.value.lastName);
	paramsData.append("business_email", userForm.value.emailAddress);
	paramsData.append("phone", userForm.value.phoneNumber);
	paramsData.append("country", userForm.value.country + "");
	paramsData.append("comments", userForm.value.describe);
	paramsData.append("indicative_budget", userForm.value.indic);
	paramsData.append("device_config_info", userForm.value.currentSwitch);

	if (userForm.value.state) {
		paramsData.append("state", userForm.value.state);
	}
	if (files.value && files.value.length) {
		files.value.forEach(file => {
			paramsData.append("software_customized_file[]", file);
		});
	}
	const { data, error } = await useRequest.post("/api/softwareCustomized", { data: paramsData });
	loading.value = false;
	if (data?.value?.code === 200 && data?.value?.data?.case_number) {
		isSuccessStatus.value = true;
	}
};

const enterSubmit = (e: { isComposing: any; keyCode: number; shiftKey: any; ctrlKey: any; altKey: any; metaKey: any; target: { tagName: any }; preventDefault: () => void }) => {
	onEnterSubmit(e, submit);
};

const submit = () => {
	formRef.value?.validate().then((val: boolean) => {
		if (val) {
			submitForm();
		}
	});
};

const handleToHome = () => {
	location.href = localeLink("/");
};

const fileChange = val => {
	files.value = val.files;
};

const telChange = (val: any) => {
	const { error } = val;
	if (error) {
		formRef.value.setFormItemErrorMsg("phoneNumber", error);
	}
};

countryStore.getCountryData();
countryStore.getState();

watch(
	() => userStore.userInfo,
	val => {
		if (val) {
			const { customers_firstname = "", customers_email_address = "", customers_lastname = "", customers_telephone = "", customer_country_id = 0, customer_state = 0 } = val;
			userForm.value.firstName = customers_firstname;
			userForm.value.lastName = customers_lastname;
			userForm.value.emailAddress = customers_email_address;
			userForm.value.phoneNumber = customers_telephone;
			userForm.value.country = customer_country_id;
			userForm.value.state = customer_state;
		}
	},
	{
		immediate: true
	}
);
const handleCheckPolicy = e => {
	if (e.target && e.target.nodeName === "A") {
		userForm.value.policy = true;
		formRef.value?.validateField("policy");
	}
};
</script>

<style lang="scss" scoped>
.banner_div {
	background-color: $bgColor1;
	.banner {
		width: 100%;
		height: 200px;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		background-image: url("https://resource.fs.com/mall/generalImg/20240528100359upsbdb.jpg");
		.banner-text {
			color: #ffffff;
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 100%;
			text-align: center;
			.banner-title {
				@include font32;
				font-weight: 600;
				// margin-bottom: 16px;
			}
			.banner-desc {
				@include font16;
			}
			.banner-title,
			.banner-desc {
				max-width: 750px;
				margin-left: auto;
				margin-right: auto;
			}
		}
		@media (max-width: 1024px) {
			background-image: url("https://resource.fs.com/mall/generalImg/20240528100329mpjbqa.jpg");
		}
		@media (max-width: 768px) {
			background-image: url("https://resource.fs.com/mall/generalImg/202405281003541glnua.jpg");
		}
	}
}
.wrap {
	max-width: 1200px;
	width: 94%;
	margin: 0 auto;
}
.content {
	max-width: 900px;
	margin: 0 auto;
	padding: 40px 0;
	.content_title {
		display: flex;
		@include font14;
	}
}
.success {
	padding: 160px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	.icon {
		font-size: 50px;
		width: 50px;
		height: 50px;
		line-height: 50px;
		display: block;
		color: #10a300;
	}
	h2 {
		@include font24;
		margin-bottom: 8px;
		margin-top: 16px;
	}
	p {
		@include font14;
		color: $textColor3;
		margin-bottom: 32px;
	}
}
.agree {
	color: #707070;
}
.fs-form {
	margin-top: 12px;
	display: flex;
	flex-wrap: wrap;
	column-gap: 12px;
	// row-gap: 16px;
	.fs-form__item {
		flex: 0 0 calc(50% - 10px);
		&.flex1 {
			flex: 0 0 100%;
		}

		&:has(.fs-button) {
			margin-bottom: 0;
		}
		&.country_box {
			flex: 0 0 calc(50% - 10px);
		}
		// &:has(.policy_box) {
		// 	padding: 32px 0;
		// 	margin-bottom: 0;
		// }
	}
	.policy_box {
		display: flex;
		align-items: center;
		cursor: pointer;
		line-height: 22px;
		margin: 12px 0 4px;
		.fs-checkbox {
			margin-right: 0;
			height: 22px;
		}
	}
	.fs-button {
		width: max-content;
	}
}
:deep(.fs-form__item--label) {
	width: max-content;
}
.fs-tooltip {
	margin-left: 8px;
}
.fs-select {
	width: 100%;
}
:deep(.fs-textarea__wrapper) {
	@include font13;
}

@media (max-width: 1024px) {
	.wrap {
		width: 100%;
		padding: 0 24px;
	}
	.content {
		width: 100%;
		max-width: 100%;
	}
	.success {
		padding: 120px 16px;
		h2 {
			@include font20;
		}
	}
}
@media (max-width: 768px) {
	.banner_div {
		.banner {
			height: 233px;
			.banner-text {
				.banner-title {
					@include font24;
				}
			}
		}
	}
	.content {
		padding-top: 24px;
	}
	.wrap {
		padding: 0 16px;
	}
	.success {
		padding: 36px 16px;
		h2 {
			@include font16;
		}
	}
	.fs-form {
		display: block;
		.fs-button {
			width: 100%;
		}
		.fs-form__item {
			&:has(.fs-checkbox) {
				// margin-bottom: 24px;
				// margin-top: 24px;
				padding: 0;
			}
			&:deep(.fs-form__item--label) {
				text-wrap: wrap;
				max-width: 100%;
				word-break: break-all;
				height: auto;
				line-height: 20px;
			}
		}
	}
}
:deep(.fs-textarea__wrapper) {
	padding: 8px 12px;
}
</style>
