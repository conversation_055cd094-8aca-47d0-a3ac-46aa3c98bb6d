<template>
	<div class="container">
		<div class="content">
			<p class="desc">{{ descStr }}</p>
			<p class="tit">{{ localeLang("contactSales.LetUs") }}</p>
			<p class="txt">{{ localeLang("contactSales.LetUsTxt") }}</p>
			<fs-form ref="formRef" :model="formModel" :rules="rules" @keydown.enter.capture="enterSubmit">
				<fs-form-item v-if="!isCn" :label="localeLang('formValidate.form.first_name')" class="itemtIitle mr12" prop="first_name">
					<fs-input v-model="formModel.first_name"></fs-input>
				</fs-form-item>

				<fs-form-item :label="localeLang('formValidate.form.last_name')" class="itemtIitle" :class="{ mr12: isCn }" prop="last_name">
					<fs-input v-model="formModel.last_name"></fs-input>
				</fs-form-item>

				<fs-form-item v-if="isCn" :label="localeLang('formValidate.form.first_name')" class="itemtIitle" prop="first_name">
					<fs-input v-model="formModel.first_name"></fs-input>
				</fs-form-item>

				<fs-form-item :label="localeLang('formValidate.form.email_business')" prop="business_email" class="itemtIitle">
					<fs-input v-model="formModel.business_email" />
				</fs-form-item>

				<fs-form-item :label="localeLang('formValidate.form.comment')" prop="comments">
					<fs-input v-model="formModel.comments" type="textarea" :maxlength="5000" :placeholder="localeLang('contactSales.helpPH')"></fs-input>
				</fs-form-item>

				<fs-form-item prop="policy">
					<FsCheckbox v-model="formModel.policy" size="small">
						<span v-html="policyText"></span>
					</FsCheckbox>
				</fs-form-item>
			</fs-form>
			<fs-button type="red" class="submit_btn" :loading="loadingRef" @click="handleSubmit">{{ localeLang("contactSales.Submit") }}</fs-button>
			<p class="desc_m">{{ localeLang("solutionDemoTest.desc") }}</p>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { FsForm, FsFormItem, FsInput, FsButton, FsCheckbox } from "fs-design";
import { onEnterSubmit } from "@/utils/utils";
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const { website, isCn } = useWebsiteStore();
const userStore = useUserStore();
const route = useRoute();
const { getRecaptchaToken } = useGrecaptcha();
const emits = defineEmits(["submitSuccess"]);

// 加载中
const loadingRef = ref(false);
// 记录表单验证提示语
const formRef = ref();

// 描述文案
const descStr = ref("");
if (route.path.includes("/solution-demo-test.html")) {
	descStr.value = localeLang("solutionDemoTest.desc");
} else if (route.path.includes("/data-center-solution-demo-test.html")) {
	descStr.value = localeLang("solutionDemoTest.desc1");
} else {
	descStr.value = localeLang("solutionDemoTest.desc");
}

const formModel = reactive({
	first_name: "",
	last_name: "",
	business_email: "",
	comments: "",
	policy: false
});

const policyText = computed(() => {
	return `I agree to FS's <a href='AAAA' target="_blank">Privacy Notice</a> and <a href='BBBB' target="_blank">Terms of Use</a>.`
		.replace("AAAA", localeLink("/policies/privacy_policy.html"))
		.replace("BBBB", localeLink("/policies/terms_of_use.html"));
});

const enterSubmit = (e: { isComposing: any; keyCode: number; shiftKey: any; ctrlKey: any; altKey: any; metaKey: any; target: { tagName: any }; preventDefault: () => void }) => {
	onEnterSubmit(e, handleSubmit);
};

const rules = reactive({
	first_name: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.entry_firstname_error"),
			trigger: "blur"
		}
	],
	last_name: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.entry_lastname_error"),
			trigger: "blur"
		}
	],
	business_email: [
		{
			required: !isCn,
			message: localeLang("formValidate.form.errors.email_business_error"),
			trigger: "blur"
		},
		{
			type: "email",
			message: localeLang("formValidate.form.errors.email_business_error01"),
			trigger: "blur"
		}
	],
	comments: [
		{
			required: true,
			message: localeLang("contactSales.fieldRequired"),
			trigger: "blur"
		}
	],
	policy: [
		{
			required: true,
			trigger: "change",
			message: "This field is required.",
			validator: (_: any, val: boolean) => val === true
		}
	]
});

watch(
	() => userStore.userInfo,
	val => {
		if (val) {
			const { customers_firstname = "", customers_lastname = "", customers_email_address = "" } = val;
			formModel.first_name = customers_firstname;
			formModel.last_name = customers_lastname;
			formModel.business_email = customers_email_address;
		}
	},
	{
		immediate: true,
		deep: true
	}
);

function clickLink(e: any) {
	console.log(e.target.href, "e.target.href");
	let eventCategory = "";
	let eventAction = "";
	if (route.path.includes("/solution-demo-test.html")) {
		eventCategory = "Solution Demo Page";
		eventAction = "Demo_Information";
	} else if (route.path.includes("/data-center-solution-demo-test.html")) {
		eventCategory = "Data Center Solution Demo Page";
		eventAction = "Data Center Demo_Information";
	} else {
		eventCategory = "Solution Demo Page";
		eventAction = "Demo_Information";
	}
	if (e.target && e.target.href && e.target.href.includes("privacy_policy")) {
		window.dataLayer &&
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory,
				eventAction,
				eventLabel: "privacy_policy",
				nonInteraction: false
			});
	} else {
		window.dataLayer &&
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory,
				eventAction,
				eventLabel: "terms_of_use",
				nonInteraction: false
			});
	}
}

function handleGa() {
	let eventCategory = "";
	let eventAction = "";
	let eventLabel = "";
	if (route.path.includes("/solution-demo-test.html")) {
		eventCategory = "Solution Demo Page";
		eventAction = "Demo_Information";
		eventLabel = "Demo_Submit";
	} else if (route.path.includes("/data-center-solution-demo-test.html")) {
		eventCategory = "Data Center Solution Demo Page";
		eventAction = "Data Center Demo_Information";
		eventLabel = "Data Center Demo_Submit";
	} else {
		eventCategory = "Solution Demo Page";
		eventAction = "Demo_Information";
		eventLabel = "Demo_Submit";
	}

	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory,
			eventAction,
			eventLabel,
			nonInteraction: false
		});
}

function handleSubmit() {
	formRef.value?.validate().then(async (val: any) => {
		if (val) {
			loadingRef.value = true;
			const { recaptchaTp = false, headers = {} } = await getRecaptchaToken();
			if (!recaptchaTp) {
				loadingRef.value = false;
				return;
			}
			const data = new FormData();
			data.append("first_name", formModel.first_name);
			data.append("last_name", formModel.last_name);
			data.append("email", formModel.business_email);
			data.append("comments", formModel.comments);
			data.append("website_link", location.href);
			const { data: resData, error } = await useRequest.post("/api/solutionDemoTest", { data, headers });
			if (resData.value && resData.value.code === 200) {
				handleGa();
				emits("submitSuccess");
			}
			if (error.value && error.value.data) {
				const { errors, code } = error.value.data;
				if (code === 422 && errors) {
					if (errors.first_name) {
						formRef.value?.setFormItemErrorMsg("first_name", errors.first_name[0]);
					}
					if (errors.last_name) {
						formRef.value?.setFormItemErrorMsg("last_name", errors.last_name[0]);
					}
					if (errors.business_email) {
						formRef.value?.setFormItemErrorMsg("email", errors.business_email[0]);
					}
					if (errors.comments) {
						formRef.value?.setFormItemErrorMsg("comments", errors.comments[0]);
					}
					if (errors.website_link) {
						formRef.value?.setFormItemErrorMsg("website_link", errors.website_link[0]);
					}
				}
			}
			loadingRef.value = false;
		}
	});
}
</script>

<style lang="scss" scoped>
.container {
	padding: 40px 48px;
	.content {
		max-width: 900px;
		margin: 0 auto;
		.desc {
			@include font14;
			color: $textColor1;
			margin-bottom: 16px;
		}
		.tit {
			@include font16;
			font-weight: 600;
			color: $textColor1;
			margin-bottom: 4px;
		}
		.txt {
			@include font14;
			color: $textColor2;
			margin-bottom: 12px;
		}
		.itemtIitle {
			width: calc(50% - 6px);
			display: inline-block;
			&.mr12 {
				margin-right: 12px;
			}
		}

		.fs-form__item:last-child {
			margin-bottom: 16px;
		}

		.agreement_wrap {
			margin-top: 16px;
			@include font12;
			line-height: 20px;
			color: $textColor2;
		}
		.desc_m {
			display: none;
		}
		:deep(.fs-input__wrapper) {
			background: #f6f6f8;
			border-color: #f6f6f8;
			// @media (max-width: 819px) {
			// 	background: #fff;
			// 	height: 42px;
			// 	@include font13;
			// }
		}
		:deep(.fs-textarea__wrapper) {
			background: #f6f6f8;
			box-shadow: none;
		}
		:deep(.fs-textarea__number) {
			top: -26px;
		}
	}
}

@media (max-width: 1024px) {
	.container {
		padding: 36px 24px;
	}
}

@media (max-width: 768px) {
	.container {
		padding: 36px 16px;
		.content {
			width: 100%;
			.desc {
				display: none;
			}
			.itemtIitle {
				width: 100%;
			}

			.submit_btn {
				width: 100%;
			}
			.desc_m {
				display: block;
				@include font14;
				color: $textColor1;
				margin-top: 24px;
			}
		}
	}
}
</style>
