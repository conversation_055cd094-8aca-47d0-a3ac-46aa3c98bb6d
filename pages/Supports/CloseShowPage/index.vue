<template>
	<div class="container">
		<div class="content">
			<h3 class="title">Coming Soon</h3>
			<p class="des">We are building a professional community to provide better customer support!</p>
			<form class="form" @submit.prevent="submit">
				<div class="form_item">
					<input
						v-model="email"
						aria-autocomplete="none"
						autocomplete="off"
						:placeholder="localeLang('footer.footerSubscribe.Enter_your_email')"
						:focusShowPlaceholder="true"
						@input="input" />
					<FsButton type="red" native-type="submit" :loading="loading">{{ localeLang("footer.footerSubscribe.Subscribe") }}</FsButton>
				</div>
				<ValidateError :error="error_info" />
				<div v-if="success" class="error_info" :class="{ error_info_success: success }">{{ success_info }}</div>
			</form>
			<p class="des" v-html="localeLang('error.goTo').replace('XXXX', localeLink('/')).replace('YYYY', localeLink('/contact_us.html'))"></p>
		</div>
	</div>
</template>
<script setup lang="ts">
import { FsButton } from "fs-design";
import ValidateError from "../component/FsFooter/components/ValidateError.vue";
definePageMeta({
	layout: "common"
});
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const loading = ref(false);
const email = ref("");
const success = ref(false);
const error_info = ref("");
const success_info = ref("");
const check = ref(true);
const { getRecaptchaToken } = useGrecaptcha();
const input = () => {
	success.value = false;
	if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value.replace(/\s+/g, ""))) {
		error_info.value = "";
	}
};
const submit = async () => {
	if (loading.value) {
		return;
	}
	if (!email.value.replace(/\s+/g, "")) {
		success.value = false;
		error_info.value = localeLang("footer.footerSubscribe.Please_enter_your_email_address");
		return;
	}
	if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value.replace(/\s+/g, ""))) {
		success.value = false;
		error_info.value = localeLang("footer.footerSubscribe.Please_enter_a_valid_email_address");
		return;
	}
	if (!check.value) {
		return;
	}
	loading.value = true;
	const { recaptchaTp = false, headers = {} } = await getRecaptchaToken();
	if (!recaptchaTp) {
		loading.value = false;
		return;
	}
	const { data, error } = await useRequest.get("/api/subscribeBlog", { data: { email: email.value }, headers });
	loading.value = false;
	if (data.value && data.value.data) {
		success.value = true;
		success_info.value = localeLang("footer.footerSubscribe.Welcome_to_FS_You_be_sent");
	}
	if (error.value && error.value.data && error.value.data.errors && error.value.data.errors.email) {
		error_info.value = error.value.data.errors.email.join(" ");
	}
};
</script>

<style lang="scss" scoped>
.container {
	height: 636px;
	margin: 0 auto;
	max-width: 1200px;
	width: 84vw;
	display: flex;
	justify-content: center;
	align-items: center;
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 864px;
		.title {
			@include font24;
			font-weight: 600;
			color: $textColor1;
		}
		.des {
			@include font14;
			color: $textColor2;
			text-align: center;
		}
	}
}
.form {
	margin-top: 24px;
	margin-bottom: 24px;
	@media (max-width: 819px) {
		margin-bottom: 21px;
	}
}
.form_item {
	display: flex;
	// gap: 8px;
	input {
		background: #f6f6f8;
		border-width: 0;
		border-radius: 4px 0 0 4px;
		padding: 0 12px;
		font-size: 12px;
		line-height: 20px;
		height: 32px;
		transition: none;

		&:focus {
			background: #f6f6f8;
			border: 1px solid #eeeeee;
			&::placeholder {
				opacity: 0;
			}
			&:hover {
				background: #f6f6f8;
			}
		}
		&::placeholder {
			color: #707070;
		}
		&:hover {
			background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
			@media (max-width: 819px) {
				background: #f6f6f8;
			}
		}
		// @media (max-width: 819px) {
		// 	&:focus {
		// 		background: #fff;
		// 		&:hover {
		// 			background: #fff;
		// 		}
		// 	}
		// 	&:hover {
		// 		background: #fff;
		// 	}
		// }
	}
	:deep(.fs-input) {
		&.is_focus {
			.fs-input__wrapper {
				border-radius: 4px 0 0 4px;
			}
		}
		.fs-input__inner {
			&:hover {
				cursor: text;
			}
		}
		.fs-input__wrapper {
			background: #f6f6f8;
			border-width: 0;
			border-radius: 4px 0 0 4px;
			padding: 0 12px;
			font-size: 12px;
			line-height: 20px;
			@media (max-width: 819px) {
				background: #fff;
				height: 42px;
				@include font13;
			}
		}
	}
	:deep(.fs-button) {
		margin-left: -2px;
		border-radius: 0 4px 4px 0;
		font-size: 12px;
		line-height: 20px;
		height: 32px;
		padding: 0 12px;
		background: #c00000;
		@media (max-width: 819px) {
			padding: 10px 24px;
			@include font14;
		}
	}
}
.error_info {
	@include font13;
	color: #c00000;

	&.error_info_success {
		color: #10a300;
		padding-top: 4px;
		text-align: center;
	}
}
</style>
