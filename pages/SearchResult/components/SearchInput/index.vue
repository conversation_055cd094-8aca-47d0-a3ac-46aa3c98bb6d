<template>
	<div class="searchContainer">
		<FsTooltip
			ref="searchTooltip"
			trigger="click"
			:showArrow="false"
			placement="bottom-start"
			sameWidth
			:popperContentStyle="popperContentStyle"
			:offsetY="4"
			@visibleChange="handleVisibleChange">
			<div class="searchInput">
				<FsInput
					ref="searchInputRef"
					v-model="searchQuery"
					:placeholder="isFocus ? '' : localeLang('header.search.pcPlaceholder')"
					clearable
					newRegular
					@focus="handleInputFocus"
					@blur="handleInputBlur"
					@input="handleInputChange"
					@keyup.enter="handleSubmitSearch"
					@clear="handleClear" />
				<FsButton type="red" @click="handleSubmitSearch"> {{ localeLang("header.search.pcPlaceholder") }}</FsButton>
			</div>

			<template #content>
				<!-- 内容区分两种状态：有搜索建议 或 无搜索建议 -->
				<template v-if="showSuggestions">
					<!-- 搜索建议模块 -->
					<div v-loading="isLoading" class="searchSuggestions">
						<template v-if="suggestions && suggestions.length > 0">
							<div v-for="(item, index) in suggestions" :key="index" class="searchSuggestions__item" @click="handleSuggestionClick(item.name)">
								<span class="searchSuggestions__text" v-html="highlightKeyword(item.name, searchQuery)"></span>
							</div>
						</template>
						<template v-else-if="!isLoading">
							<div class="empty-result">
								{{ localeLang("header.noCountryResult") }}
							</div>
						</template>
					</div>
				</template>

				<template v-else>
					<!-- 历史搜索模块 -->
					<div v-if="showHistory && historyList.length" class="searchHistory">
						<div class="searchHistory__title">{{ localeLang("header.search.recentSearch") }}</div>
						<div class="searchHistory__list">
							<div v-for="(item, index) in historyList" :key="index" class="searchHistory__list-item" @click="handleHistoryClick(item)">
								<span class="searchHistory__text">{{ item }}</span>
								<i class="iconfont icon-delete" @click.stop="handleHistoryRemove(item)">&#xf30a;</i>
							</div>
						</div>
					</div>

					<!-- 热门搜索模块 -->
					<div v-if="showHotSearch && hotSearchList?.length" class="searchHot" :class="{ searchHot__top: showHistory && historyList.length }">
						<div class="searchHot__title">
							<span>{{ localeLang("header.search.hotSearch") }}</span>
							<div class="searchHot__title-change" @click="getHotSearch">
								<i class="iconfont" :class="{ iconfont_change_rotate: hotChangePending }">&#xe705;</i>
								<span>{{ localeLang("header.search.change") }}</span>
							</div>
						</div>
						<div class="searchHot__list">
							<div v-for="(item, index) in hotSearchList" :key="index" class="searchHot__list-item" @click="handleSuggestionClick(item.name)">
								<span class="searchHot__item-text">{{ item.name }}</span>
							</div>
						</div>
					</div>
				</template>
			</template>
		</FsTooltip>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip, FsInput, FsButton } from "fs-design";
import type { CategoryContextKey } from "../../types";
import { useSearchHistory } from "./hooks/useSearchHistory";
import { useSearchSuggestions } from "./hooks/useSearchSuggestions";
import { removeLabel } from "@/utils/utils";
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const searchTooltip = ref();
const headerStore = useHeaderStore();
const { hotChangePending } = storeToRefs(headerStore);
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const isFocus = ref(false);
const searchInputRef = ref();
const popperContentStyle = {
	padding: "28px 24px 48px 12px",
	width: "900px",
	maxWidth: "900px",
	zIndex: "1",
	border: "1px solid #DEE0E3"
};
const suggestionStyle = {
	padding: "8px 0",
	width: "900px",
	maxWidth: "900px",
	border: "1px solid #DEE0E3",
	zIndex: "1"
};
// 搜索历史相关
const { searchQuery, historyList, showHistory, hotSearchList, showHotSearch, addHistory, removeHistory, clearHistory, toggleHistory } = useSearchHistory();

// 搜索建议相关
const { suggestions, showSuggestions, isLoading, getSuggestions, clearSuggestions, highlightKeyword } = useSearchSuggestions();

// 确保输入为空时显示历史记录
const ensureHistoryView = () => {
	// 强制清除建议状态
	clearSuggestions();
	// 强制更新弹出框样式
	if (searchTooltip.value?.updateContentStyle) {
		searchTooltip.value.updateContentStyle(popperContentStyle);
	}
	// 强制显示历史记录
	toggleHistory(true);
};

// 处理气泡框显示变化
const handleVisibleChange = (visible: boolean) => {
	if (!visible) {
		toggleHistory(false);
		clearSuggestions();
	} else {
		handleInputFocus();
		showHotSearch.value = hotSearchList.value.length > 0;
	}
};

// 处理输入框聚焦
const handleInputFocus = () => {
	searchInputRef?.value?.ref.focus();
	isFocus.value = true;
	if (searchQuery.value) {
		// getSuggestions(searchQuery.value);
		if (!showSuggestions.value) {
			toggleHistory(true);
		}
	} else {
		toggleHistory(true);
	}
};

// 处理输入框失焦
const handleInputBlur = () => {
	isFocus.value = false;
};

// 处理输入变化
const handleInputChange = (val: string) => {
	if (val) {
		getSuggestions(val);
	} else {
		ensureHistoryView();
	}
};

// 提交搜索
const handleSubmitSearch = () => {
	if (!searchQuery.value.trim()) return;
	addHistory(searchQuery.value);
	searchTooltip.value.hide();
	showHistory.value = false;
	showHotSearch.value = false;
	showSuggestions.value = false;
	location.href = localeLink(`/search_result?keyword=${encodeURIComponent(removeLabel(searchQuery.value))}`);
	injectState.loading.value = true;
};

// 点击历史记录
const handleHistoryClick = (keyword: string) => {
	searchQuery.value = keyword;
	handleSubmitSearch();
};

// 删除单个历史记录
const handleHistoryRemove = (keyword: string) => {
	removeHistory(keyword);
	if (historyList.value.length === 0) {
		toggleHistory(false);
	}
};

const handleClear = () => {
	searchQuery.value = "";
	clearSuggestions();
	toggleHistory(true);
};

// 点击搜索建议
const handleSuggestionClick = (suggestion: string) => {
	searchQuery.value = suggestion;
	clearSuggestions();
	handleSubmitSearch();
};

// 更新热门搜索
const getHotSearch = () => headerStore.getSearchHot();

// 监听搜索建议变化
watch(
	() => showSuggestions.value,
	hasResults => {
		// 先检查输入框是否为空
		if (!searchQuery.value) {
			ensureHistoryView();
			return;
		}
		if (hasResults) {
			searchTooltip.value.updateContentStyle(suggestionStyle);
			toggleHistory(false);
		} else {
			searchTooltip.value.updateContentStyle(popperContentStyle);
			toggleHistory(true);
		}
	}
);

watch(
	() => searchQuery.value,
	val => {
		if (!val) {
			ensureHistoryView();
		}
	}
);
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
