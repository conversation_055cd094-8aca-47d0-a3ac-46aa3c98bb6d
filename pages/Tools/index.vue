<template>
	<FsLayout>
		<component :is="componentMap[modulesId]" :componentData="componentData" />
	</FsLayout>
</template>
<script lang="ts" setup>
import { provide, ref, computed } from "vue";
import { Tools, toolsMenu, whitePages } from "./constants";
import type { ToolComponentBase } from "./types";
import FsLayout from "./Layout/index.vue";
import Home from "./pages/Home/index.vue";
import AlarmQuery from "./pages/AlarmQuery/index.vue";
import CompatibilityQuery from "./pages/CompatibilityQuery/index.vue";
import ConfigureQuery from "./pages/ConfigureQuery/index.vue";
import MibQuery from "./pages/MibQuery/index.vue";
import ProductSimulator from "./pages/ProductSimulator/index.vue";
import VisioGallery from "./pages/VisioGallery/index.vue";
import PurchasedModuleQuery from "./pages/PurchasedModuleQuery/index.vue";
definePageMeta({
	layout: "common"
});
const componentMap = {
	"tool-home": Home,
	"alarm-query": AlarmQuery,
	"product-simulator": ProductSimulator,
	"compatibility-query": CompatibilityQuery,
	"visio-gallery": VisioGallery,
	"configure-query": ConfigureQuery,
	"purchased-module-query": PurchasedModuleQuery,
	"mib-query": MibQuery
};
const localeLang = useLocaleLang();
const route = useRoute();
const websiteStore = useWebsiteStore();
const runtimeConfig = useRuntimeConfig();
const localeLink = useLocaleLink();
const { website } = storeToRefs(websiteStore);
// 如果当前id不属于tools中的任何一个id，则跳转到404页面
if (!Object.keys(Tools).includes(route.params.id as string)) {
	navigateTo(localeLink(runtimeConfig.public.VITE_NUXT_DOMAIN + "/404.html"), { external: true });
}
// 小语种页面筛选，小语种可以跳转白名单页面
let flag = true;
whitePages.forEach(item => {
	if (route.path.includes(item)) {
		flag = false;
	}
});
// 当前语言不是英文时，跳转到首页
if (flag) {
	if (!["en", "uk", "au", "sg", "de-en"].includes(website.value)) {
		const path = localeLink("/");
		await navigateTo(path, { external: true });
	}
}
const componentData = ref<ToolComponentBase>({
	ToolMenuData: toolsMenu
});
const modulesId = ref<keyof typeof Tools>(route.params.id as keyof typeof Tools);
// const asyncComponent = (name: string) => {
// 	if (name) {
// 		return componentMap[name];
// 	}
// };
// const activeComponent = computed(() => {
// 	return asyncComponent(Tools[modulesId.value]);
// });
const breadcrumbData = computed(() => {
	const baseBreadcrumb = [
		{
			name: localeLang("technicalDocuments.productSupport.home"),
			path: "/",
			active: false
		},
		{
			name: localeLang("queryTools.purchasedQuery.tool"),
			path: "/tool/tool-home",
			active: modulesId.value === "tool-home"
		}
	];

	if (modulesId.value != "tool-home") {
		const currenNode = toolsMenu.find(item => item.filterName === modulesId.value);

		const currenName = currenNode?.path === "/tool/purchased-module-query" ? localeLang("queryTools.purchasedQuery.title") : currenNode?.label;
		baseBreadcrumb.push({
			name: currenName || "",
			path: `/tool/${modulesId.value}`,
			active: true
		});
		if (modulesId.value === "purchased-module-query" && !["en", "uk", "sg", "au", "de-en"].includes(website.value)) {
			baseBreadcrumb.splice(1, 1);
		}
	}
	return baseBreadcrumb;
});
provide("breadcrumbData", breadcrumbData);
provide("modulesId", modulesId);
</script>
