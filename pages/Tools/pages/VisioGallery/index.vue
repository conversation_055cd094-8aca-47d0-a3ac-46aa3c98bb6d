<template>
	<div class="visio-gallery">
		<!-- 标题 -->
		<div class="visio-gallery-title">
			<span>Visio Gallery</span>
		</div>
		<div class="visio-gallery-filter">
			<div class="visio-gallery-filter-item">
				<span>Keywords</span>
				<div class="fs-tool-visio-gallery-cont">
					<div class="fs-tool-visio-gallery-form">
						<FsInput v-model="productModel.keyword" placeholder="Search" maxlength="200" @change="handleKeydown" @search="handleKeydown" @keydown="handleKeydown"
							><template #suffix>
								<span v-if="productModel.keyword.length > 0" class="iconfont iconfont_close" @click="clearKeywords">&#xf30a;</span>
							</template></FsInput
						>
					</div>
					<FsButton :disabled="!productModel.keyword" class="item-button" type="red" @click="search">Search</FsButton>
				</div>
			</div>
		</div>
		<div class="visio-gallery-divider"></div>
		<div v-if="productModelOptions.length > 0">
			<FsTabs @change="changeIndex">
				<template #rightExtra>
					<div v-if="visioView && baseData.length > 0" class="visio-gallery-content-download">
						<FsButton type="black" iconPlacement="prefix" plain @click="download(downloadMap.png)">
							<template #default>PNG</template>
							<template #icon>
								<i class="iconfont iconfs_2023103017icon" />
							</template>
						</FsButton>
						<FsButton type="black" iconPlacement="prefix" plain @click="download(downloadMap.vss)">
							<template #default>VSS</template>
							<template #icon>
								<i class="iconfont iconfs_2023103017icon" />
							</template>
						</FsButton>
					</div>
				</template>
				<FsTabPane v-for="(value, index) in productModelOptions" :key="index" :label="value?.label" :name="value?.value">
					<div class="visio-gallery-content">
						<div v-if="baseData.length === 0" class="visio-gallery-content-empty">
							<div class="title">There is no content available.</div>
						</div>
						<!-- 内容 -->
						<div :class="['visio-gallery-content-box', !visioView ? 'second-box' : '']">
							<template v-if="visioView">
								<div v-for="(item_p, index_p) in baseData" :key="index_p" class="visio-gallery-content-box-item icon-type">
									<img v-if="item_p.visioimgs.length > 0" class="fs-tool-visio-gallery-image" :src="item_p.visioimgs[0].imgUrl" alt="image" />
									<div class="visio-gallery-content-box-item-name">{{ item_p.visioName }}</div>
									<div class="visio-gallery-content-box-item-download">
										<span class="title">Download</span>
										<FsButton v-if="item_p.visioimgs.length > 0" type="primary" text @click="downloadFileResources(item_p.visioimgs[0].imgUrl, item_p.visioName)">PNG</FsButton>
									</div>
								</div>
							</template>
							<template v-else>
								<div v-for="(item_v, index_v) in baseData" :key="index_v" class="visio-gallery-content-box-item">
									<img v-if="item_v.visioimgs.length > 0" class="fs-tool-visio-gallery-image-second" :src="item_v.visioimgs[0].imgUrl" alt="image" />
									<div class="visio-gallery-content-box-item-name divice_name">{{ item_v.visioName }}</div>
									<div class="visio-gallery-content-box-item-describe">{{ item_v.visioDetail }}</div>
									<div class="visio-gallery-content-box-item-download">
										<span class="title">Download</span>
										<FsButton type="primary" text @click="download(item_v.visioPngurl)">PNG</FsButton>
										<span class="line"></span>
										<FsButton type="primary" text @click="download(item_v.visioVssurl)">VSS</FsButton>
									</div>
								</div>
							</template>
						</div>
					</div>
				</FsTabPane>
			</FsTabs>
		</div>

		<div v-if="baseData.length > 0" class="visio-gallery-content-page">
			<FsPagination :total="total" :current="productModel.pageindex" :pageSize="productModel.pagesize" @change="clickBuried"></FsPagination>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { FsInput, FsPagination, FsButton, FsTabs, FsTabPane } from "fs-design";
import { ref } from "vue";
import type { visioData } from "../../types";
import { downloadFileResources } from "./download";

// 两种视图的切换
const visioView = ref(false);
// 下载链接
const downloadMap = ref({
	png: "",
	vss: ""
});

const productIdValue = ref(""); // 当前节点ID对应的value
const productModelOptions = ref([
	{
		value: "1",
		label: "Optical transmission NE icons",
		id: 1,
		icon: null,
		isVersion: false,
		isVisio: 0,
		url: null,
		hasChild: false,
		children: []
	},
	{
		value: "2",
		label: "Optical access NE icons",
		id: 2,
		icon: null,
		isVersion: false,
		isVisio: 0,
		url: null,
		hasChild: false,
		children: []
	},
	{
		value: "0",
		label: "WDM / OTN / PON Equipment",
		id: 0,
		icon: null,
		isVersion: false,
		isVisio: 0,
		url: null,
		hasChild: false,
		children: []
	}
]);
// 当前选中项
const total = ref(0);
const loading = ref(false);
const productModel = ref({
	productid: "1",
	keyword: "",
	pageindex: 1,
	pagesize: 16
});
const baseData = ref<visioData[]>([]);
const handleKeydown = (value: string | number) => {
	productModel.value.pageindex = 1;
	productModel.value.keyword = value as string;
	getVisioList();
};
const clickBuried = (current: number) => {
	productModel.value.pageindex = current;
	getVisioList();
};
// 传入三个大分类的id
const changeIndex = (val: any) => {
	const productId = val.name;
	productModel.value.productid = productId;
	productModel.value.pagesize = val.name === "0" ? 8 : 16;
	productModel.value.pageindex = 1;
	productIdValue.value = productId;
	getVisioList();
};
// 点击输入框上的叉叉图标清空
const clearKeywords = () => {
	productModel.value.keyword = "";
	productModel.value.pageindex = 1;
	getVisioList();
};

// 获取下拉数据
const getProductModelOptions = async () => {
	// const { data } = await useRequest.get("/tools/mib/getproducttree?toolsname=visio&type=3");
	// 测试链接，上线需用上面注释的
	// https://tools.fs.com:8443/tools/mib/getproducttree?toolsname=visio&type=3  测试站使用
	const { data } = await useRequest.get("/tools/mib/getproducttree?toolsname=visio&type=3");
	productModelOptions.value = data.value?.obj ?? {};
	const defaultProductId = productModelOptions.value[0]?.id;
	productModel.value.productid = defaultProductId.toString();
	productModel.value.pageindex = 1;
	productIdValue.value = defaultProductId.toString();
	getVisioList();
};
// 获取列表数据
const getVisioList = async () => {
	loading.value = true;
	// "/tools/visio/getvisiolist"
	// 测试链接，上线需用上面注释的
	// https://tools.fs.com:8443/tools/visio/getvisiolist 测试站使用
	const { data } = await useRequest.get("/tools/visio/getvisiolist", {
		params: {
			...productModel.value
		}
	});
	loading.value = false;
	const { total: totalPage, records = [], isvisio = false } = data.value.obj;
	visioView.value = isvisio;
	baseData.value = records;
	downloadMap.value = {
		png: records[0]?.visioPngurl ?? "",
		vss: records[0]?.visioVssurl ?? ""
	};
	total.value = totalPage;
};
// 下载
const download = (url: string) => {
	window.open(url);
};
const search = () => {
	loading.value = true;
	productModel.value.pageindex = 1;
	// keywordLabel.value = getLabelByTreeData(productModelOptions.value, productIdValue.value);
	getVisioList();
};

getProductModelOptions();

// watch(
// 	[productModel],
// 	() => {
// 		getVisioList();
// 	},
// 	{ deep: true, immediate: true }
// );
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
