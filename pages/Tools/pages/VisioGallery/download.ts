import { Base64 } from "js-base64";
import CryptoJS from "crypto-js";

function genNonce(num: number) {
	const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
	let str = "";
	for (let i = 0; i < num; i++) {
		const pos = Math.round(Math.random() * (chars.length - 1));
		str += chars[pos];
	}
	return str;
}

export const downloadFileResources = async (imgUrl: string, imgName: string) => {
	try {
		const runtimeConfig = useRuntimeConfig();
		const websiteStore = useWebsiteStore();

		const webSiteInfo = {
			isCookie: true,
			website: websiteStore.website,
			iso_code: websiteStore.iso_code,
			currency: websiteStore.currency,
			language: websiteStore.language,
			language_id: websiteStore.language_id,
			locale: websiteStore.locale,
			countries_id: websiteStore.countries_id,
			warehouse: websiteStore.warehouse,
			country_name: websiteStore.country_name
		};

		const timestamps = new Date().getTime();
		const nonce = genNonce(16);
		const apiKey = "yuxuanxuanpc";
		const payload = "[]";
		const s = timestamps + nonce + payload;
		const h = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, "yuxuan3507");
		h.update(s);
		const signature = Base64.encode(CryptoJS.enc.Hex.stringify(h.finalize()));

		// 1. 配置请求头（根据需求添加）
		const headerss = new Headers();
		headerss.append("Content-Type", "application/json");
		headerss.append("websiteinfo", Base64.encode(JSON.stringify(webSiteInfo))); // Token认证
		headerss.append("apikey", apiKey); // 自定义头（根据需要添加）
		headerss.append("clientsignature", signature); // 自定义头（根据需要添加）
		headerss.append("nonce", nonce); // 自定义头（根据需要添加）
		headerss.append("timestamps", timestamps.toString()); // 自定义头（根据需要添加）

		// 2. 拼接url
		let baseURL = "";
		if (runtimeConfig.public.VITE_NUXT_ENV === "PROD_CN" && ["hk", "tw", "mo"].includes(websiteStore.website)) {
			baseURL = runtimeConfig.public.VITE_NUXT_FS_HK_API || runtimeConfig.public.VITE_NUXT_FS_API;
		} else {
			if (websiteStore.website === "cn") {
				baseURL = runtimeConfig.public.VITE_NUXT_CN_FS_API || runtimeConfig.public.VITE_NUXT_FS_API;
			} else {
				baseURL = runtimeConfig.public.VITE_NUXT_FS_API;
			}
		}
		const requestUrl = `${baseURL}/api/s3Download?key_name=${imgUrl}&file_name=${imgName}&type=productReporting`;

		// 3. 发送请求
		const response = await fetch(requestUrl, {
			method: "GET", // 或 GET（根据后端接口设计）
			headers: headerss
		});

		// 4. 处理响应错误
		if (!response.ok) {
			const errorText = await response.text(); // 尝试获取错误信息
			throw new Error(`下载失败: ${response.status} - ${errorText}`);
		}

		// 5. 处理文件下载
		const blob = await response.blob();
		const url = URL.createObjectURL(blob);
		const link = document.createElement("a");
		link.href = url;

		// 从响应头获取文件名（后端需正确返回）
		const contentDisposition = response.headers.get("Content-Disposition");
		if (contentDisposition) {
			const filename = contentDisposition.match(/filename="(.*?)"/)?.[1];
			if (filename) link.download = decodeURIComponent(filename); // 解码中文文件名
		} else {
			// link.download = "Bood.png"; // 默认文件名
			link.download = imgUrl.split("/").slice(-1)[0];
		}

		// 触发下载
		document.body.appendChild(link);
		link.click();
		// 清理资源
		document.body.removeChild(link);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("下载出错:", error);
	}
};
