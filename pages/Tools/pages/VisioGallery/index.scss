.visio-gallery {
	padding-bottom: 40px;
	@media screen and (min-width: 1024px) and (max-width: 1200px) {
		margin: 0 24px;
	}
	@include mobile {
		padding: 0 16px 16px 16px;
	}

	@include pad {
		padding: 0 24px 40px 24px;
	}

	&-title {
		margin-bottom: 24px;
		@include mobile {
			margin-bottom: 16px;
		}

		span {
			@include font24;
			color: $textColor1;
			font-weight: 600;
			display: flex;
			align-items: center;

			&::before {
				content: "";
				display: inline-block;
				width: 3px;
				height: 20px;
				background: #c00000;
				margin-right: 8px;
				border-radius: 212px;
			}
		}
	}

	&-filter {
		border-radius: 8px;
		background: #fafafb;
		// min-height: 320px;
		padding: 40px 0px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;

		@include mobile {
			padding: 24px 16px;
		}

		&-item {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 24px;
			// :deep(.fs-button) {
			// 	width: 120px;
			// }

			&:last-child {
				margin-bottom: 0;
			}

			@include mobile {
				flex-direction: column;
				align-items: flex-start;
				width: 100%;
				margin-bottom: 16px;
				gap: 4px;
			}

			@include pad {
				// flex-direction: column;
				// align-items: flex-start;
				// width: 100%;
			}

			span {
				@include font14;
				color: $textColor1;
				display: inline-block;
				width: 120px;
				text-align: right;

				@include mobile {
					@include font12;
					margin: 0;
					text-align: left;
				}
			}
			.fs-tool-visio-gallery-cont {
				display: flex;
				@include mobile {
					width: 100%;
				}
				.fs-tool-visio-gallery-form {
					width: 480px;
					&-cascader-m {
						display: none;
					}

					:deep(.fs-tooltip) {
						width: 480px;
					}
					:deep(.fs-input) {
						&.is-disabled {
							.fs-input__box {
								pointer-events: none;
							}
						}
						.fs-input__wrapper {
							border-radius: 4px 0 0 4px;
							background: #fff;
							border-right: 0;
						}
						.iconfont_close {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 28px;
							height: 28px;
							font-size: 16px;
							line-height: 1;
							cursor: pointer;
							color: #707070;
							padding: 6px;
							border-radius: 4px;
							margin-right: 5px !important;
							&:hover {
								color: $textColor1;
								background-color: rgba(25, 25, 26, 0.04);
							}
						}
					}

					@include mobile {
						&-cascader-m {
							display: block;
						}

						&-cascader {
							display: none;
						}

						width: 100%;

						:deep(.fs-tooltip) {
							width: 100%;
						}
						:deep(.fs-input__inner) {
							&::placeholder {
								@include font13;
							}
						}

						:deep(.fs-select) {
							width: 100%;
						}
					}
				}
			}
		}

		.item-button {
			display: flex;
			justify-content: center;
			flex-direction: row;
			background-color: #c00000;
			border-radius: 0px 4px 4px 0px;
			color: #fff;
			// .fs-button {
			// 	&:first-child {
			// 		margin-right: 16px;
			// 	}
			// 	&:last-child {
			// 		margin-left: 0;
			// 	}
			// }
		}
	}

	&-divider {
		margin: 40px 0;
		border-bottom: 1px solid $borderColor1;
		@include mobile {
			margin: 24px 0;
		}
	}

	&-content {
		margin-top: 40px;
		@include pad {
			margin-top: 24px;
		}
		@include mobile {
			margin-top: 16px;
		}
		&-download {
			// margin-bottom: -4px;
			display: flex;
			// border-bottom: 1px solid #e5e5e5;
			justify-content: flex-end;
			box-sizing: border-box;
			:deep(.fs-button) {
				display: inline-block;
				padding: 0 16px;
				box-sizing: border-box;
				top: -4px;
				height: 36px;
				.iconfont {
					color: $textColor1;
				}
				.fs-button--prefix {
					margin-left: 4px;
					@include font14;
				}
			}

			@include mobile {
				align-items: center;
				margin-top: 16px;
				// :deep(.fs-button) {
				// 	width: 100%;
				// }
			}

			// @include pad {
			// 	align-items: center;

			// 	:deep(.fs-button) {
			// 		width: 100%;
			// 	}
			// }
		}
		&-empty {
			display: flex;
			height: 142px;
			justify-content: center;
			align-items: center;
			border: 1px solid $borderColor1;
			.title {
				@include font13;
				color: $textColor2;
			}
		}

		&-box {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 20px;

			@include mobile {
				grid-template-columns: repeat(2, 1fr);
			}

			@include pad {
				grid-template-columns: repeat(3, 1fr);
			}

			&-item {
				height: auto;
				display: flex;
				flex-direction: column;
				align-items: center;
				border: 1px solid $bgColor5;
				padding: 20px 20px 62px 20px;
				border-radius: 8px;
				box-sizing: border-box;
				position: relative;
				overflow: hidden;
				@include mobile {
					width: 100%;
				}

				@include pad {
					width: 100%;
				}

				.fs-tool-visio-gallery-image {
					width: 36px;
					height: auto;
				}

				.fs-tool-visio-gallery-image-second {
					width: 180px;
					height: auto;
				}

				&-name {
					@include font14;
					color: $textColor1;
					font-weight: 600;
					padding-top: 8px;
					// padding-bottom: 12px;
					text-align: center;
					&.divice_name {
						padding-top: 16px;
						padding-bottom: 12px;
					}
				}

				&-describe {
					@include font12;
					color: $textColor2;
					text-align: left;
					// height: 140px;
					max-height: 140px;
					overflow: auto;
					&::-webkit-scrollbar {
						width: 4px;
					}
					&::-webkit-scrollbar-thumb {
						background: #dee0e3;
						border-radius: 2px;
					}
					// p {
					// 	height: 100%;
					// 	overflow: scroll;
					// 	&::-webkit-scrollbar {
					// 		width: 4px;
					// 	}
					// 	&::-webkit-scrollbar-thumb {
					// 		background: #dee0e3;
					// 		border-radius: 4px;
					// 	}
					// }
				}
				&-download {
					display: flex;
					align-items: center;
					width: 100%;
					height: 54px;
					box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.06);
					z-index: 3;
					position: absolute;
					bottom: 0;
					justify-content: center;
					opacity: 0;
					transform: translateY(100%);
					background: $bgColor6;
					transition:
						opacity 0.3s ease,
						transform 0.3s ease;
					.line {
						width: 1px;
						height: 16px;
						background-color: $borderColor1;
						margin: 0 8px;
					}
					.title {
						margin-right: 8px;
						@include font14;
						color: $textColor1;
					}
				}
				&:hover {
					.visio-gallery-content-box-item-download {
						opacity: 1;
						transform: translateY(0);
					}
				}
			}
			.icon-type {
				padding: 32px;
			}
		}
		.second-box {
			@include mobile {
				grid-template-columns: repeat(1, 1fr);
			}

			@include pad {
				grid-template-columns: repeat(3, 1fr);
			}
		}
		&-page {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-top: 20px;
			@include mobile {
				padding-top: 16px;
			}
		}
	}
	:deep(.fs-tabs) {
		.fs-tabs__nav {
			border: none;
			border-bottom: 1px solid $borderColor1;
			.fs-tabs__af {
				right: 0;
			}
			.fs-tabs__more {
				display: none;
			}
			@include mobile {
				flex-direction: column;
				align-items: flex-end;
				border-bottom: 0 solid $borderColor1;
				.fs-tabs__scroll {
					border-bottom: 1px solid $borderColor1;
				}
			}
		}
		.fs-tabs__tab {
			margin-top: 0 !important;
		}
	}
}
