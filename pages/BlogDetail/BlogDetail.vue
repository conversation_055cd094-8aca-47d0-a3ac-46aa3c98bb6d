<template>
	<main :class="{ isTag: checkTitle }">
		<Nav v-if="hasData" :navData="navData" :tagId="feishuData.blogCategoryId" @setTagId="setTagId" />
		<Details v-if="hasData" :contentData="feishuData" :navData="navData" @gaPoint="gaPoint"></Details>
	</main>
</template>

<script setup lang="ts">
import Nav from "./components/Nav/index.vue";
import Details from "./components/Details/index.vue";
definePageMeta({
	layout: "common"
});
// console.log(1);

const route = useRoute();
const websiteStore = useWebsiteStore();
const localLink = useLocaleLink();
const runtimeConfig = useRuntimeConfig();

const { website } = storeToRefs(websiteStore);

const setMeta = useSetMeta();
const locale = useHtmlLang();

const navData = ref();
const contentData = ref();
const feishuData = ref();
const pageTotal = ref<number>(1);
const checkTitle = ref<string>();
const handlePages = (current: number) => {
	params.currentPage = current;
	navigateTo({ query: { ...route.query, page: current } });
};

const setTagId = (id: string) => {
	params.currentPage = 1;
	params.tagId = id;
};

const params = reactive({
	currentPage: route.query.page ? Number(route.query.page) : 1,
	tagId: route.query.tag_id ? route.query.tag_id : null
});
// console.log(params, "params");
const hasData = ref(false);
const loading = ref(false);
const fetchBlogData = async () => {
	const blogId = route.path.split(".html")[0].split("-").at(-1);
	loading.value = true;
	const websiteStore = useWebsiteStore();
	const { website } = storeToRefs(websiteStore);

	const [topData, details] = await Promise.all([
		useRequest.post(`/cms/api/fs/blog/index`),
		useRequest.post(`/cms/api/fs/blog/detail`, {
			data: {
				blogCategoryId: route.query.tag_id ? route.query.tag_id : null,
				id: blogId
			}
		})
	]);
	console.log(details, "barrybarrybarrybarrybarrybarrybarrybarrybarry");
	if (details?.data?.value?.data === null) {
		hasData.value = false;
		// console.log(localLink("/blog.html"), "rayrayrayrayrayrayrayrayrayrayrayray");
		// navigateTo(localLink("/blog.html"));
		// console.log("barry");
		// location.href = localLink(["cn", "en"].includes(website) ? "/blog.html" : `/${website}/blog.html`);
	} else {
		hasData.value = true;
		loading.value = false;
		navData.value = topData.data.value.data.categoryList;
		feishuData.value = details.data.value.data;
		console.log(details.data.value.data, "Details1");
		const siteNameObject = {
			uk: "FS.com United Kingdom",
			au: "FS.com Australia",
			mx: "FS.com México",
			sg: "FS.com Singapore",
			es: "FS.com España",
			jp: "FS.com 日本",
			fr: "FS.com France",
			de: "FS.com Deutschland",
			"de-en": "FS.com Europe",
			ru: "FS.com Россия",
			en: "FS.com",
			it: "FS.com Italia",
			cn: "飞速（FS）"
		};
		const isSiteNameKey = (key: string): key is keyof typeof siteNameObject => {
			return key in siteNameObject;
		};
		const metaData = {
			title: feishuData.value.metaTitle,
			description: feishuData.value.metaDescription,
			default_title: feishuData.value.metaTitle,
			default_description: feishuData.value.metaDescription,
			site_name: isSiteNameKey(website.value) ? siteNameObject[website.value] : "FS.com",
			og_image: feishuData.value.metaImg,
			locale: String(locale),
			langFilter: details?.data?.value?.data?.syncAticles
		};
		console.log("metaData", website, metaData);
		setMeta(metaData);
	}
};
await fetchBlogData();
useHeadMeta();
if (!hasData.value) {
	navigateTo(localLink(runtimeConfig.public.VITE_NUXT_DOMAIN + "/404.html"), {
		external: true
	});
}

const gaPoint = function (eventCategory: string, eventAction: string, eventLabel: string) {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory,
			eventAction,
			eventLabel,
			nonInteraction: false
		});
	}
};

onMounted(() => {
	if (feishuData.value?.title) {
		gaPoint("Resource_Blog_Content Page", "page_view", feishuData.value.title);
	}

	setTimeout(() => {
		useRequest.post(`/cms/api/fs/blog/detail`, {
			data: {
				blogCategoryId: route.query.tag_id ? route.query.tag_id : null,
				id: route.path.split(".html")[0].split("-").at(-1)
			}
		});
	}, 0);
});

watch(
	() => params,
	async newVal => {
		if (newVal) {
			await fetchBlogData();
		}
	},
	{ immediate: true, deep: true }
);
</script>

<style scoped lang="scss">
main {
	// padding-bottom: 40px;
	.check_title {
		@include contentWidth;
		margin-top: 40px;
		h3 {
			@include font20;
			color: $textColor1;
		}
	}
	&.isTag {
		:deep(.featured) {
			display: none;
		}
		:deep(> .more) {
			margin-top: 20px;
			.tit {
				display: none;
			}
		}
	}
}
</style>
