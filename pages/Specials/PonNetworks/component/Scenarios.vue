<template>
	<div class="content">
		<FsTabs center @change="changeIndex">
			<FsTabPane v-for="(item, index) in dataInfo.nav" :key="String(index)" :label="item" :name="`${index + 1}`">
				<div class="subContent">
					<div class="left">
						<template v-if="index == 1">
							<PhotoGallery class="second_img" :images="[scenariosImgUrl]" alt="image" />

							<!-- <img class="second_img" :src="scenariosImgUrl" alt="image" /> -->
						</template>
						<template v-else>
							<PhotoGallery :images="[scenariosImgUrl]" alt="image" />

							<!-- <img :src="scenariosImgUrl" alt="image" /> -->
						</template>
					</div>
					<div class="right">
						<div class="tit">{{ dataInfo.hightlights_txt }}</div>
						<ul>
							<li v-for="(v, vIndex) in dataInfo.nav_list[index].list" :key="vIndex">
								<div class="i-div">
									<i></i>
									<p class="i-tit">{{ v.tit }}</p>
								</div>
								<p class="i-txt">{{ v.txt }}</p>
							</li>
						</ul>
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>
<script lang="ts" setup>
import { FsTabs, FsTabPane } from "fs-design";
import PhotoGallery from "@/component/PhotoGallery/PhotoGallery.vue";

const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();

const props = defineProps({
	dataInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { device } = storeToRefs(deviceStore);
const activeIndex = ref<number>(0);

const scenariosImgUrl = computed(() => {
	const data = props.dataInfo.nav_list[activeIndex.value];
	if (device.value === "pc") {
		return data.img;
	} else if (device.value === "pad") {
		return data.img_pad;
	} else {
		return data.img_m;
	}
});
function changeIndex(info: any) {
	activeIndex.value = info.name - 1;
}
</script>
<style lang="scss" scoped>
.content {
	padding-top: 24px;
	:deep(.fs-tabs) {
		justify-content: center;
		.fs-tabs__nav {
			border-bottom: unset;
			.fs-tabs__scroll {
				padding-right: 0px;
				justify-content: flex-start;
			}
			.fs-tabs__tab {
				border-bottom: 1px solid #e5e5e5;
			}
		}
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
		.fs-tabs__item {
			max-width: unset;
		}
	}

	.subContent {
		display: flex;
		padding-top: 24px;
		> div {
			background: #fafafb;
		}
		img {
			width: 100%;
		}
		.left {
			width: 70%;
			border-radius: 8px;
			.second_img {
				padding: 28px 32px;
			}
			img {
				border-radius: 8px;
			}
		}
		.right {
			flex: 1;
			padding: 32px;
			margin-left: 24px;
			background-color: #fafafb;
			border-radius: 8px;
			.tit {
				@include font16;
				font-weight: 600;
				margin-bottom: 16px;
			}
			ul {
				li {
					display: flex;
					flex-direction: column;
					margin-bottom: 8px;
					.i-div {
						display: flex;
						flex-direction: row;
						.i-tit {
							@include font14;
							color: $textColor1;
						}
					}
					.i-txt {
						@include font12;
						color: $textColor2;
					}
					&:last-child {
						margin-bottom: 0;
					}
					i {
						flex-shrink: 0;
						width: 4px;
						height: 4px;
						border-radius: 4px;
						background-color: $textColor3;
						margin: 9px 8px 0 0;
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.subContent {
			flex-direction: column;
			.left {
				width: 100%;
				.second_img {
					padding: 12px 13px;
				}
			}
			.right {
				width: 100%;
				padding: 20px 20px 28px 20px;
				margin-left: 0;
				margin-top: 16px;
			}
		}
	}
}
</style>
