<template>
	<div class="content">
		<FsTabs center>
			<FsTabPane v-for="(item, index) in dataInfo.nav" :key="String(index)" :label="item" :name="`${index + 1}`">
				<div class="subContent">
					<div class="left">
						<!-- <img :src="dataInfo.nav_list[index].img" alt="image" /> -->
						<PhotoGallery :images="[dataInfo.nav_list[index].img]" alt="image" />
					</div>
					<div class="right">
						<h4 v-html="dataInfo.nav_list[index].name"></h4>
						<p v-for="(c, v) in dataInfo.nav_list[index].txt" :key="v" v-html="c"></p>
						<FsButton iconPlacement="suffix" text type="primary" @click="skip(dataInfo.nav_list[index].url)">
							<template #default>{{ dataInfo.relate_solution }}</template>
							<template #icon>
								<i class="iconfont iconfs_2020091147icon" />
							</template>
						</FsButton>
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>
<script lang="ts" setup>
import { FsTabs, FsTabPane, FsButton } from "fs-design";
import PhotoGallery from "@/component/PhotoGallery/PhotoGallery.vue";

const localeLink = useLocaleLink();

const props = defineProps({
	dataInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});
const skip = function (url: string) {
	location.href = localeLink(url);
};
</script>
<style lang="scss" scoped>
.content {
	:deep(.fs-tabs) {
		padding-top: 12px;
		justify-content: center;
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
	}

	.subContent {
		display: flex;
		padding-top: 24px;
		> div {
			background: #fafafb;
		}
		img {
			width: 100%;
		}
		.left {
			flex: 1;
		}
		.right {
			width: 334px;
			padding: 32px;
			margin-left: 24px;
			h4 {
				@include font16;
				padding-bottom: 16px;
			}
			p {
				color: $textColor2;
				@include font14;
				padding-bottom: 6px;
			}
			:deep(.fs-button--suffix) {
				margin-right: 0;
				font-size: 13px;
				color: $textColor1;
				margin-top: 5px;
			}
			:deep(.iconfont) {
				color: $textColor1;
				margin-top: 5px;
				font-size: 12px;
				font-weight: 400;
				line-height: 1;
				margin-left: 4px;
				text-align: left;
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.subContent {
			display: block;
			img {
				width: 100%;
				height: auto;
			}

			.right {
				width: 100%;
				padding: 20px;
				margin-left: 0px;
				margin-top: 16px;
			}
		}
	}
}
</style>
