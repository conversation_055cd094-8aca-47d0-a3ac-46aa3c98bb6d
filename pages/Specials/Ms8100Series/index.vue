<template>
	<div class="container">
		<Banner :bannerInfo="bannerInfo" @bannerClick="bannerClick" />
		<FsTabsAnchor v-model="activeValue" :data="dataValue" bgc-color="#eee" :text="buttonText" @jumpPage="jumpPage">
			<ContentWrapper
				v-for="(section, index) in sections"
				:id="getClassName(index)"
				:key="index"
				:title="section.title"
				:contents="section.contents"
				:contentAlign="section.isCenter ? 'center' : 'left'"
				:background="section.background">
				<component :is="section.component" v-if="section.component" v-bind="section.props" />
			</ContentWrapper>
		</FsTabsAnchor>
	</div>
</template>
<script lang="ts" setup>
import { FsTabsAnchor } from "fs-design";
import ContentWrapper from "../components/SpecialsContent.vue";
import Overview from "./components/Overview.vue";
import Benefits from "./components/Benefits.vue";
import CaseStudy from "./components/CaseStudy.vue";
import EnhanceMa from "./components/EnhanceMa.vue";
import Featured from "./components/Featured.vue";
import Resource from "./components/Resource.vue";
import Solution from "./components/Solution.vue";
import Banner from "@/component/Specials/Banner.vue";
const localeLang = useLocaleLang();
const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});
function getClassName(index: number) {
	let num = index;
	if (num === 3) {
		return `extra-${num}`;
	}
	if (num > 3) {
		num = num - 1;
	}
	return `content-${num}`;
}
const { languageData } = props;
const bannerInfo = computed(() => {
	return {
		id: 0,
		image: languageData.banner.img_pc,
		padImage: languageData.banner.img_pad,
		mobileImage: languageData.banner.img_m,
		title: languageData.banner.title,
		subtitle: languageData.banner.desc,
		buttonStr: languageData.banner.btn.txt,
		height: "320"
	};
});

const sections = computed(() => [
	{
		title: languageData.overview.tit,
		contents: [languageData.overview.desc],
		background: "#ffffff",
		component: Overview,
		isCenter: true,
		props: { dataInfo: languageData.overview }
	},
	{
		title: languageData.benefits.tit,
		contents: [],
		background: "#fafafb",
		component: Benefits,
		props: { dataInfo: languageData.benefits }
	},
	{
		title: languageData.products.tit,
		contents: [languageData.products.desc],
		background: "#ffffff",
		component: Featured,
		isCenter: true,
		props: { dataList: languageData.products.list }
	},
	{
		title: languageData.network.tit,
		contents: [languageData.network.desc],
		background: "#fafafb",
		component: EnhanceMa,
		isCenter: true,
		props: { dataInfo: languageData.network }
	},
	{
		title: languageData.solutions.tit,
		contents: [],
		background: "#ffffff",
		component: Solution,
		props: { dataInfo: languageData.solutions }
	},
	{
		title: languageData.case.tit,
		contents: [],
		background: "#fafafb",
		component: CaseStudy,
		props: { dataInfo: languageData.case }
	},
	{
		title: languageData.resources.tit,
		contents: [],
		background: "#ffffff",
		component: Resource,
		props: { dataList: languageData.resources.list }
	}
]);
const length = languageData.nav.length;
const dataValue = ref(
	Array.from({ length }, (_, index) => ({
		title: languageData.nav[index],
		name: `#content-${index}`
	}))
);
const buttonText = reactive([localeLang("header.Contact_Us")]);
const localeLink = useLocaleLink();
const activeValue = ref("#content-0");
const jumpPage = () => {
	location.href = localeLink("/contact_sales_mail.html");
};

function bannerClick() {
	window.open(languageData.banner.btn.link);
}
</script>
<style lang="scss" scoped>
:deep(.fs-banner) {
	@media (max-width: 768px) {
		height: 440px !important;
	}
	.common {
		justify-content: flex-start !important;
		@media screen and (min-width: 768px) and (max-width: 1024px) {
			width: calc(100% - 96px) !important;
		}
		@media (max-width: 768px) {
			width: calc(100% - 48px) !important;
			align-items: normal !important;
		}
		.banner_txt {
			@media (max-width: 768px) {
				max-width: 100% !important;
				margin-top: 56px !important;
			}
			h1 {
				padding-left: 0 !important;
				text-align: left !important;
			}
			p {
				padding-left: 0 !important;
				text-align: left !important;
				@media (max-width: 1024px) {
					max-width: 400px !important;
				}
				@media (max-width: 768px) {
					@include font14;
				}
			}
			.btn {
				text-align: left !important;
				.fs-button {
					&:hover {
						span {
							text-decoration: none !important;
						}
						.fs-button--suffix {
							text-decoration: underline !important;
						}
						background-color: rgba(255, 255, 255, 0) !important;
					}
					margin-top: 16px !important;
					border: none !important;
					height: 22px !important;
					line-height: 22px !important;
					padding: 0 !important;
					.fs-button--suffix {
						@include font14;
						font-weight: normal;
					}
					.iconfont {
					}
				}
			}
		}
	}
}
:deep(.fs-affix) {
	.fs-tabs {
		background: #eee;
	}
	.fs-tabs__nav {
		width: 84vw;
		max-width: 1200px;
		margin: 0 auto;
		@media (max-width: 1024px) {
			width: 96vw;
		}
		@media (max-width: 768px) {
			width: 100vw;
		}
		.fs-tabs__toolBoxM {
			background: #fff !important;
		}
	}
	.fs-tabs__scroll {
		@media (max-width: 1220px) {
			width: 960px;
		}
		@media (max-width: 960px) {
			width: 100%;
			padding: unset;
		}
		@media (max-width: 768px) {
			width: 100%;
			padding: unset;
		}
	}
	.fs-tabs__item {
		font-size: 14px;
	}
	.fs-tabs__itemM {
		padding: 16px 0 !important;
		&::after {
			bottom: -16px !important;
		}
	}
	.fs-tabs__more {
		display: none;
		@media (max-width: 1200px) {
			display: flex !important;
			right: 117px !important;
		}
		@media (max-width: 768px) {
			right: 110px !important;
			padding: 0 12px !important;
		}
	}
	.fs-tabs__af {
		display: none;
		@media (max-width: 1200px) {
			display: flex !important;
			right: 163px !important;
		}
		@media (max-width: 768px) {
			right: 162px !important;
		}
	}
	.fs-button--anchor {
		@media (max-width: 768px) {
			width: max-content !important;
			padding: 6px 12px !important;
		}
		span {
			font-size: 14px !important;
			line-height: 22px !important;
		}
	}
}
.container {
	:deep(.w1200) {
		@media (max-width: 1220px) {
			width: 976px;
		}
		@media (max-width: 1024px) {
			width: 100%;
			padding-left: 24px;
			padding-right: 24px;
		}
		@media (max-width: 960px) {
			width: 100%;
			padding-left: 16px;
			padding-right: 16px;
		}
		.content {
			h2 {
				@include font20;
			}
		}
	}
}
</style>
