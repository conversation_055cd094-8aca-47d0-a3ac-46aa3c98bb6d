<template>
	<div class="content_box">
		<p class="title">{{ solution.title }}</p>
		<div class="grid_div">
			<div class="left_div">
				<img :src="bannerImg" alt="banner image" />
			</div>
			<div class="right_div">
				<div v-for="(item, index) in solution.list" :key="index" class="text_div">
					<div>
						<i></i>
						<p>{{ item.title }}</p>
					</div>
					<p class="describe">{{ item.text }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	solution: {
		type: Object,
		default() {
			return {};
		}
	}
});
const useDevice = useDeviceStore();
const { screenWidth } = storeToRefs(useDevice);
const bannerImg = computed(() => {
	if (screenWidth.value > 1024) {
		return props.solution.image.pc;
	} else if (screenWidth.value > 768) {
		return props.solution.image.pad;
	} else {
		return props.solution.image.m;
	}
});
</script>

<style lang="scss" scoped>
.content_box {
	@include width1200;
	padding: 40px 0 48px;
	font-size: 0;
	.title {
		@include font24;
		font-weight: 600;
		color: $textColor1;
		text-align: center;
		padding: 0 120px;
	}
	.grid_div {
		display: grid;
		grid-template-columns: auto 334px;
		column-gap: 28px;
		padding-top: 24px;
		.left_div {
			background-color: $bgColor3;
			img {
				width: 100%;
				height: auto;
			}
		}
		.right_div {
			background-color: $bgColor3;
			padding: 32px;
			.text_div {
				&:not(:first-child) {
					padding-top: 16px;
				}
				div {
					display: flex;
					align-items: center;
					i {
						width: 4px;
						height: 4px;
						border-radius: 2px;
						background-color: $textColor3;
						margin-right: 8px;
					}
					p {
						@include font16;
						font-weight: 600;
						color: $textColor1;
					}
				}
				.describe {
					@include font14;
					font-weight: 400;
					color: $textColor3;
					padding-top: 8px;
					margin-left: 12px;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.content_box {
		width: 100%;
		padding: 40px 24px 48px;
		.title {
			padding: 0;
		}
		.grid_div {
			grid-template-columns: auto 268px;
			.right_div {
				.text_div {
					&:not(:first-child) {
						padding-top: 8px;
					}
					div {
						p {
							@include font14;
							font-weight: 400;
						}
					}
					.describe {
						@include font12;
						padding-top: 4px;
					}
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.content_box {
		padding: 36px 16px;
		.title {
			@include font20;
		}
		.grid_div {
			grid-template-columns: auto;
			.right_div {
				padding: 20px;
			}
		}
	}
}
</style>
