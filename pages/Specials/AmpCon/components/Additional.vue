<template>
	<div class="additional_div">
		<div class="common">
			<p class="title">{{ additional.tit }}</p>
			<div v-for="(item, index) in additional.list" :key="index" class="contain">
				<div class="lt">
					<img :src="item.img" :alt="item.tit1" />
				</div>
				<div class="rt">
					<h3>{{ item.tit1 }}</h3>
					<p>{{ item.desc }}</p>
					<a v-for="btn in item.btns" :key="btn.url" class="additional" href="javascript:void(0)" @click="gaClick(btn)">
						<span>{{ btn.btn }}</span>
						<i class="iconfont">&#xe703;</i>
					</a>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	additional: {
		type: Object,
		default: () => {}
	}
});
const localeLink = useLocaleLink();
const gaClick = (btn: any) => {
	if (btn.url) {
		if (btn.url.includes("product_type=ampcon-t")) {
			gaEventUE("policy_service", "AmpCon Management Platform_Optical Networking");
		}
	}
	window.location.href = window.location.origin + localeLink(btn.url);
};
function gaEventUE(eventAction: string, eventLabel: string) {
	const obj = {
		event: "uaEvent",
		eventCategory: `FS Modify Page`,
		eventAction,
		eventLabel,
		nonInteraction: false
	};
	window.dataLayer && window.dataLayer.push(obj);
}
</script>

<style lang="scss" scoped>
.additional_div {
	padding: 40px 0;
	background: #fafafb;
	.common {
		@include width1200;
	}
	.title {
		@include font20;
		font-weight: 600;
		color: $textColor1;
		text-align: center;
		padding: 0 120px;
	}
	.contain {
		color: $textColor1;
		margin-top: 36px;
		display: flex;
		background: #fff;
		&:nth-child(2n) {
			flex-direction: row-reverse;
			.lt {
				img {
					border-radius: 0 8px 8px 0;
				}
			}
		}
		> div {
			width: 50%;
		}
		.lt {
			img {
				display: block;
				width: 100%;
				border-radius: 8px 0 0 8px;
			}
		}
		.rt {
			padding: 0 28px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			border-radius: 0 8px 8px 0;
			h3 {
				@include font14;
				font-weight: 600;
			}
			p {
				@include font12;
				color: $textColor3;
				margin: 8px 0;
			}
			.additional {
				cursor: pointer;
				display: flex;
				align-items: center;
				color: $textColor1;
				&:hover {
					text-decoration: none;
				}
				span {
					@include font13;
					&:hover {
						text-decoration: underline;
					}
				}
				.iconfont {
					font-size: 12px;
					padding-left: 4px;
					line-height: 1;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.additional_div {
		padding: 40px 24px;
		.common {
			width: 100%;
		}
		.title {
			padding: 0;
		}
		.describe {
			padding: 12px 0 24px;
		}
	}
}

@media (max-width: 768px) {
	.additional_div {
		padding: 36px 16px 36px;
		.contain {
			flex-direction: column;
			> div {
				width: 100%;
			}
			.lt {
				img {
					border-radius: 8px 8px 0 0;
				}
			}
			.rt {
				padding: 20px 20px 28px;
				border: 1px solid $borderColor2;
				border-top: none;
				border-radius: 0 0 8px 8px;
			}
		}
	}
}
</style>
