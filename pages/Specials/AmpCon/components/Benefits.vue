<template>
	<div>
		<div v-for="(item, index) in benefits.list" :key="index" class="benefits_box" :class="{ gray: index / 2 === 1 || index === 0 }">
			<div v-if="index === 1" class="common">
				<h3 class="title">{{ item.title }}</h3>
				<p class="describe" v-html="item.desc"></p>
				<div class="detail">
					<div class="left">
						<div v-for="(v, i) in item.left" :key="i" class="cont">
							<img :src="v.icon" :alt="v.tit" />
							<h3 class="tit">{{ v.tit }}</h3>
							<p class="txt">{{ v.txt }}</p>
						</div>
					</div>
					<div class="right">
						<img :src="item.img" :alt="item.title" />
					</div>
				</div>
			</div>
			<div v-else class="common">
				<h3 class="title">{{ item.title }}</h3>
				<p v-if="item.textUrl" class="describe" v-html="item.desc.replace('XXXXX', localeLink(item.textUrl))"></p>
				<p v-else class="describe" v-html="item.desc"></p>
				<img class="img" :src="item.img" :alt="item.title" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	benefits: {
		type: Object,
		default: () => {}
	}
});
const localeLink = useLocaleLink();
</script>

<style lang="scss" scoped>
.benefits_box {
	padding: 40px 0;
	background: #fff;
	&.gray {
		background: #fafafb;
	}
	.common {
		@include width1200;
	}
	.title {
		@include font20;
		font-weight: 600;
		color: $textColor1;
		text-align: center;
		padding: 0 120px;
	}
	.describe {
		@include font14;
		font-weight: 400;
		color: $textColor2;
		padding: 12px 0 0;
		max-width: 960px;
		margin: 0 auto;
		text-align: center;
	}
	.img {
		display: block;
		width: 100%;
		margin-top: 24px;
		border-radius: 8px;
	}
	.detail {
		display: flex;
		align-items: center;
		margin-top: 24px;
		.left {
			flex: 1;
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20px;
			.cont {
				.tit {
					@include font14;
					font-weight: 600;
					color: $textColor1;
					margin-top: 8px;
				}
				.txt {
					@include font12;
					font-weight: 400;
					color: $textColor3;
					margin-top: 4px;
				}
				img {
					width: 48px;
					height: 48px;
				}
			}
		}
	}
	.right {
		width: 485px;
		margin-left: 36px;
		img {
			width: 100%;
			border-radius: 8px;
		}
	}
}
@media (max-width: 1024px) {
	.benefits_box {
		padding: 40px 24px;
		.common {
			width: 100%;
		}
		.title {
			padding: 0;
		}
		.describe {
			padding: 12px 0 0;
		}
		.detail {
			margin-top: 36px;
		}
		.right {
			width: 388px;
		}
	}
}

@media (max-width: 768px) {
	.benefits_box {
		padding: 36px 16px;
		.describe {
			padding: 12px 0 0;
		}
		.detail {
			flex-direction: column;
		}
		.right {
			width: 100%;
			margin-left: 0;
			margin-top: 24px;
		}
	}
}
</style>
