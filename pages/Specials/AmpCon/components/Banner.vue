<template>
	<div>
		<div class="banner" :style="{ backgroundImage: `url(${bannerImg})` }">
			<div class="banner_txt">
				<div class="text">
					<h3 v-html="banner.title"></h3>
					<p v-html="banner.desc"></p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	banner: {
		type: Object,
		default: () => {}
	}
});
const useDevice = useDeviceStore();
const { screenWidth } = storeToRefs(useDevice);
const bannerImg = computed(() => {
	if (screenWidth.value > 1024) {
		return props.banner.img_pc;
	} else if (screenWidth.value > 768) {
		return props.banner.img_pad;
	} else {
		return props.banner.img_m;
	}
});
</script>

<style lang="scss" scoped>
.banner {
	width: 100%;
	height: 320px;
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;

	.banner_txt {
		@include width1200;
		height: 100%;
		display: flex;
		align-items: center;
	}
	.text {
		width: 750px;
	}
	h3 {
		@include font32;
		font-weight: 600;
		color: $textColor6;
		margin-bottom: 16px;
	}
	p {
		@include font16;
		color: $textColor6;
	}
}
@media (max-width: 1024px) {
	.banner {
		.text {
			width: 600px;
		}
	}
}
@media (max-width: 768px) {
	.banner {
		height: 440px;
		.banner_txt {
			align-items: flex-start;
			padding-top: 56px;
			width: calc(100% - 48px);
		}
		h3 {
			@include font24;
			margin-bottom: 12px;
		}
		p {
			@include font16;
		}
	}
}
</style>
