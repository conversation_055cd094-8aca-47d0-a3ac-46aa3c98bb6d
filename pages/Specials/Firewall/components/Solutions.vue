<template>
	<div class="content">
		<FsTabs center>
			<FsTabPane v-for="(item, index) in dataInfo.navList" :key="String(index)" :label="item" :name="`${index + 1}`">
				<div class="subContent">
					<div class="left">
						<img :src="bannerImg(dataInfo.highlights.content[index].image)" :alt="dataInfo?.highlights?.title" />
					</div>
					<div class="right">
						<p class="highlights_title">{{ dataInfo.highlights.title }}</p>
						<div v-for="(subItem, subIndex) in dataInfo.highlights.content[index].list" :key="subIndex" class="text_div" :class="{ text_div_first: subIndex === 0 }">
							<div>
								<i></i>
								<p>{{ subItem.title }}</p>
							</div>
							<p class="describe">{{ subItem.desc }}</p>
						</div>
						<div class="button_div">
							<a :href="dataInfo.related.list[index]" target="_blank">
								<FsButton iconPlacement="suffix" text type="primary">
									<template #default>{{ dataInfo.related.title }}</template>
									<template #icon>
										<i class="iconfont iconfs_2020091147icon" />
									</template> </FsButton
							></a>
						</div>
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>

<script lang="ts" setup>
import { FsTabs, FsTabPane, FsButton } from "fs-design";
const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();
const props = defineProps({
	dataInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { device } = storeToRefs(deviceStore);

function bannerImg(info: any) {
	if (device.value === "pc") {
		return info.pc;
	} else if (device.value === "pad") {
		return info.pad;
	}
	return info.m; // default image
}
</script>
<style lang="scss" scoped>
.content {
	:deep(.fs-tabs) {
		padding-top: 12px;
		justify-content: center;
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
		.fs-tabs__be {
			display: none;
		}
	}

	.subContent {
		padding-top: 24px;
		display: grid;
		grid-template-columns: auto 334px;
		column-gap: 28px;
		> div {
			background: #fafafb;
		}
		.left {
			background-color: $bgColor3;
			img {
				width: 100%;
				height: auto;
			}
		}
		.right {
			background-color: $bgColor3;
			padding: 32px;
			.highlights_title {
				@include font16;
				font-weight: 600;
				color: $textColor1;
				margin-bottom: 16px;
			}
			.text_div {
				padding-top: 8px;
				&.text_div_first {
					padding-top: 0;
				}
				div {
					display: flex;
					i {
						width: 4px;
						height: 4px;
						border-radius: 2px;
						background-color: $textColor3;
						margin: 10px 8px 0 0;
					}
					p {
						@include font14;
						font-weight: 400;
						color: $textColor1;
					}
				}
				.describe {
					@include font12;
					font-weight: 400;
					color: $textColor3;
					padding-top: 4px;
					margin-left: 12px;
				}
			}
			.button_div {
				margin: 16px 0 0 12px;

				:deep(.fs-button--suffix) {
					margin-right: 0;
					font-size: 13px;
					color: $textColor1;
				}
				:deep(.iconfont) {
					color: $textColor1;
					font-size: 12px;
					font-weight: 400;
					line-height: 1;
					margin-left: 4px;
					text-align: left;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.subContent {
			display: block;
			img {
				width: 100%;
				height: auto;
			}

			.right {
				width: 100%;
				padding: 20px;
				margin-left: 0px;
				margin-top: 16px;
			}
		}
	}
}
</style>
