<template>
	<div class="container">
		<!-- Top Image -->
		<Banner :bannerInfo="bannerInfo" @bannerClick="bannerClick" />

		<!-- content -->
		<FsTabsAnchor v-model="activeValue" :data="dataValue" bgc-color="#fff" @change="change">
			<ContentWrapper
				v-for="(section, index) in sections"
				:id="`content-${index}`"
				:key="index"
				:title="section.title"
				:contents="section.contents"
				:isDistance="section.isDistance"
				:attr="section.attr"
				:contentAlign="section.isCenter ? 'center' : 'left'"
				:background="section.background">
				<component :is="section.component" v-if="section.component" v-bind="section.props" />
			</ContentWrapper>
		</FsTabsAnchor>
	</div>
</template>
<script lang="ts" setup>
import { FsTabsAnchor } from "fs-design";
import ContentWrapper from "../components/SpecialsContent.vue";
import Overview from "./components/Overview.vue";
import Benifits from "./components/Benifits.vue";
import Help from "./components/Help.vue";
import Case from "./components/Case.vue";
import Service from "./components/Service.vue";
import Solutions from "./components/Solutions.vue";
import Banner from "@/component/Specials/Banner.vue";

const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { languageData } = props;

const bannerInfo = computed(() => {
	return {
		id: 0,
		image: languageData.banner.image.pc,
		padImage: languageData.banner.image.pad,
		mobileImage: languageData.banner.image.m,
		title: languageData.banner.title,
		buttonStr: languageData.banner.text,
		height: "200"
	};
});

const sections = computed(() => [
	{
		title: languageData.overview.title,
		contents: [languageData.overview.text],
		background: "#ffffff",
		component: Overview,
		props: { dataInfo: languageData.overview.content }
	},
	{
		title: languageData.benefits.title,
		contents: [languageData.benefits.text],
		background: "#fafafb",
		component: Benifits,
		isCenter: true,
		props: { dataList: languageData.benefits.list }
	},
	{
		title: languageData.solution.title,
		contents: [],
		background: "#ffffff",
		component: Solutions,
		props: { dataInfo: languageData.solution }
	},
	{
		title: languageData.service.title,
		contents: [languageData.service.text],
		background: "#fafafb",
		component: Service,
		isCenter: true,
		props: { dataList: languageData.service.list }
	},
	{
		title: languageData.case.title,
		contents: [],
		background: "#ffffff",
		component: Case,
		isDistance: false,
		props: { dataInfo: languageData.case }
	},
	{
		title: "",
		contents: [],
		background: "#fafafb",
		component: Help,
		isDistance: false,
		attr: {
			class: "content-padding"
		},
		props: { dataInfo: languageData.contact_us }
	}
]);
const length = languageData.tab.length;
const dataValue = ref(
	Array.from({ length }, (_, index) => ({
		title: languageData.tab[index],
		name: `#content-${index}`
	}))
);
const activeValue = ref("#content-0");
function change(index: number) {
	// console.log(val)
}

function bannerClick() {
	window.open(languageData.banner.href);
}
</script>
<style lang="scss" scoped>
:deep(.fs-affix) {
	// @media (max-width: 1024px) {
	// 	position: sticky;
	// 	top: 48px;
	// }
	.fs-tabs {
		background-color: #fff !important;
	}
	.fs-tabs__scroll {
		background-color: unset !important;
		width: 84vw;
		max-width: 1200px;
		margin: 0 auto;
		@media (max-width: 1220px) {
			width: 960px;
		}
		@media (max-width: 960px) {
			width: 100%;
			padding: 0 16px;
		}
	}
	.fs-tabs__item {
		padding: 12px 0 !important;
		font-size: 16px;
		&::after {
			bottom: -12px !important;
		}
	}
	.fs-tabs__itemM {
		padding: 12px 0 !important;
		&::after {
			bottom: -12px !important;
		}
	}
	.fs-tabs__more {
		background-color: #eee !important;
	}
}
</style>
