<template>
	<div class="container">
		<Banner :bannerInfo="bannerInfo" @bannerClick="bannerClick" />
		<ContentWrapper
			v-for="(section, index) in sections"
			:id="`content-${index}`"
			:key="index"
			:class="{ content_text: index === 0 || index === 2 || index === 3 || index === 4 || index === 5 }"
			:title="section.title"
			:contents="section.contents"
			contentAlign="center"
			:background="section.background">
			<component :is="section.component" v-if="section.component" v-bind="section.props" />
		</ContentWrapper>
	</div>
</template>
<script lang="ts" setup>
import Overview from "./components/Overview.vue";
import Category from "./components/Category.vue";
import Solutions from "./components/Solutions.vue";
import Measured from "./components/Measured.vue";
import Tested from "./components/Tested.vue";
import Testing from "./components/Testing.vue";
import Products from "./components/Products.vue";
import RelatedPosts from "./components/RelatedPosts.vue";
import Resources from "./components/Resources.vue";
import Banner from "@/component/Specials/Banner.vue";
import ContentWrapper from "@/component/Specials/SpecialsContent.vue";

const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { languageData } = props;

const bannerInfo = computed(() => {
	return {
		id: 0,
		image: languageData.banner.pc,
		padImage: languageData.banner.pad,
		mobileImage: languageData.banner.m,
		title: languageData.banner.title,
		buttonStr: languageData.banner.btn,
		height: "200"
	};
});
function bannerClick() {
	gaEventUE("policy_service", "Optical Transceivers & Cables");
	location.href = languageData.banner.url; // 当前页跳转
	console.log("location.href====", location.href);
}
function gaEventUE(eventAction: string, eventLabel: string) {
	const obj = {
		event: "uaEvent",
		eventCategory: `FS Modify Page`,
		eventAction,
		eventLabel,
		nonInteraction: false
	};
	window.dataLayer && window.dataLayer.push(obj);
}

const sections = computed(() => [
	{
		title: "",
		contents: [],
		background: "#ffffff",
		component: Overview,
		props: { dataInfo: languageData.overview }
	},
	{
		title: "",
		contents: [],
		background: "#ffffff",
		component: Category,
		props: { dataList: languageData.category }
	},
	{
		title: languageData.solutions.title,
		contents: [languageData.solutions.subTitle],
		background: "#FAFBFB",
		component: Solutions,
		props: { dataList: languageData.solutions.tagData }
	},
	{
		title: languageData.tested.title,
		contents: [languageData.tested.subTitle],
		background: "#ffffff",
		component: Tested,
		props: { dataList: languageData.tested.text }
	},
	{
		title: languageData.original.title,
		contents: [languageData.original.content],
		background: "#FAFBFB",
		component: Measured,
		props: { dataInfo: languageData.original }
	},
	{
		title: languageData.testing.title,
		contents: [],
		background: "#ffffff",
		component: Testing,
		props: { dataList: languageData.testing.list }
	},
	{
		title: languageData.productsList.title,
		contents: [],
		background: "#FAFBFB",
		component: Products,
		props: { dataInfo: languageData.productsList }
	},
	{
		title: languageData.resources.title,
		contents: [],
		background: "#ffffff",
		component: Resources,
		props: { dataList: languageData.resources.cards }
	}
]);
</script>
<style lang="scss" scoped>
/* Your SCSS styles here */
.container {
	// .content_text {
	// 	:deep(h2) {
	// 		font-size: 20px;
	// 		margin-bottom: 12px;
	// 	}
	// }
	:deep(.fs-banner) {
		@media (max-width: 768px) {
			height: 233px;
		}
		.common {
			.banner_txt {
				h1 {
					@media (max-width: 768px) {
						font-size: 24px;
						line-height: 32px;
					}
				}
				.btn {
					.fs-button {
						&:hover {
							.fs-button--suffix {
								text-decoration: underline !important;
							}

							background-color: rgba(255, 255, 255, 0) !important;
						}
						margin-top: 24px !important;
						border: none !important;
						height: 22px !important;
						line-height: 22px !important;
						padding: 0 !important;
						.fs-button--suffix {
							@include font14;
							font-weight: normal;
							color: white !important;
						}
						.iconfont {
							color: white !important;
						}
					}
				}
			}
		}
	}
}
:deep(#content-0) {
	.contentWrapper {
		border-bottom: 1px solid #dee0e3;
		padding-bottom: 36px;
	}
}
:deep(#content-1) {
	.containerInner {
		width: 100%;
		max-width: 100%;
		.contentWrapper {
			padding-top: 36px;
		}
	}
}
:deep(#content-6) {
	.containerInner {
		@media (max-width: 1220px) {
			width: 94vw;
		}
		@media (max-width: 1024px) {
			width: 100%;
		}
	}
	.contentWrapper {
		@media (max-width: 768px) {
			padding-bottom: 36px;
		}
	}
}
</style>
