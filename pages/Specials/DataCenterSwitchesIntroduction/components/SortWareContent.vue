<template>
	<div class="common-width">
		<h3>{{ software.title }}</h3>
		<FsTabs center class="software-nav">
			<FsTabPane v-for="(value, index) in software.plate_list" :key="String(index)" :label="value" :name="`${index + 1}`" @changeTab="softActive">
				<div class="software-content">
					<div class="des-box">
						{{ software.plate_c_list[index].des }}
					</div>
					<div class="img-box">
						<img :src="software_img[index]" :alt="software.plate_c_list[index].des" />
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>

<script lang="ts" setup>
import { FsTabs, FsTabPane } from "fs-design";

const props = defineProps({
	software: {
		type: Object,
		default() {
			return {};
		}
	}
});
const software_img = [
	"https://img-en.fs.com/includes/templates/fiberstore/images/specials/data_center_switches_introduction/evpn-vxlan.jpg",
	"https://img-en.fs.com/includes/templates/fiberstore/images/specials/data_center_switches_introduction/stacking-mlag.jpg",
	"https://img-en.fs.com/includes/templates/fiberstore/images/specials/data_center_switches_introduction/roce.jpg"
];
// 切换soft一级按钮
const softActive = function (index: number) {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Specials Page",
			eventAction: `Software_module`,
			eventLabel: [`What's New`, `Benefits`, `Software`, `Products`, `Solutions`, `Services`, `Resources`][index],
			nonInteraction: false
		});
};
</script>

<style lang="scss" scoped>
:deep(.fs-tabs) {
	.fs-tabs__item {
		font-size: 16px;
		padding: 12px 0 !important;
		&::after {
			bottom: -12px !important;
		}
	}
	@media (max-width: 1220px) {
		.fs-tabs__content {
			padding-left: 0px;
			padding-right: 0px;
		}
	}
}
h3 {
	@include font20;
	font-weight: 600;
	color: #19191a;
	text-align: center;
}
.common-width {
	margin: 0 auto;
	max-width: 1200px;
	width: 94%;
	.software-nav {
		margin: 24px 0;
	}
	.software-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		.des-box {
			@include font14;
			color: #707070;
			max-width: 960px;
			margin-bottom: 24px;
			margin-top: 24px;
			text-align: center;
			a {
				text-decoration: none;
				display: inline-flex;
				align-items: center;
				margin-left: 12px;
				.text {
					@include font14;
					margin-right: 8px;
					&:hover {
						text-decoration: underline;
					}
				}
				.iconfont {
					font-size: 12px;
				}
				&.has-two {
					margin-bottom: 8px;
				}
			}
		}
		.img-box {
			max-width: 1200px;
			img {
				width: 100%;
				height: auto;
				border-radius: 8px;
			}
		}
	}
}

@media (max-width: 1220px) {
	.common-width {
		padding: 0 24px;
		width: 100%;
		.software-content {
			.des-box {
				max-width: 800px;
				text-align: left;
			}
			.img-box {
				width: 100%;
				max-width: 1200px;
				img {
					width: 100%;
					height: auto;
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.common-width {
		padding: 0px 16px;
	}
	h3 {
		font-size: 20px;
		line-height: 30px;
	}
	.common-width {
		.software-nav {
			margin: 24px 0 16px 0;
		}
		:deep(.fs-tabs) {
			.fs-tabs__nav {
				width: max-content;
				margin: 0 auto;
			}
		}
		.software-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			.des-box {
				@include font14;
				margin-bottom: 26px;
				text-align: left;
			}
			&:nth-of-type(3),
			&:nth-of-type(4) {
				.des-box {
					a {
						margin-left: 0;
					}
				}
			}
		}
	}
}
</style>
