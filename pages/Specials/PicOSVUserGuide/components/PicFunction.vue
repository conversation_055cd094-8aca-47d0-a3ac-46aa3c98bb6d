<template>
	<div class="container" :style="{ backgroundImage: `url(${bannerImg})` }">
		<div class="introduction">
			<p class="title">{{ recommendations.title }}</p>
			<FsTabs center bgcColor="#ffffff00" @change="changeTab">
				<FsTabPane v-for="(item, index) in recommendations.nav" :key="String(index)" :label="item" :name="item">
					<div class="tab_cont">
						<p class="desc" :class="{ desc_more: !isShowMoreRef }">{{ recommendations.list[index].desc }}</p>
						<div class="more" @click="moreClick">
							<span>{{ isShowMoreRef ? localeLang("header.See_less") : localeLang("header.See_more") }}</span>
							<i :class="['iconfont', isShowMoreRef ? 'rotate' : '']">&#xe704;</i>
						</div>
						<div class="content_div">
							<div>
								<div class="industries">
									<p class="ind_title">{{ recommendations.list[index].industries.title }}</p>
									<div class="ind_div">
										<div v-for="(indItem, indIndex) in recommendations.list[index].industries.lists" :key="indIndex" class="ind_item">
											<img v-lazy="indItem.icon" alt="" />
											<p>{{ indItem.title }}</p>
										</div>
									</div>
								</div>
								<div class="experience">
									<p class="exp_title">{{ recommendations.list[index].experience.title }}</p>
									<div class="exp_div">
										<a v-for="(recItem, recIndex) in recommendations.list[index].experience.lists" :key="recIndex" :href="recItem.href" target="_blank" class="exp_item">
											<span>{{ recItem.title }}</span>
											<i class="iconfont iconfs_2022111601icon" />
										</a>
									</div>
								</div>
							</div>
							<div class="img_div">
								<!-- <img v-lazy="recommendations.list[index].image" alt="" /> -->
								<PhotoGallery :images="[recommendations.list[index].image]" alt="" />
							</div>
						</div>
					</div>
				</FsTabPane>
			</FsTabs>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { FsTabs, FsTabPane } from "fs-design";
import PhotoGallery from "@/component/PhotoGallery/PhotoGallery.vue";

const props = defineProps({
	recommendations: {
		type: Object,
		default() {
			return {};
		}
	}
});
const useDevice = useDeviceStore();
const { screenWidth } = storeToRefs(useDevice);
const localeLang = useLocaleLang();

// See more 按钮的默认状态
const isShowMoreRef = ref(false);
function moreClick() {
	isShowMoreRef.value = !isShowMoreRef.value;
}

// tabs 默认选中
const selectIndexRef = ref(0);
function changeTab(props: any, index: number) {
	if (index !== selectIndexRef.value) {
		isShowMoreRef.value = false;
	}
	selectIndexRef.value = index;
}

const bannerImg = computed(() => {
	if (screenWidth.value > 1024) {
		return props.recommendations.bgImage.pc;
	} else if (screenWidth.value > 768) {
		return props.recommendations.bgImage.pad;
	} else {
		return props.recommendations.bgImage.m;
	}
});
</script>

<style lang="scss" scoped>
.container {
	padding: 40px 48px;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;

	.introduction {
		max-width: 1200px;
		margin: 0 auto;
		color: $textColor1;

		.title {
			@include font20;
			text-align: center;
			font-weight: 600;
			padding-bottom: 24px;
		}

		:deep(.fs-tabs) {
			justify-content: center;

			.fs-tabs__nav {
				height: auto;
			}

			.fs-tabs__tab {
				// border-bottom: 1px solid #e5e5e5;
			}

			.fs-tabs__scroll {
				display: flex;
				justify-content: center;
			}

			.fs-tabs__more {
				background-color: #f1f3f6 !important;
				// height: 100%;
			}

			.fs-tabs__content {
				padding: 20px 0 0;
				display: flex;
				justify-content: center;

				.tab_cont {
					max-width: 960px;
					.desc {
						@include font12;
						text-align: center;
						color: $textColor2;
						&.desc_more {
							overflow: hidden;
							text-overflow: ellipsis;
							line-clamp: 3;
							display: -webkit-box;
							-webkit-line-clamp: 3;
							-webkit-box-orient: vertical;
						}
					}

					.more {
						display: flex;
						justify-content: center;
						align-items: center;
						padding: 8px;
						@include font13;
						color: $textColor5;
						width: fit-content;
						margin: auto;
						cursor: pointer;

						&:hover {
							span {
								text-decoration: underline;
							}
						}

						.iconfont {
							width: 12px;
							height: 12px;
							font-size: 12px;
							display: flex;
							align-items: center;
							margin-left: 4px;
							transition: all 0.3s;

							&.rotate {
								transform: rotateX(-180deg);
							}
						}
					}

					.content_div {
						margin-top: 12px;
						padding-top: 12px;
						display: grid;
						gap: 24px;
						grid-template-columns: 468px auto;

						.industries {
							.ind_title {
								@include font14;
								color: $textColor2;
								text-align: left;
								margin-bottom: 12px;
							}

							.ind_div {
								display: flex;
								flex-wrap: wrap;
								gap: 12px;

								.ind_item {
									background: $bgColor6;
									padding: 6px 8px;
									border-radius: 4px;
									display: flex;
									align-items: center;

									img {
										width: 16px;
										height: 16px;
									}

									p {
										@include font12;
										color: $textColor1;
										margin-left: 4px;
									}
								}
							}
						}

						.experience {
							margin-top: 40px;

							.exp_title {
								@include font14;
								color: $textColor2;
								text-align: left;
								margin-bottom: 12px;
							}

							.exp_div {
								display: flex;
								flex-wrap: wrap;
								gap: 32px;

								.exp_item {
									text-decoration: none;

									span {
										@include font13;
										color: $textColor1;
									}

									i {
										font-size: 12px;
										margin-left: 4px;
										color: $textColor1;
									}

									&:hover {
										span {
											text-decoration: underline;
										}
									}
								}
							}
						}

						.img_div {
							background: $bgColor6;
							border-radius: 8px;
							border: 1px solid #e5e5e5;

							img {
								width: 100%;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.container {
		padding: 40px 24px;

		.introduction {
			width: 100%;

			:deep(.fs-tabs) {
				.fs-tabs__content {
					.tab_cont {
						.content_div {
							grid-template-columns: 452px auto;
						}
					}
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.container {
		padding: 36px 16px;

		.introduction {
			:deep(.fs-tabs) {
				.fs-tabs__scroll {
					justify-content: start;
					height: 100%;
				}

				.fs-tabs__tab {
					height: 100%;
				}

				.fs-tabs__content {
					.tab_cont {
						.content_div {
							padding-top: 0;
							gap: 32px;
							grid-template-columns: auto;

							.experience {
								margin-top: 32px;
							}
						}
					}
				}
			}
		}
	}
}
</style>
