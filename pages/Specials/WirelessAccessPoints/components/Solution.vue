<template>
	<div class="content">
		<FsTabs center>
			<FsTabPane v-for="(item, index) in dataList" :key="String(index)" :label="item.title" :name="`${index + 1}`">
				<div class="subContent">
					<p v-html="item.desc"></p>
					<!-- <img :src="item.image" :alt="item.desc" /> -->
					<PhotoGallery :images="[item.image]" :alt="item.desc" />
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>

<script lang="ts" setup>
import { FsTabs, FsTabPane, FsButton } from "fs-design";
import PhotoGallery from "@/component/PhotoGallery/PhotoGallery.vue";

const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();
const props = defineProps({
	dataList: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { device } = storeToRefs(deviceStore);
</script>
<style lang="scss" scoped>
.content {
	:deep(.fs-tabs) {
		padding-top: 12px;
		justify-content: center;
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
	}

	.subContent {
		text-align: center;
		justify-content: center;
		padding-top: 24px;
		width: 80%;
		margin-left: 10%;
		p {
			font-family: Open Sans;
			font-size: 14px;
			font-weight: 400;
			letter-spacing: 0;
			line-height: 22px;
			text-align: center;
			width: 100%;
		}
		img {
			width: 100%;
			margin-top: 24px;
		}
		.photo_gallery_box {
			width: 100%;
			margin-top: 24px;
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.subContent {
			margin-left: 0;
			width: 100%;
			ul li {
				display: block;
				img {
					width: 100%;
					height: auto;
				}

				.right {
					width: 100%;
					padding: 20px;
					margin-left: 0px;
					margin-top: 16px;
				}
			}
		}
	}
}
</style>
