<template>
	<div class="content">
		<h4>{{ dataInfo.title }}</h4>
		<div class="subContent">
			<div class="left">
				<img :src="dataInfo.img" :alt="dataInfo?.tit" />
			</div>
			<div class="right">
				<p class="highlights_title">{{ dataInfo.tit }}</p>
				<div v-for="(subItem, subIndex) in dataInfo.list" :key="subIndex" class="text_div">
					<div>
						<i></i>
						<p>{{ subItem.tit }}</p>
					</div>
					<p class="describe">{{ subItem.txt }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
const props = defineProps({
	dataInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});
</script>
<style lang="scss" scoped>
.content {
	h4 {
		font-size: 16px;
		line-height: 24px;
		text-align: center;
		margin-bottom: 24px;
	}

	.subContent {
		display: grid;
		grid-template-columns: auto 334px;
		column-gap: 28px;
		> div {
			background: #ffffff;
		}
		img {
			width: 100%;
		}
		.left {
			background-color: $bgColor3;
			img {
				width: 100%;
				height: auto;
			}
		}
		.right {
			background-color: $bgColor3;
			padding: 32px;
			.highlights_title {
				@include font16;
				font-weight: 600;
				color: $textColor1;
				margin-bottom: 16px;
			}
			.text_div {
				padding-top: 8px;
				&.text_div_first {
					padding-top: 0;
				}
				div {
					display: flex;
					i {
						width: 4px;
						height: 4px;
						border-radius: 2px;
						background-color: $textColor3;
						margin: 10px 8px 0 0;
					}
					p {
						@include font14;
						font-weight: 400;
						color: $textColor1;
					}
				}
				.describe {
					@include font12;
					font-weight: 400;
					color: $textColor3;
					padding-top: 4px;
					margin-left: 12px;
				}
			}
			.button_div {
				margin: 16px 0 0 12px;

				:deep(.fs-button--suffix) {
					margin-right: 0;
					font-size: 13px;
					color: $textColor1;
				}
				:deep(.iconfont) {
					color: $textColor1;
					font-size: 12px;
					font-weight: 400;
					line-height: 1;
					margin-left: 4px;
					text-align: left;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		.subContent {
			display: block;
			img {
				width: 100%;
				height: auto;
			}
			.right {
				width: 100%;
				padding: 20px;
				margin-left: 0px;
				margin-top: 16px;
			}
		}
	}
}
</style>
