<template>
	<div class="content">
		<FsTabs center>
			<FsTabPane v-for="(item, index) in dataInfo.nav" :key="String(index)" :label="item" :name="`${index + 1}`">
				<div class="subContent">
					<div class="left">
						<img :src="scenariosImgUrl" :alt="dataInfo?.hightlights_txt" />
					</div>
					<div class="right">
						<div class="tit">{{ dataInfo.hightlights_txt }}</div>
						<ul>
							<li v-for="v in dataInfo.nav_list[index].txt" :key="v">
								<i></i>
								<span>{{ v }}</span>
							</li>
						</ul>
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
	</div>
</template>
<script lang="ts" setup>
import { FsTabs, FsTabPane } from "fs-design";

const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();

const props = defineProps({
	dataInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});

const { device } = storeToRefs(deviceStore);
const activeIndex = ref("0");

const scenariosImgUrl = computed(() => {
	const data = props.dataInfo.nav_list[activeIndex.value];
	if (device.value === "pc") {
		return data.img;
	} else if (device.value === "pad") {
		return data.img_pad;
	} else {
		return data.img_m;
	}
});
</script>
<style lang="scss" scoped>
.content {
	:deep(.fs-tabs) {
		.fs-tabs__scroll {
			justify-content: flex-start;
		}
		padding-top: 12px;
		justify-content: center;
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
		.fs-tabs__item {
			max-width: unset;
		}
	}

	.subContent {
		display: flex;
		padding-top: 24px;
		> div {
			background: #fafafb;
		}
		img {
			width: 100%;
		}
		.left {
			width: 70%;
		}
		.right {
			flex: 1;
			padding: 32px;
			margin-left: 16px;
			background-color: #fafafb;
			.tit {
				@include font14;
				font-weight: 600;
				margin-bottom: 16px;
			}
			ul {
				li {
					display: flex;
					@include font12;
					color: $textColor3;
					margin-bottom: 8px;
					&:last-child {
						margin-bottom: 0;
					}
					i {
						flex-shrink: 0;
						width: 4px;
						height: 4px;
						border-radius: 4px;
						background-color: $textColor3;
						margin: 9px 8px 0 0;
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.subContent {
			flex-direction: column;
			.left {
				width: 100%;
			}
			.right {
				width: 100%;
				padding: 20px 20px 28px 20px;
				margin-left: 0;
				margin-top: 16px;
			}
		}
	}
}
</style>
