<template>
	<div class="content_box">
		<p class="title">{{ overview.tit }}</p>
		<ul>
			<li v-for="(item, index) in overview.list" :key="index">
				<div class="top">
					<img :src="item.img" :alt="item.tit" />
				</div>
				<div class="bottom">
					<h3>{{ item.tit }}</h3>
					<p>{{ item.desc }}</p>
				</div>
			</li>
		</ul>
		<div class="btn_box">
			<fs-button type="red" tabindex="0" @click="TryNow">
				{{ downloadNow }}
			</fs-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
const props = defineProps({
	overview: {
		type: Object,
		default() {
			return {};
		}
	},
	downloadNow: {
		type: String,
		default: ""
	}
});
const route = useRoute();
const userStore = useUserStore();
const { isLogin } = storeToRefs(userStore);
const localeLink = useLocaleLink();

const TryNow = async () => {
	if (!isLogin.value) {
		location.href = localeLink(`/login.html?redirect=${route.fullPath}`);
	} else {
		await useRequest.post("/api/picOSVDownloadsSync", {
			data: {
				website_link: route.fullPath,
				page_from: "specials"
			}
		});
		await useRequest.post("/api/sendPicOSVSoftwareEmail");
		location.href = localeLink(`/pica8-resources.html`);
	}
};
</script>

<style lang="scss" scoped>
.content_box {
	@include width1200;
	padding: 40px 0;
	.title {
		@include font20;
		font-weight: 600;
		color: $textColor1;
		text-align: center;
		padding: 0 120px;
	}
	> ul {
		display: flex;
		justify-content: space-between;
		margin: 24px 0;
		> li {
			width: calc((100% - 40px) / 3);
			padding: 0 4px 4px;
			.top {
				margin-bottom: 8px;
				img {
					margin: 0 auto;
					display: block;
					width: 68px;
				}
			}
			.bottom {
				text-align: center;
				h3 {
					@include font14;
					font-weight: 600;
					color: $textColor1;
				}
				p {
					@include font12;
					color: $textColor2;
				}
			}
		}
	}
	.btn_box {
		display: flex;
		justify-content: center;
	}
}

@media (max-width: 1024px) {
	.content_box {
		width: calc(100% - 48px);
		.title {
			padding: 0;
		}
	}
}

@media (max-width: 768px) {
	.content_box {
		padding: 36px 0;
		width: calc(100% - 32px);
		.describe {
			padding: 12px 0 0;
		}
	}
}
</style>
