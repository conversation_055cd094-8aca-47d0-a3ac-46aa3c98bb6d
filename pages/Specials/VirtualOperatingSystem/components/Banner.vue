<template>
	<div>
		<div class="banner">
			<div class="banner_txt">
				<div class="text">
					<h1 v-html="banner.title"></h1>
					<p v-html="banner.desc"></p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps({
	banner: {
		type: Object,
		default() {
			return {};
		}
	}
});

const bannerUrlPc = `url(${props.banner.pc})`;
const bannerUrlPad = `url(${props.banner.pad})`;
const bannerUrlMobile = `url(${props.banner.m})`;
</script>

<style lang="scss" scoped>
.banner {
	width: 100%;
	height: 320px;
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	background-image: v-bind(bannerUrlPc);
	.banner_txt {
		@include width1200;
		height: 100%;
		display: flex;
		align-items: center;
	}
	.text {
		width: 750px;
	}
	h1 {
		@include font32;
		font-weight: 600;
		color: $textColor6;
		margin-bottom: 16px;
		max-width: 620px;
		@include pad {
			max-width: 460px;
		}
	}
	p {
		@include font16;
		color: $textColor6;
		max-width: 560px;
		@include pad {
			max-width: 400px;
		}
	}
}
@include pad {
	.banner {
		background-image: v-bind(bannerUrlPad);
	}
}

@media (max-width: 768px) {
	.banner {
		background-image: v-bind(bannerUrlMobile);
		height: 440px;
		.banner_txt {
			align-items: flex-start;
			padding-top: 56px;
			width: calc(100% - 48px);
		}
		h3 {
			@include font24;
			margin-bottom: 12px;
		}
		p {
			@include font16;
		}
	}
}
</style>
