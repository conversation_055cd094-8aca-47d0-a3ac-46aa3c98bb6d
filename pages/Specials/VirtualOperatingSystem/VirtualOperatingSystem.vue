<template>
	<div id="refContainer" class="container_div">
		<Banner :banner="banner"></Banner>
		<!-- <div id="slideDownRef" class="nav_div">
			<navigation-bar
				:nav="languageData.nav"
				:activeIndex="scrollIndex"
				:slideDownOpen="slideDownOpen"
				:downloadNow="languageData.download_now"
				@pageNavClick="pageNavClick"
				@clickSlideDown="clickSlideDown"></navigation-bar>
		</div> -->
		<FsTabsAnchor :data="nav" :text="languageData.download_now" @jumpPage="TryNow">
			<div id="ref-0">
				<Overview :overview="languageData.overview" :downloadNow="languageData.download_now"></Overview>
			</div>
			<div id="ref-1">
				<Benefits :overview="languageData.benefits"></Benefits>
			</div>
			<div id="ref-2">
				<Features :overview="languageData.features"></Features>
			</div>
			<div id="ref-3">
				<Services :overview="languageData.services" @openImg="openImg"></Services>
			</div>
			<div id="ref-4">
				<Applications :overview="languageData.applications"></Applications>
				<sub-banner :overview="languageData.subBanner" :downloadNow="languageData.download_now"></sub-banner>
			</div>
			<div id="ref-5">
				<Resources :resources="languageData.resource"></Resources>
			</div>
		</FsTabsAnchor>
		<!-- <image-preview :src="introductionPic" :open="imageViewOpen" @close="closeImage" /> -->
	</div>
</template>

<script setup lang="ts">
import { FsTabsAnchor } from "fs-design";

import Banner from "./components/Banner.vue";
import NavigationBar from "./components/NavigationBar.vue";
import Overview from "./components/Overview.vue";
import Features from "./components/Features.vue";
import Services from "./components/Services.vue";
import Benefits from "./components/Benefits.vue";
import ImagePreview from "./components/ImagePreview.vue";
import Applications from "./components/Applications.vue";
import Resources from "./components/Resources.vue";
import SubBanner from "./components/SubBanner.vue";
const route = useRoute();
const userStore = useUserStore();
const { isLogin } = storeToRefs(userStore);
const localeLink = useLocaleLink();
const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const banner = computed(() => {
	return {
		pc: props.languageData.banner.img_pc,
		pad: props.languageData.banner.img_pad,
		m: props.languageData.banner.img_m,
		title: props.languageData.banner.title,
		desc: props.languageData.banner.desc
	};
});
const nav: any = ref([]);

props.languageData.nav.forEach((item: { title: any }, index: string) => {
	nav.value.push({
		title: item,
		name: "#ref-" + index
	});
});
const TryNow = async () => {
	if (!isLogin.value) {
		location.href = localeLink(`/login.html?redirect=${route.fullPath}`);
	} else {
		await useRequest.post("/api/picOSVDownloadsSync", {
			data: {
				website_link: route.fullPath,
				page_from: "specials"
			}
		});
		await useRequest.post("/api/sendPicOSVSoftwareEmail");
		location.href = localeLink(`/pica8-resources.html`);
	}
};
function buriedPointWrapper(action: string, Label: string) {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "PicOS-V Specials Page",
			eventAction: action,
			eventLabel: Label,
			nonInteraction: false
		});
	}
}
const useDevice = useDeviceStore();
const { screenWidth } = storeToRefs(useDevice);
const scrollIndex = ref(0);
const slideDownOpen = ref(false);
const navName = ["Overview", "Benefits", "Features", "Applications", "Services", "Resources"];
const introductionPic = ref<string>("");
const imageViewOpen = ref(false);
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
function openImg(item: string) {
	if (!isMobile.value) return;
	introductionPic.value = item;
	imageViewOpen.value = true;
}
function closeImage() {
	imageViewOpen.value = false;
}
onMounted(() => {
	document.addEventListener("click", handleClickOutSide);
	window.addEventListener("scroll", scrollHandle);
	const aAdditional = document.querySelector(".additional a");
	if (aAdditional) {
		aAdditional.addEventListener("click", function () {
			buriedPointWrapper("Additional Services_module", "Get Expert Support");
		});
	}
});
onMounted(() => {
	window.addEventListener("scroll", scrollHandle);
});
function pageNavClick(index: number) {
	scrollIndex.value = index;
	const mHeight = screenWidth.value > 1024 ? 58 : screenWidth.value <= 768 ? 102 : 102;
	const navContent_index = document.getElementById(`ref-${index}`) as HTMLElement;
	const top = navContent_index?.offsetTop;
	document.querySelector("body,html")?.setAttribute("style", "scroll-behavior: smooth;");
	window.scrollTo(0, top - mHeight);
	slideDownOpen.value = false;
	buriedPointWrapper("specials_module_entrance", navName[index]);
}
function scrollHandle() {
	const mHeight = screenWidth.value > 1024 ? 58 : screenWidth.value <= 768 ? 102 : 102;
	props.languageData.nav.forEach((item: any, i: number) => {
		const navContent_index = document.getElementById(`ref-${i}`) as HTMLElement;
		const top = navContent_index?.offsetTop;
		const s_top = document.documentElement.scrollTop;
		if (s_top >= top - mHeight) {
			scrollIndex.value = i;
		}
	});
}
function clickSlideDown() {
	slideDownOpen.value = !slideDownOpen.value;
}
function handleClickOutSide(e: any) {
	const slideDownRef = document.getElementById("slideDownRef") as HTMLElement;
	if (slideDownRef && !slideDownRef.contains(e.target)) {
		slideDownOpen.value = false;
	}
}
</script>

<style lang="scss" scoped>
.container_div {
	:deep(.fs-affix) {
		.fs-tabs__toolBoxM {
			background-color: #fff;
		}
		.fs-tabs {
			padding: 0 48px;
			.fs-tabs__nav {
				max-width: 1200px;
				margin: 0 auto;
			}
			@media (max-width: 1024px) {
				padding: 0 24px;
				.fs-tabs__nav {
					max-width: 100%;
				}
			}
			@media (max-width: 768px) {
				padding: 0;
			}
		}
	}
	.nav_div {
		background-color: #eeeeee;
		position: sticky;
		top: 0;
		left: 0;
		padding: 0 48px;
		z-index: 20;
		@media (max-width: 1024px) {
			padding: 0;
			top: 48px;
		}
	}
}
</style>
