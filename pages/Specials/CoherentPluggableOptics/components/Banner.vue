<template>
	<div class="fs-banner">
		<div class="common">
			<div class="banner_txt">
				<h1 v-if="bannerInfo.title">{{ bannerInfo.title }}</h1>
				<p v-if="bannerInfo.desc">{{ bannerInfo.desc }}</p>
				<div v-if="bannerInfo.buttonStr" class="btn">
					<FsButton iconPlacement="suffix" text type="primary" @click="bannerClick">
						<template #default>{{ bannerInfo.buttonStr }}</template>
						<template #icon>
							<i class="iconfont iconfs_2020091147icon" />
						</template>
					</FsButton>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { FsButton } from "fs-design";
// import type { SpecialsBanner } from "./type";
interface SpecialsBanner {
	id?: number;
	title?: string;
	desc?: string;
	image?: string;
	padImage?: string;
	mobileImage?: string;
	buttonStr?: string;
	height?: string;
}
const props = defineProps({
	bannerInfo: {
		type: Object as () => SpecialsBanner,
		required: true
	}
});

// PC、Pad、Mobile
const bannerUrlPc = computed(() => `url(${props.bannerInfo.image})`);
const bannerUrlPad = computed(() => `url(${props.bannerInfo.padImage || props.bannerInfo.image})`);
const bannerUrlMobile = computed(() => `url(${props.bannerInfo.mobileImage || props.bannerInfo.image})`);
const bannerHeight = computed(() => `${props.bannerInfo.height || 200}px`);
const emits = defineEmits();

// const isHtmlContent = computed(() => {
// 	// 通过正则表达式判断内容是否为 HTML
// 	const htmlRegex = /<\/?[a-z][\s\S]*>/i;
// 	return htmlRegex.test(props.bannerInfo.title ?? "");
// });

function bannerClick() {
	emits("bannerClick");
}
</script>

<style scoped lang="scss">
.fs-banner {
	height: v-bind(bannerHeight);
	@include pc() {
		background-image: v-bind(bannerUrlPc);
	}
	@include pad() {
		background-image: v-bind(bannerUrlPad);
	}
	@include mobile() {
		background-image: v-bind(bannerUrlMobile);
	}
}

.fs-banner {
	width: 100%;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;

	.common {
		@include contentWidth;
		display: flex;
		height: 100%;
		align-items: center;
		// justify-content: center;
		color: $textColor1;

		.banner_txt {
			h1 {
				@include font32;
				font-weight: 600;
				max-width: 750px;
			}

			p {
				@include font16;
				max-width: 640px;
				font-weight: 400;
				line-height: 24px;
				margin-top: 16px;
			}

			.btn {
				text-align: center;

				:deep(.fs-button) {
					&:hover {
						.fs-button--suffix {
							text-decoration: none;
						}

						background-color: #ffffff39;
					}

					margin-top: 24px;
					border: 1px solid #fff;
					border-radius: 4px;
					cursor: pointer;
					display: inline-block;
					position: relative;
					height: 40px;
					line-height: 40px;
					padding: 0 20px 0 24px;
					text-align: center;
					transform: all 0.3s;
				}

				:deep(.fs-button--suffix) {
					margin-right: 0;
					font-size: 13px;
					color: white;
				}

				:deep(.iconfont) {
					color: white;
					font-size: 12px;
					font-weight: 400;
					line-height: 1;
					margin-left: 4px;
					text-align: left;
				}
			}
		}
	}
}

@include pad {
	.fs-banner .common {
		// justify-content: flex-start;

		.banner_txt {
			max-width: 600px;
		}
	}
}

@include mobile {
	.fs-banner {
		height: 440px;
		padding-top: 56px;

		.common {
			align-items: flex-start;
			justify-content: flex-start;
			width: calc(100% - 48px);
			.banner_txt {
				max-width: 366px;

				// padding: 0 8px;
				h1 {
					@include font24;
					// text-align: center;
				}

				p {
					@include font14;
					max-width: 298px;
					// text-align: center;
					margin-top: 12px;
					:deep(br) {
						display: none;
					}
				}
			}
		}
	}
}
</style>
