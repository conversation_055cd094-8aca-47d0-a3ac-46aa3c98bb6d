<template>
	<div class="container">
		<!-- Top Image -->
		<Banner :bannerInfo="bannerInfo" />
		<!-- content -->
		<FsTabsAnchor :data="navData" :text="buttonText" @jumpPage="jumpPage">
			<SpecialsContent
				v-for="(section, index) in sections"
				:id="`part${index}`"
				:key="index"
				:title="section.title"
				:contents="section.contents"
				:contentAlign="section.contentAlignCenter ? 'center' : 'left'"
				:background="section.background">
				<component :is="section.component" v-if="section.component" v-bind="section.props" />
			</SpecialsContent>
		</FsTabsAnchor>
	</div>
</template>
<script setup lang="ts">
import { FsTabsAnchor } from "fs-design";
import SpecialsContent from "../components/SpecialsContent.vue";
// import Features from "./component/Features.vue";
import Featured from "./component/Featured.vue";
import Connections from "./component/Connections.vue";
import Scenarios from "./component/Scenarios.vue";
import Resource from "./component/Resource.vue";
import CaseStudies from "./component/CaseStudies.vue";
import Banner from "@/component/Specials/Banner.vue";

// 传参数据
const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { languageData } = props;

// 模块数据
const sections = computed(() => [
	{
		title: languageData.overview.tit,
		contents: [languageData.overview.desc.join(" ")],
		contentAlignCenter: true,
		background: "#ffffff",
		component: "",
		props: {}
	},
	{
		title: languageData.benefits.tit,
		contents: [],
		background: "#fafbfb",
		component: Connections,
		props: { dataList: languageData.benefits.cards }
	},
	{
		title: languageData.featured.tit,
		contents: [],
		background: "#ffffff",
		component: Featured,
		props: { dataList: [...languageData.featured.cards] }
	},
	// {
	// 	title: languageData.nav[2],
	// 	contents: [],
	// 	background: "#ffffff",
	// 	component: Features,
	// 	props: { dataList: languageData.features.cards }
	// },
	{
		title: languageData.scenarios.tit,
		contents: [],
		background: "#fafbfb",
		component: Scenarios,
		props: { dataInfo: languageData.scenarios }
	},
	{
		title: languageData.cases.tit,
		contents: [],
		background: "#ffffff",
		component: CaseStudies,
		props: { dataInfo: languageData.cases }
	},
	{
		title: languageData.resources.tit,
		contents: [],
		background: "#fafbfb",
		component: Resource,
		props: { dataList: languageData.resources.cards }
	}
]);

// Banner信息
const bannerInfo = computed(() => {
	return {
		id: 0,
		image: languageData.bannerData.banner.pc,
		padImage: languageData.bannerData.banner.pad,
		mobileImage: languageData.bannerData.banner.m,
		title: languageData.bannerData.tit,
		subtitle: props.languageData.bannerData.desc,
		height: "320"
	};
});

// 导航初始数据
const navData = reactive(
	Array.from(languageData.nav, (valve, index) => ({
		title: valve,
		name: `#part${index}`
	}))
);
const buttonText = reactive([languageData.btn.txt, languageData.seeProducts.txt]);

// 导航按钮点击
const jumpPage = (index: number | undefined) => {
	if (index === 1) {
		location.href = languageData.seeProducts.link;
	} else {
		location.href = languageData.btn.link;
	}
};
</script>

<style lang="scss" scoped>
.container {
	:deep(.fs-banner) {
		.common {
			justify-content: flex-start;
			.banner_txt {
				max-width: 750px;
				h1 {
					text-align: left;
					max-width: 620px;
				}
				p {
					text-align: left;
					max-width: 560px;
				}
			}
		}
		@media (max-width: 1024px) {
			.common {
				width: calc(100% - 96px);
				.banner_txt {
					max-width: 600px;
					h1 {
						padding-left: 0px;
						max-width: 460px;
					}
					p {
						padding-left: 0px;
						max-width: 400px;
					}
				}
			}
		}

		@media (max-width: 768px) {
			height: 440px;
			.common {
				width: calc(100% - 48px);
				padding-top: 56px;
				align-items: flex-start;
				.banner_txt {
					max-width: 100%;
					h1 {
						font-size: 32px;
					}
					p {
						margin-top: 12px;
						font-size: 14px;
					}
				}
			}
		}
	}
	:deep(.fs-tabs) {
		// background: $bgColor5;
		.fs-tabs__nav {
			width: 84vw;
			max-width: 1200px;
			margin: 0 auto;
			// background: $bgColor5;
			@media (max-width: 1200px) {
				width: 94vw;
			}
			// .fs-tabs__scroll {
			// 	background-color: $bgColor5 !important;
			// }
			.fs-button--anchor {
				width: max-content !important;
			}
		}
	}
}
</style>
