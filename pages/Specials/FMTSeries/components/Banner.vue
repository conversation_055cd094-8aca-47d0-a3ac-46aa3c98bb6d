<template>
	<div class="container">
		<div class="banner_div" :style="{ backgroundImage: `url(${bannerImg})` }"></div>
		<div class="content_div">
			<div class="content_wrap">
				<div class="text_content">
					<p class="title_h1">{{ banner.title }}</p>
					<p class="desc_wrap">{{ banner.desc }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
const props = defineProps({
	banner: {
		type: Object,
		default() {
			return {};
		}
	}
});

const useDevice = useDeviceStore();
const { screenWidth } = storeToRefs(useDevice);

const bannerImg = computed(() => {
	if (screenWidth.value > 1024) {
		return props.banner.pc;
	} else if (screenWidth.value > 768) {
		return props.banner.pad;
	} else {
		return props.banner.m;
	}
});
</script>

<style lang="scss" scoped>
.container {
	position: relative;
	height: 320px;
	.banner_div {
		position: absolute;
		width: 100%;
		height: 100%;
		background: center/cover no-repeat;
		z-index: -1;
		padding: 0 48px;
	}
	.content_div {
		width: 100%;
		height: 100%;
		padding: 0 48px;
		.content_wrap {
			max-width: 1200px;
			margin: 0 auto;
			.text_content {
				width: 750px;
				color: #fff;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				.title_h1 {
					@include font32;
					font-weight: 600;
					white-space: pre-line;
				}
				.desc_wrap {
					@include font16;
					font-weight: 400;
					margin-top: 16px;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.container {
		.content_div {
			.content_wrap {
				width: 100%;
				.text_content {
					width: 600px;
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.container {
		height: 440px;
		.content_div {
			padding: 0 24px;
			.content_wrap {
				.text_content {
					padding-top: 56px;
					top: 0;
					transform: translateY(0);
					width: calc(100% - 48px);
					.title_h1 {
						@include font24;
					}
					.desc_wrap {
						@include font14;
						margin-top: 12px;
					}
				}
			}
		}
	}
}
</style>
