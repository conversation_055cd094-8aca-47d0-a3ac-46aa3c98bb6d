<template>
	<div class="content">
		<FsTabs center>
			<FsTabPane v-for="(item, index) in dataList.tabs" :key="String(index)" :label="item" :name="`${index + 1}`">
				<div class="subContent">
					<div class="left">
						<img :src="dataList.cards[index].img" :alt="dataList.cards[index].name" />
					</div>
					<div class="right">
						<h4 v-html="dataList.cards[index].name"></h4>
						<p v-for="(c, v) in dataList.cards[index].point" :key="v" v-html="c"></p>
						<FsButton iconPlacement="suffix" text type="primary" @click="skip(dataList.cards[index].url)">
							<template #default>{{ dataList.cards[index].url_txt }}</template>
							<template #icon>
								<i class="iconfont iconfs_2020091147icon" />
							</template>
						</FsButton>
					</div>
				</div>
			</FsTabPane>
		</FsTabs>
		<div class="says">
			<div class="left">
				<img :src="commentInfo.img1" :alt="commentInfo.content" />
			</div>
			<div class="center">
				<p>{{ commentInfo.content }}</p>
				<p>— {{ commentInfo.author }}</p>
			</div>
			<div class="right">
				<img :src="commentInfo.img2" alt="image" />
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { FsTabs, FsTabPane, FsButton } from "fs-design";
const localeLink = useLocaleLink();

const props = defineProps({
	dataList: {
		type: Object,
		default() {
			return {};
		}
	},
	commentInfo: {
		type: Object,
		default() {
			return {};
		}
	}
});

const skip = function (url: string) {
	location.href = localeLink(url);
};
</script>
<style lang="scss" scoped>
.content {
	:deep(.fs-tabs) {
		padding-top: 12px;
		justify-content: center;
		.fs-tabs-nav-tab {
			justify-content: center;
			li {
				color: $textColor3;
				&.active {
					font-weight: 400;
					color: $textColor1;
				}
			}
		}
	}

	.says {
		margin-top: 48px;
		padding-top: 48px;
		border-top: 1px solid #e5e5e5;
		display: flex;
		.center {
			padding: 0 80px 0 24px;
			p:last-child {
				padding-top: 36px;
				color: #707070;
			}
		}
		.left img {
			width: 32px;
			height: auto;
		}
		.right img {
			width: 154px;
		}
	}

	.subContent {
		display: flex;
		padding-top: 24px;
		> div {
			background: #fafafb;
		}
		img {
			width: 100%;
		}
		.left {
			flex: 1;
		}
		.right {
			width: 334px;
			padding: 32px;
			margin-left: 28px;
			h4 {
				@include font16;
				padding-bottom: 16px;
			}
			p {
				color: $textColor2;
				@include font14;
				padding-bottom: 6px;
			}
			:deep(.fs-button--suffix) {
				margin-right: 0;
				font-size: 13px;
				color: $textColor1;
				margin-top: 5px;
			}
			:deep(.iconfont) {
				color: $textColor1;
				margin-top: 5px;
				font-size: 12px;
				font-weight: 400;
				line-height: 1;
				margin-left: 4px;
				text-align: left;
			}
		}
	}
}

@media (max-width: 1024px) {
	.content .subContent .right {
		width: 268px;
	}
}

@media (max-width: 768px) {
	.content {
		:deep(.fs-tabs) {
			@include font16;
			.fs-tabs-nav-tab {
				justify-content: flex-start;
				padding-left: 16px;
				li {
					margin-right: 24px;
				}
			}
		}

		.says {
			margin-top: 36px;
			.center {
				padding-right: 0;
			}
			.right {
				display: none;
			}
		}

		.subContent {
			display: block;
			img {
				width: 100%;
				height: auto;
			}

			.right {
				width: 100%;
				padding: 20px;
				margin-left: 0px;
				margin-top: 16px;
			}
		}
	}
}
</style>
