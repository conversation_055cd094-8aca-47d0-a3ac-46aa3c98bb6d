<template>
	<div class="container">
		<Banner :bannerInfo="bannerInfo" @bannerClick="bannerClick" />
		<FsTabsAnchor :data="navData" :text="languageData.navbtn.txt" @jumpPage="jumpPage">
			<SpecialsContent
				v-for="(section, index) in sections"
				:id="`part${index}`"
				:key="index"
				:title="section.title"
				:contents="section.contents"
				:contentAlign="section.contentAlignCenter ? 'center' : 'left'"
				:background="section.background">
				<component :is="section.component" v-if="section.component" v-bind="section.props" />
			</SpecialsContent>
		</FsTabsAnchor>
		<FsDialog
			v-model="dialogVisible"
			className="special_183_banner_video_dialog_div"
			title=""
			:customDialogStyle="customDialogStyle"
			:headerStyle="headerStyle"
			:contentStyle="contentStyle">
			<!-- <template #title>
				<div class="fs_dialog_title">{{ website === "en" ? `FS Warehouse and Service Center` : videoInfo.title }}</div>
			</template> -->
			<FsVideo v-if="dialogVisible" id="video-player2" autoplay :src="languageData.banner.learnMore.url" />
			<div v-if="dialogVisible" class="close_icon" @click="closeClicked">
				<i class="iconfont iconfont-close">&#xf30a;</i>
			</div>
		</FsDialog>
	</div>
</template>
<script setup lang="ts">
import { FsTabsAnchor, FsDialog, FsVideo } from "fs-design";
import SpecialsContent from "../components/SpecialsContent.vue";
import Overview from "./components/Overview.vue";
import Benefits from "./components/Benefits.vue";
import ManagementPlatform from "./components/ManagementPlatform.vue";
import Products from "./components/Products.vue";
import Solutions from "./components/Solutions.vue";
import CaseStudies from "./components/CaseStudies.vue";
import Resources from "./components/Resources.vue";
import Banner from "@/component/Specials/Banner.vue";
const props = defineProps({
	languageData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const bannerInfo = computed(() => {
	return {
		id: 0,
		image: props.languageData.banner.img_pc,
		padImage: props.languageData.banner.img_pad,
		mobileImage: props.languageData.banner.img_m,
		title: props.languageData.banner.title,
		subtitle: props.languageData.banner.desc,
		buttonStr: props.languageData.banner.learnMore.tit,
		height: "320"
	};
});
const navData = reactive(
	Array.from(props.languageData.nav, (valve, index) => ({
		title: valve,
		name: `#part${index}`
	}))
);
const dialogVisible = ref<Boolean>(false);
const customDialogStyle = {
	background: "#000000"
};
const headerStyle = {
	padding: "14px 32px",
	display: "none"
};
const contentStyle = {
	padding: 0
};
const bannerClick = (event: MouseEvent) => {
	dialogVisible.value = true;
};
const closeClicked = function () {
	dialogVisible.value = false;
};
const jumpPage = () => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Picos Enterprise Switches Specials Page",
			eventAction: "overview_module",
			eventLabel: "Contact Us",
			nonInteraction: false
		});
	}
	location.href = props.languageData.navbtn.link;
};
const sections = computed(() => [
	{
		title: props.languageData.overview.tit,
		contents: [props.languageData.overview.desc],
		contentAlignCenter: true,
		background: "#ffffff",
		component: Overview,
		props: { dataInfo: props.languageData.overview.btn }
	},
	{
		title: "",
		contents: [],
		contentAlignCenter: true,
		background: "#ffffff",
		component: Benefits,
		props: { dataList: props.languageData.benefits.list }
	},
	{
		title: "",
		contents: [],
		contentAlignCenter: true,
		background: "#fafafb",
		component: ManagementPlatform,
		props: { dataInfo: props.languageData.subBanner }
	},
	{
		title: props.languageData.products.tit,
		contents: [],
		contentAlignCenter: true,
		background: "#ffffff",
		component: Products,
		props: { dataList: props.languageData.products }
	},
	{
		title: props.languageData.solutions.tit,
		contents: [],
		contentAlignCenter: true,
		background: "#fafafb",
		component: Solutions,
		props: { dataList: props.languageData.solutions.list }
	},
	{
		title: props.languageData.case.tit,
		contents: [],
		contentAlignCenter: true,
		background: "#ffffff",
		component: CaseStudies,
		props: { dataInfo: props.languageData.case.list }
	},
	{
		title: props.languageData.resource.tit,
		contents: [],
		contentAlignCenter: true,
		background: "#fafafb",
		component: Resources,
		props: { dataInfo: props.languageData.resource.list }
	}
]);
</script>
<style lang="scss">
.special_183_banner_video_dialog_div {
	&.fs-dialog__content {
		width: 100% !important;
		max-width: 75%;
		max-height: 100% !important;
		aspect-ratio: 1440/810 !important;
		position: absolute;
		overflow: visible !important;
		#video-player2 {
			width: 100% !important;
			height: 100% !important;
			cursor: default;
			@media (max-width: 768px) {
				width: 100% !important;
				height: 100% !important;
			}
		}
		video {
			object-fit: cover;
		}
	}
	&.fs-dialog__contentM {
		width: 100% !important;
		max-width: 100%;
		max-height: 100% !important;
		aspect-ratio: 1440/810 !important;
		position: absolute;
		overflow: visible !important;
		#video-player2 {
			width: 100% !important;
			height: 100% !important;
			cursor: default;
		}
		video {
			object-fit: cover;
		}
	}
	.close_icon {
		display: flex;
		background-color: rgba(255, 255, 255, 0.3);
		border-radius: 4px;
		width: 40px;
		height: 40px;
		position: absolute;
		top: 0;
		left: calc(100% + 16px);
		@media (max-width: 768px) {
			left: calc(100% - 64px);
			position: fixed;
			top: 24px;
			width: 44px;
			height: 44px;
			background-color: rgba(255, 255, 255, 0);
		}
		&:hover {
			cursor: pointer;
		}
		.iconfont {
			color: #fff;
			font-size: 24px;
			margin: auto;
		}
	}
}
</style>
<style lang="scss" scoped>
.container {
	:deep(.fs-banner) {
		.common {
			justify-content: flex-start;
			.banner_txt {
				h1 {
					text-align: left;
					color: $textColor1;
					max-width: 620px;
				}
				p {
					text-align: left;
					color: $textColor1;
					max-width: 560px;
				}
				.btn {
					text-align: left;
					.fs-button {
						margin-top: 16px;
						padding: 0;
						height: unset;
						line-height: unset;
						border: none;
						&:hover {
							span {
								text-decoration: none !important;
							}
							.fs-button--suffix {
								text-decoration: underline !important;
							}

							background-color: unset;
						}
						.fs-button--suffix {
							color: $textColor1;
							@include font14;
						}
						.iconfont {
							color: $textColor1;
						}
					}
				}
			}
		}
		@media (max-width: 1024px) {
			.common {
				width: calc(100% - 96px);
				.banner_txt {
					h1 {
						padding-left: 0px;
						max-width: 460px;
					}
					p {
						padding-left: 0px;
						max-width: 400px;
					}
				}
			}
		}

		@media (max-width: 768px) {
			height: 440px;
			.common {
				width: calc(100% - 48px);
				padding-top: 56px;
				align-items: flex-start;
				.banner_txt {
					max-width: 100%;
					h1 {
						max-width: 100%;
						@include font24;
					}
					p {
						margin-top: 12px;
						max-width: 100%;
						@include font14;
					}
					.btn {
						.fs-button {
							margin-top: 12px;
						}
					}
				}
			}
		}
	}
	:deep(.fs-affix) {
		.fs-tabs {
			padding: 0 48px;
			.fs-tabs__nav {
				max-width: 1200px;
				margin: 0 auto;
			}
			@media (max-width: 1024px) {
				padding: 0 24px;
				.fs-tabs__nav {
					max-width: 100%;
				}
			}
			@media (max-width: 768px) {
				padding: 0;
			}
		}
	}
	:deep(#part0) {
		.content {
			padding-bottom: 24px;
		}
	}
	:deep(#part1) {
		.content {
			padding-top: 0;
		}
	}
}
</style>
