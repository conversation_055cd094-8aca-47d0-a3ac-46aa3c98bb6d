.compare_body {
	// @include contentWidth;
	width: 84vw;
	max-width: 1200px;
	margin: 0 auto;
	.back {
		padding: 20px 0;
		@include mobile {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		:deep(.fs-button) {
			.iconfont {
				font-size: 12px;
				line-height: 1;
				display: block;
				color: $textColor2;
			}
			.fs-button--prefix {
				color: $textColor2;
			}
			&:hover {
				.iconfont,
				.fs-button--prefix {
					color: $textColor1;
					text-decoration: none;
				}
			}
		}
		.m_highlight_switch {
			display: none;
			@include mobile {
				display: flex;
			}
			align-items: center;
			.title {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				margin-right: 8px;
			}
			.content {
				width: 44px;
				height: 24px;
				padding: 2px;
				border-radius: 999px;
				background-color: $bgColor7;
				cursor: pointer;
				transition: all 0.2s;
				position: relative;
				.btn {
					width: 20px;
					height: 20px;
					background-color: $bgColor6;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: absolute;
					top: 2px;
					left: 2px;
					.iconfont {
						display: block;
						font-size: 12px;
						line-height: 1;
						color: $textColor2;
						opacity: 0;
						transition: all 0.2s;
					}
				}
				&.isOpen {
					background-color: $bgColor8;
					.btn {
						left: 22px;
						.iconfont {
							opacity: 1;
						}
					}
				}
				&.disableBtn {
					cursor: no-drop;
				}
			}
		}
	}
	.head {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-bottom: 24px;
		border-bottom: 1px solid $borderColor1;
		> .title {
			@include font20;
			font-weight: 600;
			color: $textColor1;
		}
		.highlight_switch {
			display: flex;
			align-items: center;
			.title {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				margin-right: 8px;
			}
			.content {
				width: 44px;
				height: 24px;
				padding: 2px;
				border-radius: 999px;
				background-color: $bgColor7;
				cursor: pointer;
				transition: all 0.2s;
				position: relative;
				.btn {
					width: 20px;
					height: 20px;
					background-color: $bgColor6;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: absolute;
					top: 2px;
					left: 2px;
					.iconfont {
						display: block;
						font-size: 12px;
						line-height: 1;
						color: $textColor2;
						opacity: 0;
						transition: all 0.2s;
					}
				}
				&.isOpen {
					background-color: $bgColor8;
					.btn {
						left: 22px;
						.iconfont {
							opacity: 1;
						}
					}
				}
				&.disableBtn {
					cursor: no-drop;
				}
			}
		}
		@include mobile {
			display: none;
		}
	}
	.list_all {
		width: 100%;
		overflow: auto;
	}
	.list {
		.sticky_body {
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			grid-gap: 0;
			margin-bottom: 20px;
			padding-left: 200px;
			@media screen and (min-width: 768px) and (max-width: 1024px) {
				padding-left: calc((100% - 48px) / 6);
			}
			@media (max-width: 768px) {
				display: flex;
				padding-left: 0;
				width: 100%;
			}
		}
		.item {
			width: 200px;
			@media screen and (min-width: 768px) and (max-width: 1024px) {
				width: 100%;
			}
			overflow: hidden;
			padding: 20px 10px 24px;
			position: relative;
			:deep(.grid__img) {
				img {
					width: 120px;
					height: 120px;
				}
			}
			:deep(.grid__img__cart) {
				display: flex;
				align-items: center;
				justify-content: center;
				border-color: transparent;
				color: $textColor1;
				cursor: pointer;
				@include mobile() {
					width: 36px;
					height: 36px;
				}
			}
			&:hover {
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
			}
		}
		@include mobile {
			display: flex;
			padding: 0;
			margin-bottom: 0;
			width: 100%;
			min-width: max-content;
			.item {
				padding: 10px;
				width: 200px;
				@media screen and (min-width: 768px) and (max-width: 1024px) {
					width: 100%;
				}
				:deep(.grid) {
					.grid__img {
						margin-bottom: 8px;
						img {
							width: 60px;
							height: 60px;
						}
						.grid__img__cart {
							// display: none;
							bottom: -28px;
						}
					}
					.grid__title {
						text-align: center;
						margin: 0;
						@include font13;
					}
					.grid__label,
					.grid__price,
					.grid__inventory,
					.grid__solidReview {
						display: none;
					}
				}
			}
		}
		&.sticky {
			position: fixed;
			top: 0;
			@media (max-width: 1024px) {
				top: 48px;
			}
			left: 0;
			z-index: 10;
			width: 100%;
			background-color: $bgColor6;
			box-shadow: 0px 3px 6px -2px rgba(0, 0, 0, 0.1);
			.sticky_body {
				display: flex;
				padding-left: 0;
				width: 84vw;
				max-width: 1200px;
				margin: 0 auto;
				padding-left: 200px;
				@media screen and (min-width: 768px) and (max-width: 1024px) {
					width: calc(100% - 48px);
					padding-left: calc((100% - 48px) / 6);
				}
			}
			@include mobile {
				top: 48px;
			}
			.item {
				width: 200px;
				@media screen and (min-width: 768px) and (max-width: 1024px) {
					width: 20%;
				}
				padding: 0 0 10px;
				:deep(.grid) {
					.grid__img {
						img {
							width: 60px;
							height: 60px;
						}
						.grid__img__cart {
							display: none;
						}
					}
					.grid__title {
						text-align: center;
						margin: 0;
					}
					.brandColor,
					.grid__label,
					.grid__price,
					.grid__inventory,
					.grid__solidReview {
						display: none;
					}
				}
			}
		}
	}
	.tabs {
		padding-bottom: 36px;
		:deep(.fs-tabs) {
			.fs-tabs-nav-tab {
				.fs-tab-pane {
					&:first-child {
						padding-left: 0;
					}
				}
			}
			.fs-tabs__content {
				padding: 20px 0 0;
				@include mobile {
					padding: 20px 0 0;
				}
			}
		}
		.table {
			.tr {
				display: grid;
				grid-template-columns: repeat(6, 1fr);
				grid-gap: 0;
				.td {
					width: 200px;
					@media screen and (min-width: 768px) and (max-width: 1024px) {
						width: 100%;
					}
					@include font13;
					color: $textColor1;
					padding: 12px;
					display: flex;
					align-items: center;
					&:first-child {
						font-weight: 600;
					}
					> span {
						word-break: break-word;
					}
					> img {
						display: block;
					}
				}
				&.highlighted {
					background: rgba(255, 215, 72, 0.08) !important;
				}
			}
			.tr_m {
				display: none;
			}
			&.features {
				.zebra:nth-child(odd) {
					background: rgba(0, 96, 191, 0.08);
				}
				.zebra:nth-child(odd) {
					.tr_list .td {
						background: rgba(0, 96, 191, 0.08);
					}
				}
			}
			&.specifications {
				.zebra:nth-child(odd) {
					background: rgba(0, 96, 191, 0.08);
				}
				.zebra:nth-child(odd) {
					.tr_list .td {
						background: rgba(0, 96, 191, 0.08);
					}
				}
			}
		}
		@include mobile {
			:deep(.fs-tabs) {
				width: max-content;
				min-width: 100%;
			}
			.table {
				width: 100%;
				.tr {
					display: none;
				}
				.tr_m {
					display: block;
					background-color: transparent !important;
					.tr_title {
						span {
							@include font12;
							color: $textColor1;
							font-weight: 600;
							position: absolute;
						}
					}
					.tr_list {
						display: flex;
						// grid-template-columns: repeat(5, 1fr);
						grid-gap: 0;
						margin-bottom: 8px;
						padding-top: 24px;
						.td {
							width: 200px;
							@media screen and (min-width: 768px) and (max-width: 1024px) {
								width: 100%;
							}
							@include font13;
							color: $textColor1;
							padding: 12px;
							display: flex;
							align-items: center;
							// &:first-child {
							// 	font-weight: 600;
							// }
							> span {
								word-break: break-word;
							}
							> img {
								display: block;
							}
							&.highlighted {
								background: rgba(255, 215, 72, 0.08) !important;
							}
						}
					}
					&:last-child {
						.tr_list {
							margin: 0;
						}
					}
				}
			}
		}
	}
	// @include pad {
	// }
	// @include mobile {
	// 	.compare_body {
	// 		width: 100%;
	// 		.tabs {
	// 			.table {
	// 				.tr {
	// 					display: grid;
	// 					grid-template-columns: repeat(6, 1fr);
	// 					grid-gap: 0;
	// 					.td {
	// 						width: 190px;
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}
	// }
	@media screen and (min-width: 768px) and (max-width: 1024px) {
		width: calc(100% - 48px);
	}
	@media (max-width: 768px) {
		width: 100%;
		.back {
			padding: 20px 16px;
		}
		.list_all {
			padding: 0 16px;
		}
	}
}
