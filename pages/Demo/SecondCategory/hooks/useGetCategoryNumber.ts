/**
 *  @description: 根据屏幕宽度展示分类数量
 *  @returns {number}
 */
import type { matchingRangeResults } from "../types";
export const useGetCategoryNumber = (narrowLength: number) => {
	const deviceStore = useDeviceStore();
	const state = ref(0);
	const getMatchingRange = (value: number): matchingRangeResults | undefined => {
		const ranges: matchingRangeResults[] = [
			{ min: 1401, max: Infinity, result: 4 },
			{ min: 1025, max: 1400, result: narrowLength > 3 ? 3 : narrowLength },
			{ min: 961, max: 1024, result: narrowLength > 2 ? 2 : narrowLength },
			{ min: 769, max: 960, result: narrowLength > 1 ? 1 : narrowLength },
			{ min: 0, max: 768, result: 0 }
		];
		const matchingRange = ranges.find(range => range.min <= value && value <= range.max);
		return matchingRange;
	};
	const getNumberByRange = (value: number): number => {
		if (getMatchingRange(value)) {
			state.value = getMatchingRange(value)!.result;
		}
		return state.value;
	};
	watch(
		() => deviceStore.screenWidth,
		newVal => {
			state.value !== getMatchingRange(newVal)!.result && getNumberByRange(newVal);
		},
		{
			immediate: true
		}
	);
	return state;
};
