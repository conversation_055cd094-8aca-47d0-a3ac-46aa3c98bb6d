.filterSelect {
	margin-right: 8px;
	box-sizing: border-box;

	&__input {
		max-width: 168px;
		padding: 0px 16px;
		@include font12;
		color: $textColor1;
		border: 1px solid $borderColor1;
		border-radius: 3px;
		cursor: pointer;

		p {
			display: flex;
			align-items: center;

			&.active {
				span {
					color: $textColor1;
				}
			}
		}

		&__title {
			margin-right: 8px;
			display: block;
			max-width: 120px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;

			&__decoration {
				max-width: 100px;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		i {
			font-size: 12px;
			line-height: 1;
			color: $textColor2;
		}

		.iconfont_more {
			font-size: 14px;
			line-height: 1;
		}

		&__active {
			border-color: $borderColor4;

			span {
				color: $textColor1;
			}

			i {
				color: $textColor1;
				transform: rotateX(-180deg);
				transition: all 0.3s ease;

				&.iconfont_more {
					transform: rotateX(0deg);
				}
			}
		}
	}

	&__list {
		max-height: 291px;
		max-width: 320px;
		min-width: 212px;
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		column-gap: 16px;
		padding: 0 16px;

		&__ones {
			width: 212px;
			grid-template-columns: repeat(1, 1fr);
			grid-column-gap: 0;

			&:first-child:last-child {
				:deep(.fs-checkbox__label) {
					max-width: 166px;
				}
			}
		}

		&__specialWidth {
			width: min-content;
		}

		&__item {
			&:last-of-type {
				margin-bottom: 0;
			}
		}

		&__title {
			@include font12;
			padding: 6px 16px;
			font-weight: 600;
			color: #19191a;
		}

		&__content {
			display: grid;
			max-width: 100%;
			grid-template-columns: repeat(2, 1fr);
			padding: 0 16px;
			gap: 0 16px;
			&.content_more_ones {
				grid-template-columns: repeat(1, 1fr);
				grid-column-gap: 0;
				width: 182px;
			}

			:deep(.fs-checkbox) {
				margin-right: 0;
				padding: 8px 0;

				.fs-checkbox__label {
					@include font12;
					color: $textColor2;
					display: inline-block;
					max-width: 122px;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				.fs-checkbox__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}

				&.is-checked {
					.fs-checkbox__label {
						color: $textColor1;
					}

					.fs-checkbox__icon {
						color: $textColor2;
					}
				}

				&:hover:not(.is-disabled) {
					.fs-checkbox-box {
						.fs-checkbox__label {
							color: $textColor1;
						}
					}
				}
			}

			&.content_more_ones {
				width: 182px;
				min-width: 182px;
				max-width: 182px;
				grid-template-columns: repeat(1, 1fr);
				grid-column-gap: 0;
			}

			.filterSelect__list__checkbox:first-child:last-child {
				:deep(.fs-checkbox__label) {
					max-width: 220px;
				}
			}
		}

		.special {
			width: fit-content !important;

			:deep(.fs-checkbox-box) {
				width: 100% !important;

				.fs-checkbox__label {
					max-width: 100% !important;
				}
			}
		}

		&__checkbox {
			// width: 135px;
			margin-right: 24px;

			&:nth-child(3n) {
				margin-right: 0;
			}
		}

		li {
			cursor: pointer;
			color: #707070;
			display: inline-flex;
			align-items: center;

			:deep(.fs-checkbox) {
				margin-right: 0;
				padding: 8px 0px;

				.fs-checkbox-box {
					display: flex;
					align-items: center;
				}

				.fs-checkbox__label {
					@include font12;
					color: $textColor2;
					display: inline-block;
					max-width: 122px;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;

					&:hover {
						color: $textColor1;
					}
				}

				.fs-checkbox__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}

				&.is-checked {
					.fs-checkbox__label {
						color: $textColor1;
					}

					.fs-checkbox__icon {
						color: $textColor2;
					}
				}

				&:hover {
					.fs-checkbox-box {
						.fs-checkbox__icon {
							color: $textColor2;
						}
					}
				}

				&.is-disabled {
					.fs-checkbox__label {
						color: $textColor4;
					}

					&:hover {
						.fs-checkbox-box {
							.fs-checkbox__icon {
								color: $borderColor1;
							}
						}
					}
				}
			}
		}
	}

	i {
		font-size: 14px;
	}

	:deep(.fs-tooltip__popper) {
		padding: 8px 16px;
	}
}

.filterSelectActive {
	.fs-tooltip {
		.filterSelect__input {
			border: 1px solid $textColor1;
			border-radius: 999px;
			box-sizing: border-box;
		}
	}
}
