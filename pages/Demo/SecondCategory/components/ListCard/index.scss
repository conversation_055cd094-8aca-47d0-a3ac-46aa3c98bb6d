.list {
	display: flex;

	&__left {
		width: 180px;

		@media (max-width: 960px) {
			width: 120px;
		}

		.brandColor {
			margin-top: 8px;

			:deep(.brand) {
				margin-bottom: 0;
			}
		}

		img {
			width: 100%;
			height: 100%;
			display: block;
			margin: 0 auto;
		}

		:deep(.fs-checkbox) {
			margin: 0;
			height: 20px;
			position: absolute;
			top: 10px;
			left: 180px;

			.fs-checkbox-box {
				.fs-checkbox__icon {
					font-size: 20px;
					line-height: 1;
				}

				.fs-checkbox__label {
					display: none;
				}
			}

			.is-checked {
				.fs-checkbox-box {
					.fs-checkbox__icon {
						color: $textColor2;
					}
				}
			}

			&:hover {
				.fs-checkbox-box {
					.fs-checkbox__icon {
						color: $textColor2;
					}
				}
			}
		}
	}

	&__right {
		flex: 1;
		display: flex;
		justify-content: space-between;

		&__container {
			width: 60%;
			padding: 0 20px 0 40px;
			box-sizing: border-box;
		}

		&__title {
			font-size: 14px;
			color: $textColor1;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 3;
			-webkit-box-orient: vertical;
			margin-bottom: 12px;
			max-height: 66px;
			overflow: hidden;
			font-weight: 600;
			line-height: 22px;
			cursor: pointer;
			max-width: 550px;

			a {
				color: $textColor1;
				text-decoration: none;
				outline: 0;

				&:hover {
					text-decoration: underline;
				}
			}

			span {
				color: $textColor2;
				margin-left: 10px;
				font-size: 13px;
				font-weight: 400;
			}
		}

		&__taxPriceTips {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}

		&__label {
			overflow: hidden;
			margin-bottom: 8px;
			width: 100%;
			color: $textColor2;
			@include font12;
			cursor: default;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
		}

		&__txt {
			display: flex;
			align-items: center;
			margin-bottom: 4px;

			span {
				position: relative;
				@include font12;
				color: $textColor2;
				padding-left: 8px;

				:deep(a) {
					color: $textColor1;

					&:hover {
						text-decoration: underline;
					}
				}
			}

			i {
				display: inline-block;
				width: 4px;
				height: 4px;
				background: $textColor2;
				border-radius: 50%;
			}
		}

		&__opearation {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
		}

		//价格
		&__price {
			text-align: right;
			margin-bottom: 12px;

			// display: flex;
			// align-items: center;
			&__normal {
				color: $textColor1;
				@include font16;
				font-weight: 600;
			}

			&__tax {
				color: $textColor1;
				@include font16;
				font-weight: 600;
			}

			.box__tax__popper {
				margin-left: 4px;
			}

			&__taxProductsPrice {
				color: #b1b4bf;
				@include font12;
				font-weight: normal;
			}
		}

		&__inventory {
			margin-bottom: 12px;
			text-align: right;

			span {
				color: $textColor2;
				@include font12;
				display: block;
				word-break: break-word;
				margin-bottom: 8px;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}

		//销量 频率
		&__solidReview {
			display: flex;
			align-items: center;
			color: $textColor2;
			@include font12;

			&__solid {
				:first-child {
					color: #a65300;
				}
			}

			s {
				border-right: 1px solid $borderColor1;
				height: 10px;
				margin: 0 6px;
			}

			&__review {
				:first-child {
					color: $textColor5;
				}
			}

			&__solid,
			&__review {
				&:hover {
					cursor: pointer;
					text-decoration: underline;
				}
			}
		}

		&__cart {
			display: flex;
			margin-top: 12px;
			background-color: #ffffff;
			padding: 11px 16px;
			border-radius: 43px;
			box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
			align-items: center;
			cursor: pointer;
			transition: 0.3s all;

			i {
				font-size: 16px;
				color: #19191a;
				line-height: 1;
				margin-right: 8px;
			}

			> span {
				@include font12;
				color: #19191a;
			}

			&:hover {
				background-color: rgba(0, 0, 0, 0.04);
			}
		}
	}
}
