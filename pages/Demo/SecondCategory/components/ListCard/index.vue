<template>
	<div v-loading="loading" class="list">
		<div class="list__left">
			<FsCheckbox v-if="comparison" v-model="isCheckIcon" :disabled="!isChecked && comparisonList && comparisonList.length === 5" @change="handleChange"></FsCheckbox>
			<div ref="gridImg" class="list__left__img">
				<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)">
					<img :src="info.image" :alt="info.productsName" />
				</a>
			</div>
			<BrandColor v-model="loading" :categoryProduct="info" :cardIndex="cardIndex" />
		</div>
		<TextTag :tagType="info.isLabel ? info.isLabel : 0" :tagLabel="info.label" :styles="tagStyle" />
		<div class="list__right">
			<div class="list__right__container">
				<h3 class="list__right__title">
					<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)" @click="gaEventTitle">{{ info.productsName }}</a>
					<span>#{{ info.productsId }}</span>
				</h3>
				<div v-if="info.productsTag" class="list__right__label" :title="info.productsTag">
					{{ info.productsTag }}
				</div>
				<div v-if="(info?.serverHighlights && info?.serverHighlights.length) || info?.productsSellingPoints.length">
					<div v-for="(v, i) in info?.serverHighlights && info?.serverHighlights.length ? info.serverHighlights : info.productsSellingPoints" :key="i" class="list__right__txt">
						<i></i>
						<span v-html="v"></span>
					</div>
				</div>
			</div>
			<div class="list__right__opearation">
				<div v-if="!info?.isInquiry" class="list__right__price">
					<!-- 含税价 -->
					<div :class="{ list__right__taxPriceTips: info?.taxPriceTips }">
						<div v-if="info?.taxProductsPrice" class="list__right__price__normal" v-html="trimString(info.taxProductsPrice)"></div>
						<template v-if="info?.isShowTax && info?.taxPriceTips">
							<FsTooltip class="box__tax__popper" :content="info?.taxPriceTips" placement="top">
								<i class="iconfont iconxinxi_info" />
							</FsTooltip>
						</template>
					</div>
					<!-- 不含税价 -->
					<div
						v-if="info?.productsPrice"
						class="list__right__price__tax"
						:class="{ list__right__price__taxProductsPrice: info?.productsPrice && info?.taxProductsPrice }"
						v-html="trimString(info.productsPrice)"></div>
				</div>
				<div v-if="info?.isVirtualProduct" class="list__right__inventory">
					<span>{{ localeLang("secondCategory.in_stock") }}{{ inventory?.length ? `, ${inventory[0]?.delivery}` : "" }}</span>
				</div>
				<div v-else-if="inventory && inventory?.length" class="list__right__inventory">
					<span v-for="(v, i) in inventory" :key="i">{{ v.description }}</span>
				</div>
				<div class="list__right__solidReview">
					<div class="list__right__solidReview__solid">
						<span> {{ strSplit(info.productsSales)[0].toString() ?? 0 }}&nbsp;</span>
						<span> {{ strSplit(info.productsSales)[1] }} </span>
					</div>
					<s></s>
					<div class="list__right__solidReview__review">
						<span> {{ strSplit(info.productsReviews)[0].toString() ?? 0 }}&nbsp;</span>
						<span> {{ strSplit(info.productsReviews)[1] }} </span>
					</div>
				</div>
				<div v-if="!isRussia" class="list__right__cart" @click.stop="changeCartStatus(info.productsId)">
					<i v-if="info?.isVirtualProduct && info?.isInquiry" class="iconfont">&#xe675;</i>
					<i v-else class="iconfont">&#xe662;</i>
					<span v-if="info.productsId === 195659 || info.productsId === 231981 || info.productsId === 258683 || info.productsId === 282455">{{
						localeLang("secondCategory.start_trial")
					}}</span>
					<span v-else-if="info.productsId === 196175 || info.productsId === 178410">{{ localeLang("secondCategory.launch_demo") }}</span>
					<span v-else-if="info.productsId === 229069">
						{{ localeLang("secondCategory.download") }}
					</span>
					<span v-else>{{ localeLang("secondCategory.add_cart") }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip, FsCheckbox } from "fs-design";
import TextTag from "../TextTag/index.vue";
import BrandColor from "../BrandColor/index.vue";
import { trimString } from "../../../utils";
import type { CategoryContextKey } from "../../types";
import type { ListCardProps, ListCardEmits } from "./types";
const props = defineProps<ListCardProps>();
const emits = defineEmits<ListCardEmits>();
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const tagStyle = { position: "absolute", top: "8px", left: "12px" };
const gridImg = ref();
const loading = ref(false);
const websiteStore = useWebsiteStore();
const isRussia = computed(() => websiteStore.isRussia);
/**
 * 改变购物车状态
 * @param id 商品id
 * @description 点击展示购物车弹窗 provide 提供公共状态
 * @returns void
 */
const changeCartStatus = (id: number): void => {
	injectState.addCartId.value = id;
	injectState.addCartStatus.value = true;
};

const isChecked = ref(false);
const isCheckIcon = computed(() => {
	if (props.comparisonList?.length && props.sessionCompareIds && props.sessionCompareIds.includes(props.info.productsId)) {
		return true;
	} else {
		if (props.compareIds && props.compareIds.includes(props.info.productsId)) {
			return true;
		} else {
			return false;
		}
	}
});
// 选择对比产品
const handleChange = () => {
	if (
		props.comparisonList.filter((item: any) => {
			return item.id === props.info.productsId;
		}).length
	) {
		isChecked.value = false;
	} else {
		isChecked.value = true;
	}
	emits("change", { id: props.info.productsId, img: props.info.image });
};

const gaEventTitle = () => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: `Product List Page_${props.categoriesId}`,
			eventAction: "select_item",
			eventLabel: `text_${props.info.productsName}_${props.info.productsId}`,
			nonInteraction: false
		});
	}
};
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
