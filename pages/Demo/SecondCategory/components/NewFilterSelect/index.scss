.filterSelect {
	&__input {
		display: flex;
		align-items: center;
		padding: 8px 12px;
		color: $textColor2;
		font-size: 12px;
		border-radius: 9999px;
		cursor: pointer;
		background: #f6f6f8;

		span {
			margin-left: 4px;
		}

		i {
			font-size: 12px;
			color: $textColor1;
		}

		&__text {
			overflow: hidden;
			max-width: 53px;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		&__active {
			span {
				color: $textColor1;
			}
		}
	}

	&__list {
		max-height: 428px;
		width: 428px;
		display: grid;
		grid-column-gap: 36px;
		padding: 0 15px;
		grid-template-columns: repeat(2, calc((100% - 36px) / 2));

		&__ones {
			width: 212px;
			grid-template-columns: repeat(1, 1fr);
			grid-column-gap: 0;
		}

		&__specialWidth {
			width: min-content;
		}

		&__item {
			padding: 0 16px;

			&:first-of-type {
				.filterSelect__list__title {
					margin-top: 0;
				}
			}

			&:last-of-type {
				margin-bottom: 0;

				.filterSelect__list__content {
					border-bottom: 1px solid #e5e5e5;
				}

				.filterSelect__list__content .fs-checkbox {
					margin-bottom: 20px;
				}
			}
		}

		&__title {
			@include font12;
			font-weight: 600;
			color: #19191a;
			margin: 20px 0 8px 0;
		}

		&__content {
			display: grid;
			grid-column-gap: 36px;
			min-width: 400px;
			grid-template-columns: repeat(2, calc((100% - 36px) / 2));
			margin-bottom: -12px;

			&.content_more_ones {
				grid-template-columns: repeat(1, 1fr);
				grid-column-gap: 0;
				width: 182px;
			}

			:deep(.fs-checkbox) {
				margin-right: 0;
				margin-bottom: 12px;
				height: 20px;

				.fs-checkbox__label {
					@include font12;
					color: $textColor2;
					display: inline-block;
					max-width: 168px;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				.fs-checkbox__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}

				&.is-checked {
					.fs-checkbox__label {
						color: $textColor1;
					}

					.fs-checkbox__icon {
						color: $textColor2;
					}
				}

				&:hover {
					.fs-checkbox-box {
						.fs-checkbox__icon {
							color: $textColor2;
						}

						.fs-checkbox__label {
							color: $textColor1;
						}
					}
				}

				&.is-disabled {
					.fs-checkbox__label {
						color: $textColor4;
					}

					&:hover {
						.fs-checkbox-box {
							.fs-checkbox__icon {
								color: $borderColor1;
							}

							span {
								color: #ccc;
							}
						}
					}
				}
			}

			&.content_more_ones {
				width: 182px;
				min-width: 182px;
				max-width: 182px;
				grid-template-columns: repeat(1, 1fr);
				grid-column-gap: 0;
			}
		}

		.special {
			width: fit-content !important;

			:deep(.fs-checkbox-box) {
				width: 100% !important;

				.fs-checkbox__label {
					max-width: 100% !important;
				}
			}
		}

		&__checkbox {
			margin-right: 24px;

			&:nth-child(3n) {
				// margin-bottom: 0;
				margin-right: 0;
			}
		}

		li {
			cursor: pointer;
			color: #707070;
			display: inline-flex;
			align-items: center;

			:deep(.fs-checkbox) {
				margin-right: 0;
				padding: 8px 0;

				.fs-checkbox-box {
					display: flex;
					align-items: center;
				}

				.fs-checkbox__label {
					@include font12;
					color: $textColor2;
					display: inline-block;
					max-width: 168px;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				.fs-checkbox__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}

				&.is-checked {
					.fs-checkbox__label {
						color: $textColor1;
					}

					.fs-checkbox__icon {
						color: $textColor2;
					}
				}

				&:hover {
					.fs-checkbox-box {
						.fs-checkbox__icon {
							color: $textColor2;
						}
					}
				}

				&.is-disabled {
					.fs-checkbox__label {
						color: $textColor4;
					}

					&:hover {
						.fs-checkbox-box {
							.fs-checkbox__icon {
								color: $borderColor1;
							}
						}
					}
				}
			}
		}
	}

	i {
		font-size: 14px;
	}

	:deep(.fs-tooltip__popper) {
		padding: 8px 16px;
	}
}

.filterSelect__input {
	span {
		&:hover {
			color: $textColor1;
		}
	}
}

.filterSelect__input__icon {
	display: inline-flex;
	padding: 4px;

	i {
		display: inline-block;
		width: 14px;
		height: 14px;
	}
}

.filterSelect__footer {
	display: inline-block;
	padding: 6px 8px;
	border-radius: 4px;
	opacity: 0.3;
	background: #707070;
	color: #fff;
	margin: 28px 0 0 16px;
	cursor: not-allowed;
	font-size: 12px;
}

.filterSelect__footer__clear {
	cursor: pointer;
	opacity: 1;
}

.filterSelectActive {
	position: relative;

	span {
		color: $textColor1;
	}

	&::after {
		content: "";
		position: absolute;
		top: 10px;
		right: 12px;
		height: 4px;
		width: 4px;
		background: #4080ff;
		border-radius: 999px;
		box-sizing: border-box;
	}
}
