<template>
	<div v-if="dataSource.length" class="filterSelect" :class="{ filterSelectActive: reMapDataBoolean }">
		<FsTooltip
			ref="tooltip"
			:showArrow="false"
			placement="bottom-start"
			transition="fade"
			:offsetY="8"
			:popperContentStyle="popperContentStyle"
			@visible-change="handleVisibleChange">
			<div class="filterSelect__input">
				<div class="filterSelect__input__icon"><i class="iconfont">&#xe739;</i></div>
				<span class="filterSelect__input__text" :title="localeLang('secondCategory.filter')">{{ localeLang("secondCategory.filter") }}</span>
			</div>

			<template #content>
				<ul>
					<template v-for="(j, a) in dataSource" :key="a">
						<li class="filterSelect__list__item">
							<div class="filterSelect__list__title">{{ j.title }}</div>
							<div class="filterSelect__list__content">
								<template v-for="(k, b) in j.narrow" :key="b">
									<FsCheckbox v-model="k.checked" class="filterSelect__list__checkbox" :disabled="k.disabled" :title="k.name" size="small" @change="handleChange(j, b)">
										{{ k.name }}
									</FsCheckbox>
								</template>
							</div>
						</li>
					</template>
				</ul>
				<div class="filterSelect__footer" :class="{ filterSelect__footer__clear: filterVals.length > 0 }" @click="removeFilter">
					<span>{{ localeLang("secondCategory.menu.clear_all") }}</span>
				</div>
			</template>
		</FsTooltip>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip, FsCheckbox } from "fs-design";
import type { FsTooltipInstance } from "fs-design";
import useRouteInfo from "../../hooks/useRouteInfo";
import type { NarrowProps } from "../FilterSelectMenu/types";
import type { FilterSelectProps, FilterSelectEmits } from "./types";
import { reMapDataIsCheck } from "~/pages/Category/utils";
const props = defineProps<FilterSelectProps>();
const visible = ref(false);
const tooltip = ref<FsTooltipInstance>();
const localeLang = useLocaleLang();
const emits = defineEmits<FilterSelectEmits>();
const { filterVals } = useRouteInfo();
const dataSource = ref(reMapDataIsCheck(props.options) as NarrowProps[]);

const popperContentStyle = {
	padding: "16px 0",
	"max-width": "428px",
	"max-height": "428px",
	"overflow-y": "auto",
	"border-radius": "8px",
	"z-index": "109"
};

const reMapDataBoolean = computed(() => {
	const reMapIds = dataSource?.value
		.map(item => item.narrow)
		.flat()
		.map(x => x.narrowId);
	const reMapData = reMapIds.filter((item: number) => filterVals.value.includes(item));
	if (reMapData.length) {
		return true;
	}
	return false;
});

const handleChange = (v: NarrowProps, i: number) => {
	tooltip.value?.hide();
	visible.value = false;
	emits("changeOption", {
		option_id: v.productsNarrowByOptionsId,
		selectedItem: v,
		selectedIndex: i
	});
};

const handleVisibleChange = (val: boolean) => (visible.value = val);

const removeFilter = () => {
	if (filterVals.value.length) {
		tooltip.value?.hide();
		visible.value = false;
		navigateTo({ replace: true, query: {} });
	}
};

watch(
	() => props.options,
	newVal => {
		dataSource.value = reMapDataIsCheck(newVal) as NarrowProps[];
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
