<template>
	<FsTooltip ref="tooltip" :showArrow="false" :mobileForcePc="true" placement="bottom-end" :offsetY="isMobile ? 4 : 8" :popperContentStyle="popperContentStyle">
		<div class="filterSelectType__input" :class="{ filterSelectType__input__active: tooltip?.visible }" @click="toggleFilter">
			<!-- <div v-if="!isMobile" class="filterSelectType__input__icon">
				<i class="iconfont filterSelectType__input__prefixIcon">&#xe6b3;</i>
			</div> -->
			<div class="filterSelectType__input__content">
				<!-- m端前置的sordBy -->
				<div class="filterSelectType__input__sortBy">{{ localeLang("secondCategory.menu.sort_by") }}：</div>
				<div class="filterSelectType__input__sortByCheck">
					<!-- pc端没有选值默认的sordBy -->
					<span v-if="!filterTitle" class="filterSelectType__input__sortByPc">
						{{ localeLang("secondCategory.menu.sort_by") }}
					</span>
					{{ filterTitle }}
					<!-- m端默认选中展示的展示值 -->
					<span v-if="!filterTitle" class="filterSelectType__input__sortBy">
						{{ localeLang("secondCategory.sortList.Popu") }}
					</span>
				</div>
			</div>
			<div class="filterSelectType__input__icon">
				<i class="iconfont filterSelectType__input__arrow">&#xe704;</i>
			</div>
		</div>
		<template #content>
			<div class="filterSelectType__input__list">
				<span v-for="(v, index) in dataSource" :key="index" class="filterSelectType__input__list__sortBy" :class="{ active: v.name === sort_order }" @click="changeVal(v.name)">
					<i v-if="v.name === sort_order" class="iconfont">&#xf052;</i>
					<span>{{ v.label }}</span>
				</span>
				<!-- <FsRadio
					v-for="(v, index) in dataSource"
					:key="index"
					v-model="sort_order"
					size="small"
					:label="v.name"
					:title="v.name"
					@change="changeVal">
					{{ v.label }}
				</FsRadio> -->
			</div>
		</template>
		<template #footer> </template>
	</FsTooltip>
</template>

<script setup lang="ts">
import { FsTooltip } from "fs-design";
import type { FsTooltipInstance } from "fs-design";
import useRouteInfo from "../../hooks/useRouteInfo";
import type { FilterSelectTypeProps } from "./types";
defineProps<FilterSelectTypeProps>();
const route = useRoute();
const RouteInfo = useRouteInfo();
const { sort_order } = RouteInfo;
const deviceStore = useDeviceStore();
const isMobile = ref(false);
const tooltip = ref<FsTooltipInstance>();
const localeLang = useLocaleLang();
const popperContentStyle = ref({ padding: 0, "border-radius": "8px", "z-index": "109" });
const dataSource = ref([
	{
		name: "popularity",
		label: localeLang("secondCategory.sortList.Popu"),
		en_name: "Popularity"
	},
	{
		name: "new",
		label: localeLang("secondCategory.sortList.nf"),
		en_name: "Newest First"
	},
	// {
	// 	name: "limitedOffer",
	// 	label: localeLang("secondCategory.sortList.limitedOffer"),
	// 	en_name: "Price: limitedOffer"
	// },
	{
		name: "price",
		label: localeLang("secondCategory.sortList.plth"),
		en_name: "Price: Low to High"
	},
	{
		name: "priced",
		label: localeLang("secondCategory.sortList.phtl"),
		en_name: "Price: High to Low"
	},
	{
		name: "rate",
		label: localeLang("secondCategory.sortList.rhtl"),
		en_name: "Rate: High to Low"
	}
]);

const filterTitle = ref();

const changeVal = async (val: string) => {
	filterTitle.value = dataSource.value.find(v => v.label === val)?.name;
	tooltip?.value?.hide();
	await navigateTo({ path: route.path, query: { ...route.query, sort_order: val as string } });
};

onMounted(() => {
	isMobile.value = deviceStore.isMobile;
	if (!sort_order.value) {
		sort_order.value = "popularity";
	}
});

const toggleFilter = () => {
	if (isMobile.value) {
		if (tooltip?.value?.visible) {
			tooltip?.value?.hide();
		} else {
			tooltip?.value?.show();
		}
	}
};

watch(
	() => deviceStore.isMobile,
	newVal => {
		isMobile.value = newVal;
		if (newVal) {
			filterTitle.value = dataSource.value.find(v => v.label === sort_order.value)?.name;
		}
		if (!newVal && !route.query.sort_order) {
			filterTitle.value = "";
		}
	}
);

watch(
	() => route.fullPath,
	() => {
		if (!sort_order.value) {
			sort_order.value = "popularity";
			filterTitle.value = localeLang("secondCategory.sortList.Popu");
		} else {
			const title = dataSource.value.find(v => v.name === sort_order.value)?.label;
			filterTitle.value = title;
		}
	},
	{
		immediate: true
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
