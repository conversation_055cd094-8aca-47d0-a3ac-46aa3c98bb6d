<template>
	<FsDialog v-model="dialogStatus" transition="slide-down" className="mFilterSelect" v-bind="$attrs" width="100%" Bottom :contentStyle="contentStyle">
		<template #title>
			<div class="mFilterSelectMenu__head">
				<span>{{ titile }}</span>
			</div>
		</template>
		<template v-if="type === 'category'">
			<FsRadioGroup v-model="radioValue" className="mFilterSelectMenu__category">
				<FsRadio v-for="(item, index) in dataSource?.category" :key="index" :label="item.categories_id">
					{{ item.name }}
				</FsRadio>
			</FsRadioGroup>
		</template>
		<div v-else-if="type === 'narrow'" class="mFilterSelectMenu__narrow">
			<FsCheckbox
				v-for="(item, index) in dataSource?.narrow"
				:key="index"
				v-model="item.checked"
				:class="{ special: dataSource?.en_title === 'Inventory' }"
				:disabled="item.disabled"
				@change="handleChangeSelected(item, dataSource)">
				{{ item.name }}
			</FsCheckbox>
		</div>
		<template v-else>
			<ul class="mFilterSelectMenu__list__filter">
				<li v-for="(item, index) in dataSource.slice(showNum + 1)" :key="index" class="mFilterSelectMenu__list__item">
					<div class="mFilterSelectMenu__list__title">
						{{ item.title }}
					</div>
					<div class="mFilterSelectMenu__list__content">
						<FsCheckbox
							v-for="(v, i) in item.narrow"
							:key="i"
							v-model="v.checked"
							:class="{ special: item?.en_title === 'Inventory' }"
							:disabled="v.disabled"
							@change="handleChangeSelected(v, dataSource.slice(showNum + 1)[index])">
							{{ v.name }}
						</FsCheckbox>
					</div>
				</li>
			</ul>
		</template>
		<template #footer>
			<div class="mFilterSelectMenu__footer">
				<FsButton type="black" plain @click="handleReset">{{ localeLang("secondCategory.menu.clear_all") }}</FsButton>
				<FsButton type="red" @click="handleSubmit">{{ localeLang("secondCategory.submit") }}</FsButton>
			</div>
		</template>
	</FsDialog>
</template>

<script setup lang="ts">
import { watch, ref } from "vue";
import { FsButton, FsCheckbox, FsRadio, FsRadioGroup, FsDialog } from "fs-design";
import type { NarrowItemProps, NarrowProps } from "../FilterSelectMenu/types";
import type { MFilterSelectProps, MFilterSelectEmits } from "./types";
import { reMapDataIsCheck } from "@/pages/Category/utils";
const props = defineProps<MFilterSelectProps>();
const emits = defineEmits<MFilterSelectEmits>();
const route = useRoute();
const localeLang = useLocaleLang();
const dialogStatus = ref(props.modelValue);
const radioValue = ref();
const dataSource = ref();
const narrowOptions = ref();
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
const temporaryFilterOpt = ref();
const type = ref();
// const footerStyle = {
// 	padding: "16px 10px"
// };
const contentStyle = computed(() => {
	const baseStyle = {
		"max-height": "calc(100vh - 183px)",
		"overflow-x": "hidden"
	};
	const padding = "12px 16px";
	// const padding = Array.isArray(props.options) && type.value !== "catrgory" ? "16px 16px 24px" : "16px";
	return {
		...baseStyle,
		padding
	};
});

const titile = computed(() => {
	if (type.value === "category" || type.value === "narrow") {
		return dataSource.value.title;
	} else if (type.value === "filter") {
		return "Filter";
	}
});

/**
 * @description 获取初始查询参数 过滤符合和不符合filterParameters函数的参数
 * @returns {filterQueryResult, notFilterQueryResult}
 * @returns {filterQueryResult} { "option_id": 30,  "narrow_id": [41552]}
 * @returns {notFilterQueryResult}  { "sort_order": "rate", "page": "1" }
 */
const combinedQuery = computed(() => {
	if (route.query) {
		const filterQueryResult: { option_id: number; narrow_id: number[] }[] = [];
		const notFilterQueryResult: { [x: string]: any } = {};
		Object.entries(route.query).forEach(([key, value]) => {
			const option_id = Number(key);
			const narrow_id = typeof value === "string" ? value?.split(",").map(j => Number(j)) : [];
			if (filterParameters(key)) {
				filterQueryResult.push({ option_id, narrow_id });
			} else {
				notFilterQueryResult[key] = value;
			}
		});
		return { filterQueryResult, notFilterQueryResult };
	}
	return { filterQueryResult: [], notFilterQueryResult: [] };
});

/**
 * @description 获取最终查询参数
 * @returns {finallyQuery,notFilterQueryResult} { "option_id": 30,  "narrow_id": [41552],"sort_order": "rate", "page": "1"}
 */
const finallyQuery = computed(() => {
	const filterQuery: { [x: string]: string } = {};
	temporaryFilterOpt.value.forEach((item: { option_id: string | number; narrow_id: number[] }) => {
		filterQuery[item.option_id] = item.narrow_id.join();
	});
	return {
		...filterQuery,
		...combinedQuery.value.notFilterQueryResult
	};
});

/**
 *
 * @param currentItem 当前选中的item
 * @param data 选中的当前父级数据源
 * @returns {void}
 */
const handleChangeSelected = (currentItem: NarrowItemProps, data: NarrowProps): void => {
	const curOptionId = data.productsNarrowByOptionsId;
	const curNarrowId = currentItem.narrowId;
	const curIndex = temporaryFilterOpt.value.findIndex((item: { option_id: number }) => item.option_id === curOptionId);
	if (curIndex > -1) {
		const curItem = temporaryFilterOpt.value[curIndex];
		const narrowIds = curItem.narrow_id;
		if (narrowIds.includes(curNarrowId)) {
			if (narrowIds.length === 1) {
				temporaryFilterOpt.value.splice(curIndex, 1);
			} else {
				curItem.narrow_id = narrowIds.filter((item: number) => item !== curNarrowId);
			}
		} else {
			curItem.narrow_id.push(curNarrowId);
		}
	} else {
		temporaryFilterOpt.value.push({
			option_id: curOptionId,
			narrow_id: [curNarrowId]
		});
	}
};

const handleSubmit = async () => {
	emits("update:modelValue", false);
	if (type.value === "category") {
		const filterUrl = dataSource.value?.category.find((item: { categories_id: number }) => radioValue.value === item.categories_id)?.url;
		await navigateTo({ path: filterUrl });
	} else if (type.value === "narrow" || type.value === "filter") {
		await navigateTo({ path: route.path, query: finallyQuery.value as any });
	}
};

const handleReset = () => {
	if (type.value === "category") {
		radioValue.value = 0;
	} else {
		if (Array.isArray(dataSource.value)) {
			temporaryFilterOpt.value = [];
		} else {
			const curIndex = temporaryFilterOpt.value.findIndex((item: { option_id: number }) => item.option_id == dataSource.value.productsNarrowByOptionsId);
			if (curIndex > -1) {
				temporaryFilterOpt.value.splice(curIndex, 1);
			}
		}
		dataSource.value = reMapDataIsCheck(dataSource.value, true);
	}
};

onMounted(() => {
	if (props.crumbs.length === 4) {
		radioValue.value = props.crumbs.at(-1)?.id;
	}
});

watch(dialogStatus, newVal => !newVal && emits("update:modelValue", false));

watch(
	() => props.modelValue,
	newVal => {
		dialogStatus.value = newVal;
		type.value = props.showType;
		if (newVal) {
			dataSource.value = type.value !== "category" ? reMapDataIsCheck(props.options) : props.options;
			temporaryFilterOpt.value = JSON.parse(JSON.stringify(combinedQuery.value.filterQueryResult));
			narrowOptions.value = props.narrrow;
		}
	}
);

watch(isMobile, newVal => !newVal && (dialogStatus.value = false));
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
<style lang="scss">
.fs-dialog {
	.mFilterSelect {
		&.fs-dialog__contentM {
			border-radius: 8px 8px 0 0;
		}
	}
}
</style>
