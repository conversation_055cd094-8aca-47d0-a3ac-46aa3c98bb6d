<template>
	<div class="mFilterSelectMenu">
		<div ref="allWidth" class="mFilterSelectMenu__list" :class="crumbs.length === 4 && 'justifyBetween'">
			<FsTooltip
				ref="tooltipRef"
				class="mFilterSelectMenu__popper"
				mobileForcePc
				:popperContentStyle="popperContentStyle"
				:popperOptions="popperOptions"
				:showArrow="false"
				placement="bottom-end">
				<div v-show="isFourthLevelDisplay" class="mFilterSelectMenu__list__left">
					<div ref="all" class="mFilterSelectMenu__list__all" :class="{ isAct: activeIndex === 99 }" @click="handleSelect(99)">
						<span>{{ localeLang("secondCategory.all") }}({{ isFourthLevelDisplayAllCount }})</span>
					</div>
					<div v-for="(item, index) in firstNarrow" v-show="index <= dataShow" :ref="setRefs(index)" :key="index" class="list__left__item" @click="handleSelect(index)">
						<!-- <span :class="{ isAct: filterKeys.includes(item.productsNarrowByOptionsId) }">{{ item.title }}</span> -->
						<span :class="{ isAct: activeIndex === item.narrowId }">{{ item.name }}</span>
					</div>
					<div v-if="allWidthShow && JSON.stringify(dataShow)" ref="allNumber" class="mFilterSelectMenu__list__rest" @click="changeSelectBox">
						<span :class="{ isAct: resetNarrow.found }">+{{ firstNarrow.length - dataShow - 1 }}</span>
					</div>
				</div>
				<template v-if="allWidthShow" #content>
					<div class="mFilterSelectMenu__dropdown">
						<template v-for="(item, index) in firstNarrow.slice(dataShow + 1)" :key="item.narrowId">
							<div class="mFilterSelectMenu__dropdown__item" @click="handleSelect(dataShow + 1 + index)">
								<span :class="{ isAct: activeIndex === item.narrowId }">{{ item.name }}</span>
							</div>
						</template>
					</div>
				</template>
			</FsTooltip>
			<div v-if="narrow?.length" ref="allFilter" class="mFilterSelectMenu__list__right" :class="{ isAct: filterMenuShow }" @click="changeDialogType">
				<i class="iconfont">&#xe739;</i>
				<span>{{ localeLang("secondCategory.filter") }}</span>
			</div>
		</div>
		<div class="mFilterSelectMenu__result">
			<div class="mFilterSelectMenu__result__total">{{ Results }}</div>
			<FilterSelectType />
		</div>
		<MFilterSelect v-model="showFilter" showType="filter" :options="narrowMapData" :narrrow="narrow" :crumbs="crumbs" />
		<div class="mFilterSelectMenu__banner">
			<a :href="localeLink('/contact_sales_mail.html')">
				<img src="https://resource.fs.com/mall/generalImg/20240702155347ufe2qa.png" :alt="localeLang('secondCategory.contact_sales_mail')" />
				<div class="txt">
					<div class="title">{{ localeLang("secondCategory.contact_sales_mail") }}</div>
					<div class="btn">
						<span>GO</span>
					</div>
				</div>
			</a>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip } from "fs-design";
import type { MFilterSelectMenuProps } from "../MFilterSelectMenu/types";
import MFilterSelect from "../MFilterSelect/index.vue";
import FilterSelectType from "../FilterSelectType/index.vue";
import useRouteInfo from "../../hooks/useRouteInfo";
const props = defineProps<MFilterSelectMenuProps>();
const tooltipRef = ref();
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const options = ref(props.narrow);
const showFilter = ref(false);
const activeIndex = ref(99);
const route = useRoute();
const { filterKeys, filterVals } = useRouteInfo();
const emit = defineEmits();
// 动态计算筛选项
const allWidth = ref(); // 总宽度
const allNumber = ref(); // 筛选按钮宽度
const allFilter = ref(); // 剩余数字筛选按钮
const allWidthShow = ref(true); // 是否显示更多按钮
const dataShow = ref(0); // 显示的筛选项个数
const refs: any[] = [];

const popperOptions = {
	modifiers: [
		{
			name: "offset",
			options: {
				offset: [0, 8]
			}
		}
	]
};

// 是否平铺
const isFourthLevelDisplay = computed(() => props.narrow?.[0]?.isFourthLevelDisplay === 1);
const isFourthLevelDisplayAllCount = computed(() => {
	return isFourthLevelDisplay.value && props.narrow?.[0].narrow.reduce((total, item) => (total += item.count), 0);
});
const narrowMapData = computed(() => {
	return isFourthLevelDisplay.value ? props.narrow.slice(1) : props.narrow;
});
const firstNarrow = computed(() => props?.narrow?.[0]?.narrow ?? []);

const resetNarrow = computed(() => {
	const mapNarrowData = firstNarrow?.value.slice(dataShow?.value + 1, firstNarrow?.value.length);
	const commonValues = mapNarrowData.filter(item => filterVals.value.includes(item.narrowId));
	if (commonValues.length > 0) {
		activeIndex.value = commonValues[0].narrowId;
	}
	return {
		found: commonValues.length > 0,
		commonValues
	};
});

const popperContentStyle = ref({
	"max-width": "100%",
	width: "calc(100% - 32px)",
	padding: "12px",
	"border-radius": "8px"
});

const Results = computed(() => {
	return props.total ? localeLang("secondCategory.results").replace("xxxx", props.total) : "";
});

const findCommonValues = (arr1: any[], arr2: any[]): { found: boolean; commonValues: any[] } => {
	const commonValues = arr1.filter(item => arr2.includes(item));
	activeIndex.value = commonValues.length > 0 ? commonValues[0] : 99;
	return {
		found: commonValues.length > 0,
		commonValues
	};
};

const comparRouteResult = () => {
	const ids = firstNarrow.value.slice(0, dataShow.value + 1).map(item => item.narrowId);
	return findCommonValues(filterVals.value, ids);
};

const changeSelectBox = () => {
	if (tooltipRef.value?.visible) {
		tooltipRef.value?.hide();
	} else {
		tooltipRef.value?.show();
	}
};

const handleSelect = async (val: number) => {
	tooltipRef.value?.hide();
	const narrowFilterId = props.narrow?.[0].productsNarrowByOptionsId;
	const newRouterQuery = JSON.parse(JSON.stringify(route.query));
	if (val === 99) {
		activeIndex.value = 99;
		delete newRouterQuery[narrowFilterId];
		await navigateTo({ path: route.path, query: newRouterQuery });
	} else {
		const matchKeys = props.narrow?.[0].narrow?.[val].narrowId;
		if (activeIndex.value === props.narrow?.[0].narrow?.[val].narrowId) return;
		activeIndex.value = props.narrow?.[0].narrow?.[val].narrowId;
		newRouterQuery[narrowFilterId] = matchKeys;
		await navigateTo({ path: route.path, query: newRouterQuery });
	}
};

const changeDialogType = () => {
	options.value = narrowMapData.value;
	showFilter.value = !showFilter.value;
};

const filterMenuShow = computed(() => {
	for (const item of narrowMapData.value) {
		if (filterKeys.value.includes(item.productsNarrowByOptionsId)) {
			return true;
		}
	}
});

// 标记筛选项
const setRefs = (index: { toString: () => any }): ((el: any) => void) => {
	const refValue = toRef(refs, index.toString());
	return (el: any) => {
		refValue.value = el;
	};
};
// 计算方法
const getDom = async (): Promise<void> => {
	let domArr: number[] = []; // 筛选项集合
	dataShow.value = firstNarrow.value.length;
	await nextTick();
	if (!refs?.length) return;
	if (!domArr.length) {
		domArr = refs.map((dom: { offsetWidth: number }) => dom.offsetWidth + 4);
	}
	let sum = 0;
	const al = allWidth.value.offsetWidth - 40;
	console.log(al, sum, "xxxx");
	for (let i = 0; i < domArr.length; i++) {
		sum += domArr[i] + 6;
		const filterValue = allFilter.value ? allFilter.value.offsetWidth : 0;
		const restNumber = allNumber.value ? allNumber.value.offsetWidth : 0;
		if (sum + filterValue + restNumber + 40 >= al && i !== 0) {
			allWidthShow.value = true;
			break;
		} else {
			dataShow.value = i;
			allWidthShow.value = false;
		}
	}
};

onMounted(() => {
	getDom();
	comparRouteResult();
});

watch(showFilter, newVal => {
	if (!newVal) {
		activeIndex.value = 99;
	}
});
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
<style lang="scss">
.mFilterSelectMenu__popper {
	.fs-tooltip__popper {
		margin: 0 16px;
	}
}
</style>
