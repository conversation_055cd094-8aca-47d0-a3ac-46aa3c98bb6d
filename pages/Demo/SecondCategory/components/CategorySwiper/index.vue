<template>
	<div class="container">
		<div v-if="isMaskLeft" class="container__beforeBtn" @click="scrollTabs('left')">
			<!-- <i class="iconfont">&#xe702;</i> -->
		</div>
		<div ref="containerScroll" class="container__scroll" @scroll="handleScroll">
			<div ref="containerContent" class="container__content">
				<div
					v-for="(item, index) in data"
					:ref="el => (tabsRefs[index] = el)"
					:key="item.categoryId"
					:class="activeId === item.categoryId && 'active'"
					class="container__scroll__item"
					@click="handleTabClick(item, index)">
					<img :src="isMobile ? item.mobileImageUrl : item.pcImageUrl" alt="" />
					<span :ref="el => (spanRefs[index] = el)" :style="{ height: spanHeight ? spanHeight + 'px' : 'auto' }" :title="item.name" v-html="item.name"></span>
				</div>
			</div>
		</div>
		<div v-if="isMaskRight" class="container__afterBtn" @click="scrollTabs('right')">
			<!-- <i class="iconfont">&#xe703;</i> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import type { CategoryContextKey } from "../../types";
import type { ICategoryFilter, ICategprySwiper } from "./types";
import { getCategoryId } from "~/pages/Category/utils";
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const props = defineProps<ICategprySwiper>();
const isMaskLeft = ref(false);
const isMaskRight = ref(false);
const tabsRefs: any = {};
const containerScroll = ref();
const containerContent = ref();
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
const data = ref(props.categoryFilter ?? []);
const activeId = ref<number>();
const route = useRoute();
const spanHeight = ref();
const spanRefs: any = ref([]); // 存储每个 span 的 ref
const localeLink = useLocaleLink();

// 切换 Tab 时的处理
const handleTabClick = (item: ICategoryFilter, index: number) => {
	if (activeId.value === item.categoryId) return;
	activeId.value = item.categoryId;
	injectState.loading.value = true;
	navigateTo({ path: localeLink(item.url) });
};

// 滚动时的处理
const handleScroll = () => {
	const { scrollLeft, clientWidth, scrollWidth } = containerScroll.value!;
	isMaskLeft.value = scrollLeft > 16;
	isMaskRight.value = isMobile.value
		? scrollLeft + clientWidth + 48 < scrollWidth
		: scrollLeft + clientWidth < scrollWidth || containerContent.value!.getBoundingClientRect().width > clientWidth;
};

// 滚动到选中的 Tab
const scrollToActiveTab = (index: number) => {
	let sum = 0;
	for (let i = 0; i <= index; i++) {
		sum += tabsRefs[i].getBoundingClientRect().width + 24;
	}
	const halfWidth = innerWidth / 2;
	let scrollLeft = sum - tabsRefs[index].getBoundingClientRect().width / 2 - 24 + (isMobile.value ? 16 : 0);
	if (scrollLeft > halfWidth) {
		scrollLeft -= halfWidth;
	} else {
		scrollLeft = 0;
	}
	containerScroll.value!.scrollTo({ left: scrollLeft, behavior: "smooth" });
	updateMaskVisibility(index, sum);
};

// 更新左右按钮的显示状态
const updateMaskVisibility = (index: number, sum: number) => {
	const { left } = tabsRefs[index].getBoundingClientRect();
	const halfWidth = innerWidth / 2;
	if (left <= halfWidth / 2 && containerScroll.value!.scrollLeft > 16) {
		isMaskRight.value = true;
	} else if (left > halfWidth / 2 && sum >= innerWidth) {
		isMaskLeft.value = true;
	} else {
		isMaskLeft.value = false;
	}
	if (containerContent.value!.scrollWidth > containerScroll.value!.getBoundingClientRect().width) {
		isMaskRight.value = true;
	} else {
		isMaskRight.value = false;
	}
};

const scrollTabs = (direction: "left" | "right") => {
	const { scrollLeft, clientWidth, scrollWidth } = containerScroll.value!;
	let totalWidth = 0;
	let tabWidthToScroll = 0;
	let nextScrollPosition = scrollLeft;
	// 计算当前 visible 范围内的 Tab
	for (let i = 0; i < Object.values(tabsRefs).length; i++) {
		const tabWidth = tabsRefs[i].getBoundingClientRect().width + 24; // 包含间隙
		totalWidth += tabWidth;
		if (totalWidth > scrollLeft + clientWidth) {
			tabWidthToScroll = tabWidth;
			break;
		}
	}
	if (direction === "left") {
		nextScrollPosition = scrollLeft - tabWidthToScroll;
		if (nextScrollPosition < 0) {
			nextScrollPosition = 0; // 防止滚动超出左边界
		}
	}
	if (direction === "right") {
		nextScrollPosition = scrollLeft + tabWidthToScroll;
		if (nextScrollPosition > scrollWidth - clientWidth) {
			nextScrollPosition = scrollWidth - clientWidth; // 防止滚动超出右边界
		}
	}

	containerScroll.value!.scrollTo({
		left: nextScrollPosition,
		behavior: "smooth"
	});
};

const calculateMaxHeight = () => {
	let maxHeight = 20; // 默认高度
	console.log(spanRefs.value, "spanRefs.valuespanRefs.value");
	spanRefs.value.forEach((span: { offsetHeight: number }) => {
		if (span) {
			const height = span?.offsetHeight; // 获取 span 的实际高度
			console.log(height, 323);
			if (height > maxHeight) {
				maxHeight = height;
			}
		}
	});
	spanHeight.value = maxHeight; // 更新所有 span 的高度
};

onMounted(() => {
	const [id] = getCategoryId(route.path);
	activeId.value = id;
	// calculateMaxHeight();
	// nextTick(() => {
	// 	const index = data.value.findIndex(item => item.categoryId === activeId.value);
	// 	scrollToActiveTab(index ?? 0);
	// });
});
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
