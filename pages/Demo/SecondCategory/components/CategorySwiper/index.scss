.container {
	position: relative;

	@include mobile {
		padding: 0 0 0 16px;
	}
}

.container__scroll {
	display: flex;
	align-items: center;
	overflow: auto;
	width: 100%;

	&::-webkit-scrollbar {
		display: none;
	}

	@include pc {
		padding-right: 32px;
	}

	@include pad {
		padding-right: 48px;
	}

	@include mobile {
		padding-right: 48px;
	}
}

.container__content {
	display: flex;
}

.container__scroll__item {
	width: 120px;
	padding: 8px 12px;
	margin-right: 12px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	flex-shrink: 0;

	&:last-of-type {
		margin-right: 0;
	}

	img {
		width: 38px;
		height: 38px;
		mix-blend-mode: multiply;
	}

	span {
		text-align: center;
		font-size: 12px;
		line-height: 20px;
		color: $textColor1;
		// min-height: 40px;
		max-width: 120px;
		white-space: wrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}

	&:hover {
		background: #f6f6f8;
		border-radius: 8px;
	}
}

.container__beforeBtn,
.container__afterBtn {
	display: flex;
	align-items: center;
	background: #ffffff;
	position: absolute;
	top: 0;
	width: 32px;
	height: 100%;
	cursor: pointer;
	z-index: 2;

	@include pad {
		width: 48px;
		height: 100%;
	}

	@include mobile {
		width: 48px;
		height: 100%;
	}
}

.container__beforeBtn {
	left: 0;
	transform: rotate(180deg);
	background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);

	@include mobile {
		left: 16px;
		justify-content: center;
	}
}

.container__afterBtn {
	right: 0;
	justify-content: flex-end;
	background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);

	@include mobile {
		justify-content: center;
	}
}

.active {
	background: #f6f6f8;
	border-radius: 8px;
	&:hover {
		cursor: default;
	}
}
