<template>
	<FsTag v-if="tagType === 1" size="small" type="new" bg-color="#D9F5DF" color="#09832A" class="New" :style="styleObject">
		<span>New</span>
	</FsTag>
	<FsTag v-else-if="tagType === 2" size="small" type="hot" bg-color="#FFEBDD" color="#F56300" class="Hot" :style="styleObject">
		<span>Hot</span>
	</FsTag>
	<FsTag v-else-if="tagType === 4" size="small" bg-color="#ECDDF9" color="#512971" :style="styleObject">
		<span>Limited Offer</span>
	</FsTag>
	<FsTag v-else size="small" bg-color="#ECDDF9" color="#512971" :style="styleObject">
		<span>{{ tagLabel }}</span>
	</FsTag>
</template>

<script setup lang="ts">
import { FsTag } from "fs-design";
import type { TextTagProps } from "./types.ts";
const props = defineProps<TextTagProps>();
const styleObject = computed(() => {
	return {
		padding: "0 4px",
		"font-size": "12px",
		"font-weight": 600,
		...props.styles
	};
});
</script>
<style scoped lang="scss">
.New,
.Hot,
.Free {
	font-size: 12px;
	// margin: 0 8px;
}
.New {
	color: #10a300;
}
.Hot {
	color: #f56300;
}
.Free {
	color: #4080ff;
}

::deep(.fs-tag) {
	.fs-tag__content {
		span {
			padding: 0 4px !important;
		}
	}
}
</style>
