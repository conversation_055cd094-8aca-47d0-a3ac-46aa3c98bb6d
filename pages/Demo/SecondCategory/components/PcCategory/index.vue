<template>
	<div class="category">
		<template v-if="crumbs?.length">
			<Crumbs :crumbs="crumbs" :categoryFilter="categoryFilter" />
		</template>
		<div v-if="crumbs?.length && crumbs.length >= 3" class="category__secondaryTitle">{{ crumbs?.at(-1)?.name }}</div>
		<!-- 新版分类滚动列表 -->
		<CategorySwiper v-if="Array.isArray(categoryFilter.categories)" :categoryFilter="categoryFilter.categories" />
		<div class="splitline"></div>
		<div v-if="crumbs?.length" class="category__filter">
			<FilterSelectMenu :crumbs="crumbs" :narrow="narrowFilter" :total="pageConfig.total" />
		</div>
		<div v-if="categoryProducts?.length" class="category__grid" :class="{ category__paginationPadding: pageConfig.total <= 12 }">
			<div v-for="(item, index) in categoryProducts" :key="item.productsId" :class="'category__grid__item'">
				<GridCard :info="item" :inventory="inventoryDeliveryList?.[item.productsId]" :cardIndex="index" :categoriesId="categoryInfo.categoriesId" type="pc" />
			</div>
		</div>
		<div v-else class="category__noProducts">
			<div class="category__noProducts__tit">{{ localeLang("secondCategory.no_results_tit") }}</div>
			<div class="category__noProducts__msg">{{ localeLang("secondCategory.no_results_des") }}</div>
		</div>
		<div v-if="pageConfig.total > 12" class="category__pagination">
			<FsPagination v-bind="{ ...pageConfig }" @change="changePage" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsPagination } from "fs-design";
// import FilterSelectType from "../FilterSelectType/index.vue";
import CategorySwiper from "../CategorySwiper/index.vue";
import type { CategoryInfoProps } from "../../types";
import Crumbs from "../Crumbs/index.vue";
import FilterSelectMenu from "../FilterSelectMenu/index.vue";
import GridCard from "../GridCard/index.vue";
const localeLang = useLocaleLang();
const props = defineProps<CategoryInfoProps>();
const { crumbs, pageConfig, narrowFilter, categoryProducts, categoryFilter, inventoryDeliveryList, allCount } = toRefs(props.categoryInfo);

const route = useRoute();
const changePage = (n: number) => {
	if (n === pageConfig.value.current) return;
	navigateTo({ query: { ...route.query, page: n } });
};
</script>
<style scoped lang="scss">
@import url("./index.scss");
</style>
