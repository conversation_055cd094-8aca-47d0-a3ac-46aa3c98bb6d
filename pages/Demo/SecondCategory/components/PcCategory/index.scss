.category {
	width: 84vw;
	max-width: 1200px;
	margin: 0 auto;

	@media (max-width: 1024px) {
		width: calc(100% - 48px);
	}

	&__secondaryTitle {
		font-size: 24px;
		font-weight: 600;
		margin-bottom: 20px;
	}

	&__title {
		@include font24;
		color: $textColor1;
		font-weight: 600;

		&__mb20 {
			margin-bottom: 20px;
		}
	}

	&__filter {
		display: flex;
		align-items: center;

		&__secondary {
			margin-right: 8px;

			:deep(.filterSelectType__input) {
				border-radius: 999px;
			}
		}
	}

	&__grid,
	&__list {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		position: relative;
		gap: 12px;

		// &::after {
		// 	position: absolute;
		// 	content: "";
		// 	width: 100%;
		// 	height: 1px;
		// 	background-color: #fff;
		// 	bottom: -5px;
		// 	left: 0;
		// 	z-index: 1;
		// }

		@include pad {
			grid-template-columns: repeat(3, 1fr);
		}

		&__item {
			overflow: hidden;
			background: #fafbfb;
			border-radius: 4px;

			&:hover {
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

				:deep(.grid__img__cart) {
					// display: block;
					visibility: visible;
					align-items: center;
					justify-content: center;
					border-color: transparent;
					color: $textColor1;
				}
			}

			&:hover {
				background-color: #fff;
			}
		}
	}

	&__list {
		display: flex;
		flex-direction: column;

		&__item {
			padding: 20px;
			overflow: visible;
		}
	}

	&__noProducts {
		margin: 20px auto;
		padding: 40px 48px;
		background: #fff;

		> div {
			text-align: center;
		}

		&__tit {
			@include font16;
			color: $textColor1;
			font-weight: 600;
			margin-bottom: 8px;
		}

		&__msg {
			@include font14;
			color: $textColor3;
		}
	}

	&__pagination {
		margin: 20px auto 36px;
	}

	&__paginationPadding {
		padding-bottom: 36px;

		// 	&::after {
		// 		bottom: 30px;
		// 	}
	}
}
