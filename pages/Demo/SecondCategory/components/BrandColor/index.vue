<template>
	<div v-if="data?.length" class="brandColor" @mouseleave="poperShow = false">
		<div v-if="!color" class="brand">
			<span
				v-for="(v, i) in data"
				v-show="i <= dataShow"
				:ref="setRefs(i)"
				:key="i"
				:class="{ active: v.isDefault, blue: v.type == 2 }"
				:title="v.attributeNameStr ?? ''"
				@click="changeGoodsInfo(v, i)">
				{{ v.attributeNameStr ?? "" }}
			</span>
			<i
				ref="moreNumber"
				class="pcMoreNumber"
				:class="{
					active: defaultIndex > dataShow,
					iHover: poperShow,
					visible: moreNumberShow && isNumber(dataShowNumber) && Number(dataShowNumber) > 0
				}"
				@mouseenter="mouseeventMore"
				@click="clickMore"
				>+{{ dataShowNumber }}</i
			>
		</div>
		<div v-else class="colorAssemble">
			<span
				v-for="(v, i) in data"
				v-show="i <= dataShow"
				:key="i"
				:ref="setRefs(i)"
				:class="{ active: v.isDefault }"
				:title="v.attributeNameStr ?? ''"
				@click="changeGoodsInfo(v, i)">
				<i :class="{ WB: v.enAttrName == 'White' }" :style="{ background: productColor[v.enAttrName] }" />
			</span>
			<i
				v-show="moreNumberShow && isNumber(dataShowNumber) && Number(dataShowNumber) > 0"
				ref="moreNumber"
				class="num pcNum"
				:class="{
					active: defaultIndex > dataShow,
					iHover: poperShow,
					visible: moreNumberShow && isNumber(dataShowNumber) && Number(dataShowNumber) > 0
				}"
				@mouseenter="mouseeventMore"
				@click="clickMore"
				>+{{ dataShowNumber }}</i
			>
		</div>
		<div v-if="poperShow" class="poper">
			<div class="poper_bg">
				<div class="poper_list">
					<div
						v-for="(v, i) in data"
						v-show="i > dataShow"
						:key="i"
						class="poper_item"
						:class="{ active: v.isDefault, blue: v.type == 2, color: color }"
						@click="changeGoodsInfo(v, i)">
						<template v-if="color">
							<i :class="{ WB: v.enAttrName == 'White' }" :style="{ background: productColor[v.enAttrName] }"></i>
						</template>
						<template v-else>
							{{ v.attributeNameStr ?? "" }}
						</template>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { getCategoryId } from "../../../utils";
import type { CategoryContextKey, CategoryProductProps, RelateProduct } from "../../types";
import type { BrandColorProps } from "./types";
const props = defineProps<BrandColorProps>();
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const deviceStore = useDeviceStore();
const route = useRoute();
const localeLink = useLocaleLink();
const [id] = getCategoryId(route.path);
const dataShow = ref();
const loading = defineModel({ type: Boolean, default: false });
const defaultIndex = ref(-1);
const moreNumberShow = ref(true);
const moreNumber = ref();
const poperShow = ref(false);
const instance = getCurrentInstance();
let domArr: number[] = [];
const refs: any[] = [];

/**
 * @param {CategoryProductProps} data
 * @description: 映射数据源 获取展示数据
 * @return {RelateProduct[]}
 */
const remapData = (data: CategoryProductProps): RelateProduct[] => {
	const { attributes } = data;
	if (!attributes || !attributes.relateProducts) return [];
	return attributes.relateProducts.filter(productId => +productId !== 0);
};

const data = ref(remapData(props.categoryProduct));

const color: ComputedRef<boolean> = computed(() => {
	return props.categoryProduct.attributes && props.categoryProduct.attributes.isColor;
});

const dataShowNumber = computed(() => {
	return data.value.length - dataShow.value - 1;
});

/**
 * @param {number} index
 * @description: 设置每个dom元素的refs
 * @return {(el: any) => void}
 */
const setRefs = (index: { toString: () => any }): ((el: any) => void) => {
	const refValue = toRef(refs, index.toString());
	return (el: any) => {
		refValue.value = el;
	};
};

/**
 *
 * @param v 商品信息
 * @param i 索引
 * @description: 点击切换品牌或者色彩 上报inject事件 更新数据 改变视图
 * @return {void}
 */
const changeGoodsInfo = async (v: RelateProduct, i: number): Promise<void> => {
	if (v.type === 2) {
		location.href = localeLink(`/products/${v.productId}.html`);
	} else {
		if (defaultIndex.value === i) return;
		try {
			loading.value = true;
			data.value = data.value.map((item: RelateProduct, index: number) => {
				return index === i ? { ...item, default: true } : { ...item, default: false };
			});
			const { data: res } = await useRequest.post(`/cms/api/fs/category/getProductShow`, {
				data: {
					category_id: id ?? 0,
					products_id: v.productId
				}
			});
			const newVal = {
				...props.categoryProduct,
				attributes: {
					...props.categoryProduct.attributes,
					relate_products: data.value
				}
			};
			loading.value = false;
			defaultIndex.value = i;
			injectState.changeGoodsInfo(res?.value?.data, props.cardIndex, newVal);
		} catch (error) {
			loading.value = false;
		}
	}
};

const { screenWidth } = storeToRefs(deviceStore);
const mouseeventMore = () => {
	if (screenWidth.value > 768) {
		poperShow.value = true;
	}
};

const clickMore = () => {
	if (screenWidth.value <= 768) {
		poperShow.value = !poperShow.value;
	}
};

/**
 * @description: 获取所有dom元素的宽度
 * @return {Promise<void>}
 */
const getDom = async (): Promise<void> => {
	dataShow.value = data.value.length;
	await nextTick();
	if (!refs?.length) return;
	if (!domArr.length) {
		domArr = refs.map((dom: { offsetWidth: number }) => dom.offsetWidth + 4);
	}
	let sum = 0;
	const al = injectState.displayType.value === "grid" ? instance?.parent?.vnode?.el?.clientWidth : instance?.vnode?.el?.clientWidth;
	for (let i = 0; i < domArr.length; i++) {
		sum += domArr[i];
		if (sum + moreNumber.value.offsetWidth >= al - 40 && i !== 0) {
			moreNumberShow.value = true;
			break;
		} else {
			dataShow.value = i;
			moreNumberShow.value = false;
		}
	}
};

onMounted(() => getDom());

watch(
	() => deviceStore.screenWidth,
	() => getDom()
);

watch(
	data,
	newVal => {
		defaultIndex.value = newVal.findIndex((item: { isDefault: boolean }) => item.isDefault === true);
	},
	{
		immediate: true
	}
);

watch(
	() => props.categoryProduct,
	newVal => {
		data.value = remapData(newVal);
	}
);
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>
