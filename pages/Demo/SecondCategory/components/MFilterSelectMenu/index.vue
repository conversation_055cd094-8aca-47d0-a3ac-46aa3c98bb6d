<template>
	<div class="mFilterSelectMenu">
		<div ref="allWidth" class="mFilterSelectMenu__list">
			<div class="mFilterSelectMenu__list__left">
				<div
					v-for="(item, index) in narrow"
					v-show="index <= dataShow"
					:ref="setRefs(index)"
					:key="index"
					class="list__left__item"
					:class="{ active: activeIndex === index, isLine: filterKeys.includes(item.productsNarrowByOptionsId) }"
					@click="changeDialogType('narrow', index)">
					<span :class="{ isAct: filterKeys.includes(item.productsNarrowByOptionsId) }">{{ item.title }}</span>
					<i class="iconfont">&#xe704;</i>
				</div>
				<!-- </template> -->
			</div>
			<div
				v-if="narrow?.length && narrow.length - 1 > dataShow"
				ref="allNumber"
				class="mFilterSelectMenu__list__right"
				:class="{ isAct: filterMenuShow }"
				@click="changeDialogType('filter')">
				<i class="iconfont">&#xe739;</i>
				<span>{{ localeLang("secondCategory.filter") }}</span>
			</div>
		</div>

		<div class="mFilterSelectMenu__result">
			<div class="mFilterSelectMenu__result__total">{{ Results }}</div>
			<FilterSelectType />
		</div>
		<MFilterSelect v-model="showFilter" :showType="showType" :options="options" :narrrow="narrow" :showNum="dataShow ?? 0" :crumbs="crumbs" />
		<!-- <div class="mFilterSelectMenu__banner">
			<a :href="localeLink('/contact_sales_mail.html')">
				<img src="https://resource.fs.com/mall/generalImg/20240702155347ufe2qa.png" :alt="localeLang('secondCategory.contact_sales_mail')" />
				<div class="txt">
					<div class="title">{{ localeLang("secondCategory.contact_sales_mail") }}</div>
					<div class="btn">
						<span>GO</span>
					</div>
				</div>
			</a>
		</div> -->
	</div>
</template>

<script setup lang="ts">
import type { showType as ShowTypeProps } from "../MFilterSelect/types";
import type { MFilterSelectMenuProps } from "../MFilterSelectMenu/types";
import MFilterSelect from "../MFilterSelect/index.vue";
import FilterSelectType from "../FilterSelectType/index.vue";
import useRouteInfo from "../../hooks/useRouteInfo";
const props = defineProps<MFilterSelectMenuProps>();
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const options = ref();
const showFilter = ref(false);
const activeIndex = ref();
const deviceStore = useDeviceStore();
const { filterKeys } = useRouteInfo();
const showType = ref<ShowTypeProps>("category");
const showCategory = computed(() => props.crumbs.length === 3);
const minimumScreen = ref(false);
const Results = computed(() => {
	return props.total ? localeLang("secondCategory.results").replace("xxxx", props.total) : "";
});
onMounted(() => {
	minimumScreen.value = window.screen.width < 414;
});
const changeDialogType = (type: ShowTypeProps, index?: number) => {
	showFilter.value = !showFilter.value;
	showType.value = type;
	activeIndex.value = index;
	if (type === "category") {
		options.value = props.category;
	} else if (type === "narrow" && index !== undefined) {
		options.value = props.narrow[index];
	} else {
		options.value = props.narrow;
	}
};
const filterMenuShow = computed(() => {
	for (const item of props.narrow) {
		if (filterKeys.value.includes(item.productsNarrowByOptionsId)) {
			return true;
		}
	}
});

const emit = defineEmits();
//	开关切换
const checkbox = () => {
	getDom();
	emit("change");
};

// 动态计算筛选项

const allWidth = ref(); // 总宽度
const allNumber = ref(); // 更多按钮
const allWidthShow = ref(true); // 是否显示更多按钮
const dataShow = ref(); // 显示的筛选项个数
const refs: any[] = [];

// 标记筛选项
const setRefs = (index: { toString: () => any }): ((el: any) => void) => {
	const refValue = toRef(refs, index.toString());
	return (el: any) => {
		refValue.value = el;
	};
};
// 计算方法
const getDom = async (): Promise<void> => {
	let domArr: number[] = []; // 筛选项集合
	dataShow.value = props.narrow.length;
	await nextTick();
	if (!refs?.length) return;
	if (!domArr.length) {
		domArr = refs.map((dom: { offsetWidth: number }) => dom.offsetWidth + 4);
	}
	let sum = 0;
	const al = allWidth.value.offsetWidth - 40;
	for (let i = 0; i < domArr.length; i++) {
		sum += domArr[i] + 6;
		if (sum + (allNumber.value ? allNumber.value.offsetWidth : 0) + 20 >= al && i !== 0) {
			allWidthShow.value = true;
			break;
		} else {
			dataShow.value = i;
			allWidthShow.value = false;
		}
	}
};

onMounted(() => getDom());
watch(showFilter, newVal => {
	if (!newVal) {
		activeIndex.value = 10;
	}
});
watch(
	() => deviceStore.screenWidth,
	newVal => {
		minimumScreen.value = newVal < 414;
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
