<template>
	<div class="crumbs">
		<div ref="scroll" class="crumbs__box" @scroll="handleScroll">
			<div v-show="shouldShowLeftMask" class="scroll_mask left"></div>
			<div v-show="shouldShowRightMask" class="scroll_mask"></div>
			<template v-for="(item, index) in crumbsList" :key="index">
				<div class="crumbs__box__item" :class="{ crumbs__box__item__last: crumbs.length === 3 && index === 2 }">
					<a v-if="item.url" :href="crumbs.length === 3 && index === 2 ? 'javascript:;' : localeLink(item.url)">{{ item.name }}</a>
					<a v-else href="javascript:;">{{ item.name }}</a>
					<i class="iconfont">&#xf238;</i>
				</div>
			</template>
			<div class="crumbs__box__title" :class="{ 'crumbs__box__title--active': tooltip?.visible }">
				<span class="crumbs__box__title__content">{{ title }}</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { FsTooltipInstance } from "fs-design";
import { FsTooltip } from "fs-design";
import type { CrumbsProps, CategoryFilterProps, CategoryFilterItemProps } from "../../types";
import type { CrumbsComponentProps } from "./types";
const props = defineProps<CrumbsComponentProps>();
const crumbsList = ref<CrumbsProps[]>(props.crumbs);
const categorySource = ref<CategoryFilterProps>(props.categoryFilter);
const deviceStore = useDeviceStore();
const isMobile = ref(false);
const localeLink = useLocaleLink();
const title = ref();
const tooltip = ref<FsTooltipInstance>();
const popperContentStyle = { padding: "5px 0px", "border-radius": "8px" };
const scroll = ref<HTMLElement>();
const shouldShowLeftMask = ref(false);
const shouldShowRightMask = ref(false);
/**
 *  @description 判断是否需要展示左右两边遮罩层
 */
const handleScroll = () => {
	if (!scroll.value) return;
	const { scrollWidth, clientWidth, scrollLeft } = scroll.value || {};
	if (scrollWidth - clientWidth > 0) {
		shouldShowLeftMask.value = scrollLeft > 0;
		shouldShowRightMask.value = Math.round(scrollLeft + clientWidth) < scrollWidth;
	} else {
		shouldShowLeftMask.value = false;
		shouldShowRightMask.value = false;
	}
};

onMounted(() => {
	handleScroll();
	scroll?.value && useEventListener(scroll.value, "scroll", handleScroll);
	isMobile.value = deviceStore.isMobile;
});

watch(
	() => deviceStore.isMobile,
	newVal => (isMobile.value = newVal)
);

watch(
	[() => props.categoryFilter, () => props.crumbs],
	newVal => {
		categorySource.value = newVal[0];
		crumbsList.value = newVal[1];
		if (newVal[1].length > 3) {
			crumbsList.value = newVal[1].slice(0, 3);
			title.value = newVal[1].at(-1)?.name;
		}
	},
	{
		deep: true,
		immediate: true
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
