<template>
	<div class="mCategory">
		<template v-if="crumbs?.length">
			<Crumbs :crumbs="crumbs" :categoryFilter="categoryFilter" />
		</template>
		<div v-if="crumbs?.length && crumbs.length >= 3" class="mCategory__secondaryTitle">{{ crumbs?.at(-1)?.name }}</div>
		<CategorySwiper :categoryFilter="categoryFilter?.categories" />
		<div class="splitline"></div>
		<template v-if="categoryProducts?.length">
			<MFilterSelectMenu :crumbs="crumbs" :narrow="narrowFilter" :category="categoryFilter" :total="pageConfig.total" />
		</template>
		<div v-if="categoryProducts?.length" class="mCategory__grid">
			<div v-for="(item, index) in categoryProducts" :key="item.productsId" class="mCategory__grid__item">
				<GridCard :info="item" :inventory="inventoryDeliveryList?.[item.productsId]" :cardIndex="index" :categoriesId="categoryInfo.categoriesId" type="mobile" />
			</div>
		</div>
		<div v-else class="mCategory__noProducts">
			<div class="mCategory__noProducts__tit">{{ localeLang("secondCategory.no_results_tit") }}</div>
			<div class="mCategory__noProducts__msg">{{ localeLang("secondCategory.no_results_des") }}</div>
		</div>
		<div v-if="pageConfig?.total" class="mCategory__pagination">
			<FsPagination :total="pageConfig.total" :current="pageConfig.current" @change="changePage" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsPagination } from "fs-design";
import type { CategoryInfoProps } from "../../types";
import CategorySwiper from "../CategorySwiper/index.vue";
import Crumbs from "../Crumbs/index.vue";
import GridCard from "../GridCard/index.vue";
import MFilterSelectMenu from "../MFilterSelectMenu/index.vue";
const props = defineProps<CategoryInfoProps>();
const { crumbs, pageConfig, narrowFilter, categoryProducts, categoryFilter, inventoryDeliveryList } = toRefs(props.categoryInfo);

const route = useRoute();
const localeLang = useLocaleLang();
const changePage = (n: number) => {
	if (n === pageConfig.value.current) return;
	navigateTo({ query: { ...route.query, page: n } });
};
</script>
<style scoped lang="scss">
@import url("./index.scss");
</style>
