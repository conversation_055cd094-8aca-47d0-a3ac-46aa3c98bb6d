import type { CrumbsProps } from "../../types";
export interface NarrowItemProps {
	narrowId: number;
	name: string;
	isCheck: number;
	checked?: boolean;
	disabled?: boolean;
}

export interface NarrowProps {
	title: string;
	en_title: string;
	productsNarrowByOptionsId: number;
	narrow: NarrowItemProps[];
}

export interface FilterSelectMenuProps {
	narrow: Array<NarrowProps>;
	crumbs: Array<CrumbsProps>;
	total: number;
	comparison?: boolean | undefined;
	comparisonList?: object[] | any;
}

export interface ChangeOptionProps {
	option_id: number;
	selectedItem: NarrowProps;
	selectedIndex: number;
}
