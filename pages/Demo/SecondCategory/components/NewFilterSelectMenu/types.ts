import type { CrumbsProps } from "../../types";
export interface NarrowItemProps {
	narrowId: number;
	name: string;
	isCheck: number;
	checked?: boolean;
	disabled?: boolean;
	count: number;
}

export interface NarrowProps {
	title: string;
	enTitle: string;
	productsNarrowByOptionsId: number;
	narrow: NarrowItemProps[];
	isFourthLevelDisplay: number;
}

export interface FilterSelectMenuProps {
	narrow: Array<NarrowProps>;
	crumbs: Array<CrumbsProps>;
	allCount: number;
}

export interface ChangeOptionProps {
	option_id: number;
	selectedItem: NarrowProps;
	selectedIndex: number;
}
