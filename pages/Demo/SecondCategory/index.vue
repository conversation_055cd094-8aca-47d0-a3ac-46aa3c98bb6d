<template>
	<div>
		<div class="pcCategory">
			<PcCategory v-loading.fullscreen="loading" :categoryInfo="categoryInfo" />
		</div>
		<div class="mCategory">
			<MCategory v-loading.fullscreen="loading" :categoryInfo="categoryInfo" />
		</div>
		<!-- <AddCart :id="addCartId" v-model="addCartStatus" /> -->
		<AddCartSuccessPopup v-model="showAddCartPopup" :loading="addCartLoading" />
	</div>
</template>

<script setup lang="ts">
import { getCategoryId, setCategoryMeta } from "../utils";
import PcCategory from "./components/PcCategory/index.vue";
import MCategory from "./components/MCategory/index.vue";
import type { CategoryApiProps, CategoryProps, CategoryContextKey, CardType, CategoryFilterProps, CategoryProductProps } from "./types";
import AddCartSuccessPopup from "@/popup/AddCartSuccessPopup/index.vue";
const { website } = useWebsiteStore();
const route = useRoute();
const router = useRouter();
const localeLink = useLocaleLink();
const scrollToElement = useScrollToElement();
const setMeta = useSetMeta();
const loading = ref(true);
const { animateToCart } = useCartAnimation();

const deviceStore = useDeviceStore();
const isMobile1024 = computed(() => deviceStore.isMobile1024);

const addCartLoading = ref(false);
const bdRequest = useBdRequest();
const cartStore = useCartStore();
const separateList = ref([]);
const addCartStatus = ref(false);
const cartAnimationstartElement = ref(0);
const showAddCartPopup = computed({
	get: () => {
		return addCartStatus.value && separateList.value?.some((item: { show_add_window: number }) => item.show_add_window === 1);
	},
	set: (val: boolean) => {
		addCartStatus.value = val;
	}
});
const displayType: Ref<CardType> = ref("grid");
const addCartId = ref(0);
const result = ref();
const errorResult = ref();
const params: Ref<CategoryApiProps> = ref({
	categoryId: 0,
	sortOrder: route.query.sort_order ? route.query.sort_order : "popularity",
	filters: [],
	current: route.query.page ? +route.query.page : 1,
	meta: {},
	isLocalWarehouse: 0,
	size: 12
});
const categoryInfo: CategoryProps = reactive({
	categoriesId: 0,
	categoryProducts: [],
	waterfall: [],
	categoryFilter: {} as CategoryFilterProps,
	inventoryDeliveryList: [],
	narrowFilter: [],
	filterList: [],
	sortOrder: "popularity",
	crumbs: [],
	pageConfig: {
		pageSize: 12,
		current: 1,
		total: 0
	},
	allCount: 0
});
const backTop = () => {
	scrollToElement({ ele: document.body });
};

/**
 *
 * @param n 新的商品信息
 * @param cardIndex 卡片索引
 * @param categoryProductInfo 切换品牌或者色彩之前的卡片信息
 * @returns void
 */
const changeGoodsInfo = (n: CategoryProductProps, cardIndex: number, categoryProductInfo: CategoryProductProps): void => {
	const keys = Object.keys(categoryInfo.inventoryDeliveryList);
	if (keys.includes(categoryProductInfo.productsId.toString())) {
		const newKey = n.productsId;
		delete categoryInfo.inventoryDeliveryList[categoryProductInfo.productsId];
		categoryInfo.inventoryDeliveryList[newKey] = n.inventoryDeliveryList;
		categoryInfo.categoryProducts[cardIndex] = Object.assign(categoryProductInfo, n);
	}
};

/**
 * 获取分类数据
 * @param null
 * @returns {Promise<void>}
 */
const getCategoryData = async (): Promise<void> => {
	try {
		const metaParams = getMetaParams(route);
		const filterMap = ref([]) as any;
		const [id] = getCategoryId(route.path);
		let isLocal = 0;
		for (const i in route.query) {
			if (filterParameters(i)) {
				if (i != "-5") {
					const queryStr = route.query[i]?.toString();
					if (queryStr) {
						filterMap.value.push({ optionId: i, valueIds: queryStr.split(",") });
					}
				} else {
					isLocal = 1;
				}
			}
		}
		params.value = {
			categoryId: id,
			sortOrder: route.query.sort_order ? route.query.sort_order : "popularity",
			filters: filterMap,
			current: route.query.page ? +route.query.page : 1,
			meta: metaParams,
			isLocalWarehouse: isLocal,
			size: 12
		};

		// 后续static服务恢复 取消注释
		// const apiEndpoint = isNullObject(route.query) ? "/static" : "/cms/api/fs/category/productList";
		// const requestData = isNullObject(route.query)
		// 	? {
		// 			method: "POST",
		// 			url: "/cms/api/fs/category/productList",
		// 			filterId: `${id}_${route.query.sort_order ? route.query.sort_order : "popularity"}_${route.query.page ? +route.query.page : 1}`,
		// 			moduleName: "SecondCategory",
		// 			params: params.value
		// 		}
		// 	: params.value;
		const apiEndpoint = "/cms/api/fs/category/productList";
		loading.value = true;
		const { data, error } = await useRequest.post<CategoryApiProps>(apiEndpoint, {
			data: params.value
		});
		result.value = data?.value?.data;
		errorResult.value = error?.value?.code;
		if (result.value && !result.value?.isRedirect) {
			let routePath = result.value?.crumbs?.at(-1)?.url;
			if (website !== "en") {
				routePath = localeLink(routePath);
			}
			if (routePath !== route.path) {
				router.push({
					path: route.path,
					query: route.query || {}
				});
			}
			const filterList = Object.values(filterMap).flatMap(item => item);
			const categoryState = {
				categoriesId: id,
				...result.value,
				filterList,
				sortOrder: route.query.sort_order ? route.query.sort_order : "popularity",
				pageConfig: {
					pageSize: 12,
					current: route.query.page ? +route.query.page : 1,
					total: result.value.total
				}
			};
			Object.assign(categoryInfo, categoryState);
			setMeta(setCategoryMeta(route.query, result.value));
		} else if (result.value?.isRedirect) {
			location.href = localeLink(result.value?.redirectUrl);
		}

		if (errorResult.value === 422) {
			location.href = localeLink("/500.html");
		}
		loading.value = false;
	} catch (error) {
		loading.value = false;
	}
};

const addToCart = async () => {
	try {
		bdRequest([
			{
				logidUrl: location.href,
				newType: 46
			}
		]);
		addCartLoading.value = true;
		const obj = {
			products: [
				{
					products_id: addCartId.value,
					qty: 1,
					isChecked: 1,
					attributes: {}
				}
			]
		};
		separateList.value = [];
		const { data, error } = await useRequest.post(`/api/cart/addNew`, { data: obj });
		if (data.value?.data) {
			cartStore.setAddCartData(data.value.data);
			// cartStore.getCart();
			separateList.value = data.value.data?.cartList;
			// 只有当没有 show_add_window 为 1 的商品时才重置 addCartStatus
			const hasShowAddWindow = data.value.data.cartList?.some((item: { show_add_window: number }) => item.show_add_window === 1);
			if (!hasShowAddWindow) {
				addCartStatus.value = false;
			}

			addCartLoading.value = false;

			console.log(cartAnimationstartElement.value);
			if (Math.ceil((cartAnimationstartElement.value + 1) / 4) % 2 !== 0) {
				animateToCart({
					startElement: document.querySelectorAll(".pcCategory .category__grid__item")[cartAnimationstartElement.value].querySelector("img"),
					endElement: isMobile1024.value ? document?.querySelector(".m_header_right .cart_box") : document?.querySelector(".header_ctn .cart_box"),
					imageUrl: document.querySelectorAll(".pcCategory .category__grid__item")[cartAnimationstartElement.value].querySelector("img").src || "",
					duration: 800,
					onComplete: () => {
						const cartBox = isMobile1024.value ? document?.querySelector(".m_header_right .cart_box") : document?.querySelector(".header_ctn .cart_box");
						cartBox?.classList.add("cartAnimation");
						setTimeout(() => {
							cartStore.setCartData(data.value.data);
						}, 400);
						setTimeout(() => {
							cartBox?.classList.remove("cartAnimation");
						}, 500);
					}
				});
			} else {
				document?.querySelector(".header_ctn .cart_box")?.classList.add("cartAnimation");
				setTimeout(() => {
					cartStore.setCartData(data.value.data);
				}, 400);
				setTimeout(() => {
					document?.querySelector(".header_ctn .cart_box")?.classList.remove("cartAnimation");
				}, 500);
			}

			// 			flyToCart({
			//     startEl:document.querySelectorAll(".pcCategory .category__grid__item")[cartAnimationstartElement.value],
			//     endEl: document?.querySelector(".header_ctn .cart_box"),
			//     imgSrc:  document.querySelectorAll(".pcCategory .category__grid__item")[cartAnimationstartElement.value].querySelector("img").src || '',
			//     onEnd: () => {
			//       // 动画结束后的回调，例如更新购物车数量等
			//       console.log('商品已添加到购物车');
			//     }
			//   });
		}
	} finally {
		addCartLoading.value = false;
	}
};

const getNewSource = async () => {
	await nextTick();
	const ids = categoryInfo.categoryProducts.map(item => item.productsId);
	const { data } = await useRequest.post("/api/category_new/newSource", {
		data: { product_ids: ids }
	});
	categoryInfo.inventoryDeliveryList = data?.value?.data;
};

onMounted(() => {
	getNewSource();
	// if (categoryInfo?.crumbs.slice(-1)[0]?.url !== route.path) {
	// 	navigateTo({ path: localeLink(categoryInfo.crumbs?.at(-1)!.url), query: route.query });
	// }
});

await getCategoryData();
useHeadMeta();
/**
 * 提供公共数据
 * @param displayType  "grid" | "list"
 * @function changeGoodsInfo  修改商品信息
 */
provide("CategoryContextKey", {
	loading,
	addCartLoading,
	displayType,
	addCartStatus,
	addCartId,
	changeGoodsInfo,
	cartAnimationstartElement
} as CategoryContextKey);

/**
 * 监听路由变化
 * @description 重新获取数据
 */
watch(
	() => route.fullPath,
	async () => {
		await getCategoryData();
		await getNewSource();
		backTop();
	}
);
watch(addCartStatus, (newVal: boolean) => {
	if (newVal) {
		addToCart();
	}
});
</script>
<style lang="scss" scoped>
.pcCategory {
	display: block;
	@include mobile {
		display: none;
	}
}

.mCategory {
	display: none;
	@media (max-width: 767px) {
		display: block;
	}
}
</style>
