// Swiper pagination 样式自定义部分
:deep(.swiper) {
	overflow: initial;
}
:deep(.swiper-pagination) {
	display: flex;
	justify-content: center;
	bottom: -20px;
}
:deep(.swiper-pagination-bullet) {
	opacity: 0.4;
	margin: 0 5px;
	background: $textColor2;
	transition: all 0.3s;
}
:deep(.swiper-pagination-bullet-active) {
	opacity: 1;
	transition: all 0.3s;
	background: $textColor2;
	width: 20px;
	border-radius: 8px;
}

// 带箭头的无边框按钮样式
:deep(.fs-button) {
	justify-content: flex-start;
	&.is-text {
		padding: 0;
		height: auto;
		margin-top: 12px;
		@include font13;
		.fs-button--suffix {
			margin-right: 4px;
		}
		i {
			color: $textColor1;
		}
	}
}
