<template>
	<div v-if="hasData" class="detail_component" :class="{ isPadding: special(contentData.componentId), bg3: showBg(contentData.componentId) }">
		<ComponentHeader v-if="special(contentData.componentId)" :contentData="contentData"></ComponentHeader>
		<component :is="currentComponent" :contentData="contentData"></component>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { propsData } from "../../types";
import ComponentHeader from "../SpecialComponents/components/ComponentHeader.vue";
import Banner from "../Banner/Banner.vue";
import WhyUs from "../WhyUs/WhyUs.vue";
import FSLabs from "../FSLabs/FSLabs.vue";
import SolutionDesign from "../SolutionDesign/SolutionDesign.vue";
import Testing from "../Testing/Testing.vue";
import Products from "../Products/Products.vue";
import CustomerSuccess from "../CustomerSuccess/CustomerSuccess.vue";
import Resource from "../Resource/Resource.vue";
import Service from "../Service/Service.vue";
import FeatureSolutions from "../FeatureSolutions/FeatureSolutions.vue";
defineOptions({
	name: "SpecialComponents"
});

// 组件映射表
const componentMap = {
	1: Banner,
	2: WhyUs,
	3: FSLabs,
	4: SolutionDesign,
	5: Testing,
	6: Products,
	7: CustomerSuccess,
	8: Resource,
	9: Service,
	17: FeatureSolutions // 新增的17号组件
} as Record<number, Component>;

const props = defineProps<propsData>();

// 当前模块
const currentComponent = computed(() => {
	return componentMap[props.contentData.componentId] ?? null; // 使用 ?? 处理undefined
});

// 判断模块是否显示
const hasData = computed(() => props.contentData && Object.keys(props.contentData.content).length > 0);

// 判断特殊模块
const special = (num: number) => num !== 1 && num !== 9;

// 显示背景色 定义参与背景交替的组件ID
const bgOrderIds = [3, 4, 5, 6, 17] as const;
const showBg = (componentId: any) => {
	const index = bgOrderIds.indexOf(componentId);
	return index >= 0 && index % 2 === 0;
};
</script>

<style scoped lang="scss">
@import url("./SpecialComponents.scss");
</style>
