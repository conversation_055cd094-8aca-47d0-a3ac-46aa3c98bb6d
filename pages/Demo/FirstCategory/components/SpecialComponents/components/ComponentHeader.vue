<template>
	<header>
		<h2 v-html="contentData.content?.title"></h2>
		<p v-if="contentData.content?.description" class="describe">{{ contentData.content.description }}</p>
		<a v-if="contentData.content?.href && contentData.content.text" :href="linkTo(contentData.content.href)">{{ contentData.content.text }}</a>
	</header>
</template>

<script setup lang="ts">
import type { propsData } from "../../../types";
import { linkTo } from "../../../../utils";
defineOptions({
	name: "ComponentHeader"
});
// 获取数据
const props = defineProps<propsData>();
</script>

<style scoped lang="scss">
@import url("./ComponentHeader.scss");
</style>
