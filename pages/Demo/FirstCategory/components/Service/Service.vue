<template>
	<section class="fs-service">
		<div class="fs-service-content">
			<div class="fs-service-title">
				<p v-html="contentData.content.title"></p>
			</div>
			<ul class="fs-service-list">
				<li v-for="(item, index) in contentData.content.modules" :key="index">
					<figure>
						<img class="icon" :src="item.image" :alt="item.title" />
						<figcaption>
							<a :href="localeLink(item.url)" :title="item.title" @click.stop="bdClick">{{ item.title }}</a>
							<p>{{ item.description }}</p>
						</figcaption>
					</figure>
				</li>
			</ul>
		</div>
	</section>
</template>

<script setup lang="ts">
import type { propsData } from "../../types";
defineOptions({
	name: "Service"
});
// 传参和路由信息
const props = defineProps<propsData>();
const localeLink = useLocaleLink();
const bdRequest = useBdRequest();

const bdClick = () => {
	bdRequest([
		{
			logidUrl: location.href,
			newType: 20
		}
	]);
};
</script>

<style lang="scss" scoped>
@import url("./Service.scss");
</style>
