<template>
	<section class="fs-resource">
		<ul class="fs-resource-list">
			<swiper :modules="modules" :slidesPerView="slidesPerView" :spaceBetween="spaceBetween" :pagination="pagination">
				<swiper-slide v-for="(item, index) in list" :key="index">
					<div class="fs-resource-item">
						<a v-for="(t, i) in item" :key="i" :href="linkTo(t.url)">
							<figure>
								<div>
									<img :src="t.image" :alt="t.title" />
								</div>
								<figcaption>
									<p class="title">{{ t.title }}</p>
									<FsButton class="more" iconPlacement="suffix" text>
										<template #default>{{ t.text }}</template>
										<template #icon>
											<span class="iconfont right">&#xe703;</span>
										</template>
									</FsButton>
								</figcaption>
							</figure>
						</a>
					</div>
				</swiper-slide>
			</swiper>
		</ul>
		<figure class="fs-resource-notes">
			<img class="punctuation" src="https://img-en.fs.com/network_img_new/cate/fs-cs/icon-douhao.svg" alt="quote icon" />
			<figcaption>
				<p class="txt">{{ contentData.content.comment }}</p>
				<p class="by">{{ contentData.content.info }}</p>
			</figcaption>
			<img class="pic" src="https://img-en.fs.com/network_img_new/cate/fs-cs/cs-normal.svg" alt="quote icon" />
		</figure>
	</section>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import { computed } from "vue";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";
import { linkTo } from "../../../utils";
import type { propsData } from "../../types";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";
defineOptions({
	name: "Resource"
});

// 传参设备路由信息
const props = defineProps<propsData>();
const deviceStore = useDeviceStore();

// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const slidesPerView = 1;
const spaceBetween = 16;
// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(props.contentData.content.modules, 1) : ReorganizeArrays(props.contentData.content.modules, 0)));
</script>

<style lang="scss" scoped>
@import url("./Resource.scss");
</style>
