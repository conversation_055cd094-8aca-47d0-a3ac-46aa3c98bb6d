.fs-resource {
	@include contentWidth;
	> h2 {
		@include font24;
		color: $textColor1;
		text-align: center;
	}
	.fs-resource-describe {
		margin-top: 12px;
		@include font14;
		color: $textColor2;
		text-align: center;
	}
	.fs-resource-list {
		margin-top: 24px;
		.fs-resource-item {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 20px;
			> a {
				height: 100%;
				background-color: $bgColor6;
				transition: all 0.3s ease-in-out;
				overflow: hidden;
				border-radius: 8px;
				&:hover {
					text-decoration: none;
					box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
					> figure {
						> figcaption {
							.more {
								:deep(.fs-button--suffix) {
									text-decoration: underline;
								}
							}
						}
					}
				}
				> figure {
					height: 100%;
					display: flex;
					flex-direction: column;
					> div {
						position: relative;
						padding-top: 56.5%;
						> img {
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
						}
					}
					> figcaption {
						flex: 1;
						padding: 20px 20px 28px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						border-radius: 0 0 8px 8px;
						border: 1px solid $borderColor1;
						border-top: none;
						.title {
							@include font14;
							font-weight: 600;
							color: $textColor1;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
							overflow: hidden;
							margin-top: 12px;
						}
						.more {
							text-decoration: none;
							> .right {
								font-size: 12px;
								line-height: 1;
								color: $textColor1;
							}
							&:hover {
								.right {
									text-decoration: none;
								}
							}
						}
					}
				}
			}
		}
	}
	.fs-resource-notes {
		display: flex;
		justify-content: space-between;
		padding-top: 48px;
		margin-top: 48px;
		border-top: 1px solid #e5e5e5;
		.punctuation {
			width: 32px;
			height: 32px;
		}
		> figcaption {
			flex: 1;
			margin: 0 80px 0 24px;
			.txt {
				@include font14;
				color: $textColor1;
			}
			.by {
				@include font14;
				color: $textColor2;
				margin-top: 36px;
			}
		}
		.pic {
			width: 154px;
			height: 72px;
		}
	}
	@include mobile {
		overflow: hidden;
		.fs-resource-list {
			.fs-resource-item {
				grid-template-columns: repeat(1, 1fr);
			}
		}
		.fs-resource-notes {
			margin-top: 56px;
			padding-top: 36px;
			flex-direction: column;
			align-items: center;
			> figcaption {
				margin: 12px auto 20px;
			}
		}
	}
}
