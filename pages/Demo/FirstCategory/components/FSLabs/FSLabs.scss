.fs-labs {
	overflow: hidden;
	@include contentWidth;
	.fs-labs-view {
		display: flex;
		margin-top: 24px;
		> .fs-labs-video {
			width: 50%;
			position: relative;
			border-radius: 8px 0 0 8px;
			overflow: hidden;
			> img {
				width: 100%;
				height: 100%;
			}
			.play {
				cursor: pointer;
				position: absolute;
				padding: 8px 16px;
				border-radius: 3px;
				background: rgba(25, 25, 26, 0.6);
				display: flex;
				align-items: center;
				top: 50%;
				left: 50%;
				transition: all 0.3s;
				transform: translate(-50%, -50%);
				.iconfont {
					color: $textColor6;
					font-size: 14px;
					display: block;
				}
				span {
					@include font14;
					margin-left: 8px;
					color: $textColor6;
				}
				&:hover {
					background: rgba(25, 25, 26, 1);
				}
			}
		}
		.fs-labs-view-list {
			flex: 1;
			display: grid;
			background-color: $bgColor6;
			grid-template-columns: repeat(2, 1fr);
			padding: 40px;
			border-radius: 0 8px 8px 0;
			overflow: hidden;
			.fs-labs-view-item {
				padding: 16px;
				border-right: 1px solid $borderColor1;
				border-bottom: 1px solid $borderColor1;
				&:nth-child(2n) {
					border-right: none;
				}
				&:nth-child(n + 3) {
					border-bottom: none;
				}
				> img {
					width: 48px;
					height: 48px;
					margin: 0 auto 12px;
				}
				> strong {
					display: block;
					@include font14;
					color: $textColor1;
					text-align: center;
				}
			}
		}
	}
	@include pad {
		.fs-labs-view {
			.fs-labs-video {
				max-height: 276px;
			}
			.fs-labs-view-list {
				padding: 0 40px;
			}
		}
	}
	@include mobile {
		.fs-labs-view {
			display: block;
			.fs-labs-video {
				width: 100%;
				display: flex;
				> img {
					height: auto;
				}
			}
			.fs-labs-view-list {
				padding: 20px 0;
			}
		}
	}
}
