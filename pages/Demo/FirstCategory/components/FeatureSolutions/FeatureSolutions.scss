.fs-feature-solutions {
	@include contentWidth;
	.fs-feature-solutions-list {
		margin-top: 24px;
		.fs-feature-solutions-item {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 20px;
			> a {
				height: 100%;
				border-radius: 8px;
				overflow: hidden;
				background-color: $bgColor6;
				transition: all 0.3s ease-in-out;
				&:hover {
					text-decoration: none;
					box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
					> figure {
						> figcaption {
							.title {
								text-decoration: underline;
							}
						}
					}
				}
				> figure {
					height: 100%;
					display: flex;
					flex-direction: column;
					> div {
						position: relative;
						padding-top: 56.2%;
						> img {
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
							height: 100%;
						}
					}
					> figcaption {
						flex: 1;
						padding: 20px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.text {
							.title {
								@include font14;
								color: $textColor1;
								font-weight: 600;
							}
							.describe {
								@include font12;
								color: $textColor3;
								margin-top: 4px;
							}
						}
					}
				}
			}
		}
	}
	@include mobile {
		padding: 0 0 20px;
		overflow: hidden;
		.fs-feature-solutions-list {
			.fs-feature-solutions-item {
				grid-template-columns: repeat(1, 1fr);
			}
		}
	}
}
