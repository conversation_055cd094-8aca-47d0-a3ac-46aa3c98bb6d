.fs-testing {
	@include contentWidth;
	> h2 {
		@include font24;
		color: $textColor1;
		text-align: center;
	}
	.fs-testing-describe {
		margin-top: 12px;
		@include font14;
		color: $textColor2;
		text-align: center;
	}
	.fs-testing-list {
		margin-top: 24px;
		.fs-testing-item {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 20px;
			> a {
				height: 100%;
				border-radius: 8px;
				overflow: hidden;
				background-color: $bgColor6;
				transition: all 0.3s ease-in-out;
				&:hover {
					text-decoration: none;
					box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
					> figure {
						> figcaption {
							.more {
								:deep(.fs-button--suffix) {
									text-decoration: underline;
								}
							}
						}
					}
				}
				> figure {
					height: 100%;
					display: flex;
					flex-direction: column;
					> div {
						position: relative;
						padding-top: 56.2%;
						> img {
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
						}
					}
					> figcaption {
						flex: 1;
						padding: 20px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						.text {
							.title {
								@include font14;
								color: $textColor1;
								font-weight: 600;
							}
							.describe {
								@include font12;
								color: $textColor2;
								margin-top: 4px;
							}
						}
						.more {
							text-decoration: none;
							> .right {
								font-size: 12px;
								line-height: 1;
								color: $textColor1;
							}
							&:hover {
								.right {
									text-decoration: none;
								}
							}
						}
					}
				}
			}
		}
	}
	@include mobile {
		padding: 0 0 20px;
		overflow: hidden;
		.fs-testing-list {
			.fs-testing-item {
				grid-template-columns: repeat(1, 1fr);
			}
		}
	}
}
