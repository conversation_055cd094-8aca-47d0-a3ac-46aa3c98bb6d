<template>
	<div class="careers_page">
		<div v-if="jobPageType === -1" class="step_01">
			<div class="top">
				<h3 class="tit">{{ localeLang("careers.tit") }}</h3>
				<p class="sub_tit">{{ localeLang("careers.desc").replace("xxxx", contentData[pageCurrent].length) }}</p>
			</div>
			<div class="main">
				<ul>
					<li v-for="(item, index) in contentData[pageCurrent]" :key="index" @click="chooseJob(index, item)">
						<div class="lt">
							<div class="job_title">{{ item.positionName }}</div>
							<p class="job_desc">{{ item.employmentTypeLocation }}</p>
						</div>
						<div class="rt">{{ item.jobType }}</div>
					</li>
				</ul>
				<FsPagination :total="pageTotal" :current="pageCurrent" :pageSize="pageSize" @change="changePage"></FsPagination>
			</div>
		</div>
		<div v-else class="step_02">
			<template v-if="!success">
				<div class="back">
					<a class="back_btn" href="javascript:;" @click="jobPageType = -1">
						<i class="iconfont">&#xe702;</i>
						<span>{{ localeLang("careers.back") }}</span>
					</a>
				</div>
				<div class="top">
					<h3 class="tit">{{ jobInfo.positionName }}</h3>
					<p class="sub_tit">{{ jobInfo.employmentTypeLocation }}</p>
				</div>
				<img :src="bannerImg" alt="" class="banner" />
				<div class="main">
					<div class="lt">
						<div v-if="jobInfo.jobDescription.title" class="item">
							<h2>{{ jobInfo.jobDescription.title }}</h2>
						</div>
						<div v-for="(item, index) in jobInfo.jobDescription.details" :key="index" class="item">
							<h3 v-if="item.title" v-html="item.title"></h3>
							<h4 v-if="item.subtitle" v-html="item.subtitle"></h4>

							<ul v-if="item.description && item.description.length">
								<li v-for="(tt, ii) in item.description" :key="index + ii" v-html="tt"></li>
							</ul>
							<ul v-if="item.details && item.details.length">
								<li v-for="(tt, ii) in item.details" :key="index + ii" v-html="tt"></li>
							</ul>
						</div>
					</div>
					<div class="rt">
						<FsForm ref="formRef" :rules="rules" :model="model" layout="vertical">
							<FsFormItem prop="entry_firstname" :label="`${localeLang('ContactSales.first_name')}`">
								<FsInput v-model="model.entry_firstname" newRegular />
							</FsFormItem>
							<FsFormItem prop="entry_lastname" :label="`${localeLang('ContactSales.last_name')}`">
								<FsInput v-model="model.entry_lastname" newRegular />
							</FsFormItem>

							<FsFormItem prop="email_address" :label="`${emailLabel}`">
								<FsInput v-model="model.email_address" newRegular />
							</FsFormItem>
							<FsFormItem :label="`LinkedIn (${localeLang('formValidate.form.Optional')})`">
								<FsInput v-model="model.linkedIn" newRegular />
							</FsFormItem>
							<FsFormItem prop="comments" class="comments_box" :label="`${localeLang('ContactSales.aboutYourself')}`">
								<FsInput v-model="model.comments" type="textarea" newRegular> </FsInput>
							</FsFormItem>
							<FsFormItem prop="reviews_newImg" class="upload_file">
								<fs-upload v-model="model.reviews_newImg" :text="localeLang('formValidate.form.upload_file')" multiple :limit="5" accept=".pdf,.doc,.docx">
									<template #tip>
										<fs-tooltip placement="right">
											<span class="iconfont iconfs_2024110818icon"></span>
											<template #content>
												<p>{{ localeLang("careers.fileTips") }}</p>
											</template>
										</fs-tooltip>
									</template>
								</fs-upload>
							</FsFormItem>
							<FsFormItem prop="policy">
								<div class="check_box">
									<fs-checkbox v-model="model.policy" size="small"> </fs-checkbox>
									<span class="check_box_text" v-html="policyText"></span>
								</div>
							</FsFormItem>
							<!-- <FsFormItem>
								<div
									class="agreement_wrap"
									v-html="
										localeLang('ContactSales.submitTip').replace('XXXX1', localeLink('/policies/privacy_policy.html')).replace('XXXX2', localeLink('/policies/terms_of_use.html'))
									"></div>
							</FsFormItem> -->
						</FsForm>
						<fs-button type="red" class="submit_btn" :loading="loading" @click.prevent="handleSubmit()">{{ localeLang("contactSales.Submit") }}</fs-button>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="success">
					<div class="text">
						<div class="title">
							<h3><i class="iconfont">&#xe67a;</i>{{ localeLang("careers.successTit") }}</h3>
						</div>
						<p>
							{{ localeLang("careers.successDesc") }}
						</p>
					</div>
					<fs-button type="red" @click="jobPageType = -1">{{ localeLang("contactSales.Submit") }}</fs-button>

					<!-- <a :href="localeLink('/')" class="back_to_home">{{ localeLang("ContactSales.success.btn_txt") }}</a> -->
				</div>
			</template>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsButton, FsPagination, FsForm, FsFormItem, FsCheckbox, FsInput, FsUpload, FsTooltip } from "fs-design";
import type { FormInstance } from "fs-design";
import { email_valdate, cn_all_phone } from "~/constants/validate";
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();
const bannerImg = computed(() => {
	if (deviceStore.screenWidth < 768) {
		return "https://resource.fs.com/mall/generalImg/20250519180348w102z7.png";
	} else if (deviceStore.screenWidth > 1024) {
		return "https://resource.fs.com/mall/generalImg/20250519180348kqejfh.png";
	} else {
		return "https://resource.fs.com/mall/generalImg/202505191803485o2dxp.png";
	}
});
const websiteStore = useWebsiteStore();
const { website, countries_id, isCn, isSg } = useWebsiteStore();

definePageMeta({
	layout: "common"
});

const jobPageType = ref(-1);
const jobInfo = ref();
const chooseJob = (index: number, data: any) => {
	console.log("chooseJob", index);
	jobPageType.value = index;
	jobInfo.value = data;
	console.log("jobInfo.value", jobInfo.value);
};
const contentData = ref();
const pageTotal = ref(0);
const pageSize = 12;
const pageCurrent = ref(1);
const getData = async () => {
	const { data } = await useRequest.get(`/cms/api/fs/hirePosition/overseasJobList`);

	if (data.value?.code === 200 && data.value.data.length) {
		// contentData.value = data.value.data;
		pageTotal.value = data.value.data.length;
		const result = [""];
		for (let i = 0; i < data.value.data.length; i += pageSize) {
			result.push(data.value.data.slice(i, i + pageSize));
		}
		contentData.value = result;
	}
	console.log("contentData.value", contentData.value, countries_id);
};

const changePage = (current: number) => {
	pageCurrent.value = current;
	console.log("changePage", current);
};

await getData();
const model = ref({
	policy: false,
	entry_firstname: "",
	entry_lastname: "",
	email_address: "",
	linkedIn: "",
	comments: "",
	reviews_newImg: []
});
const defaultRule = {
	required: true,
	trigger: "blur",
	transform(value: string) {
		return value.trim();
	},
	message: localeLang("ContactSales.fieldRequired")
};
const rules: { [x: string]: any } = ref({
	entry_firstname: [{ ...defaultRule }],
	entry_lastname: [{ ...defaultRule }],
	comments: [
		{
			...defaultRule,
			message: localeLang("ContactSales.fieldRequired")
		}
	],

	email_address: [
		{
			validator: () => {
				const val = model.value.email_address;
				let msg = "";
				if (!val) msg = localeLang("ContactSales.errors.email_address_error");
				else {
					if (!email_valdate.test(val)) msg = localeLang("ContactSales.errors.email_address_error01");
				}
				return msg;
			},
			trigger: "blur",
			required: true,
			isFunctionValidator: true
		}
	],
	policy: [
		{
			require: true,
			message: localeLang("formValidate.form.errors.check2_error"),
			trigger: "change",
			validator: (_: any, val: boolean) => val == true
		}
	]
});

const emailLabel = computed(() => {
	return `${localeLang("ContactSales.email_business")}`;
});
const loading = ref(false);
const formRef = ref<FormInstance>();
const success = ref(false);
const handleSubmit = async () => {
	const countriesId = countries_id;
	try {
		!success.value && (await formRef?.value?.validate());
		loading.value = true;
		const params = new FormData();
		params.append("countriesId", String(countriesId));
		params.append("email", model.value.email_address);
		params.append("firstName", model.value.entry_firstname);
		params.append("jobCategory", jobInfo.value.jobType);
		params.append("lastName", model.value.entry_lastname);
		params.append("linkedIn", model.value.linkedIn);
		params.append("positionName", jobInfo.value.positionName);
		params.append("selfDescription", model.value.comments);

		if (model.value.reviews_newImg.length) {
			for (let i = 0; i < model.value.reviews_newImg.length; i++) {
				params.append("files", model.value.reviews_newImg[i]);
			}
		}
		const { data } = await useRequest.post("/cms/api/fs/hirePosition/apply", { data: params });
		loading.value = true;
		if (data.value?.code === 200) {
			success.value = true;
		}
		console.log("data", data);
	} catch (error) {
		console.log(error);
	}
	// const data = await useRequest.post("/cms/api/fs/hirePosition/apply", {
	// 	data: params
	// });
	// const { data } = await useRequest.get(`/cms/api/fs/hirePosition/overseasJobList`);

	// const { data } = await useRequest.post("/cms/api/fs/hirePosition/apply", { data: params });
	// loading.value = false;
	// if (data?.value?.code === 200 && data?.value?.data?.case_number) {
	// 	isSuccessStatus.value = true;
	// }
	// if (resData.value && resData.value.code === 200) {
	// 	console.log(resData.value, "resData");
	// }
	//
};
const policyText = computed(() => {
	if (isSg) {
		return localeLang("formValidate.form.aggree_policy").replace("AAAA", localeLink("/policies/privacy_policy.html")).replace("BBBB", localeLink("/policies/terms_of_use.html"));
	} else {
		return localeLang("formValidate.form.aggree_policy").replace("XXXX1", localeLink("/policies/privacy_policy.html")).replace("XXXX2", localeLink("/policies/terms_of_use.html"));
	}
});
</script>

<style scoped lang="scss">
.careers_page {
	color: $textColor1;
	.step_01 {
		@include contentWidth;
		padding: 36px 0;
		.top {
			text-align: center;
			.tit {
				@include font20;
				font-weight: 600;
			}
			.sub_tit {
				margin-top: 12px;
				@include font16;
				font-weight: 600;
			}
		}
		.main {
			margin-top: 24px;
			ul {
				display: flex;
				flex-direction: column;
				gap: 20px;
				li {
					display: flex;
					justify-content: space-between;
					align-items: center;

					border-radius: 4px;
					background: #fafafb;
					transition: all 0.3s;
					padding: 24px;
					cursor: pointer;
					.lt {
						.job_title {
							@include font14;
							font-weight: 600;
						}
						.job_desc {
							margin-top: 8px;
							@include font13;
							color: $textColor2;
						}
					}
					.rt {
						@include font13;
						font-weight: 600;
						color: $textColor1;
					}
					/* 传统小投影 */
					&:hover {
						background: #ffffff;
						box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
					}
				}
			}
			.fs-pagination {
				margin-top: 24px;
			}
		}
	}
	.step_02 {
		@include contentWidth;
		padding: 36px 0;
		.back {
			text-align: center;
			padding-bottom: 16px;
			.back_btn {
				display: flex;
				align-items: center;
				justify-content: center;
				@include font14;
				color: $textColor5;
				text-decoration: none;
				gap: 4px;
				&:hover {
					span {
						text-decoration: underline;
					}
				}
				.iconfont {
					font-size: 12px;
				}
			}
		}
		.top {
			text-align: center;

			.tit {
				@include font20;
				font-weight: 600;
			}
			.sub_tit {
				margin-top: 12px;
				@include font16;
				font-weight: 600;
			}
		}
		.banner {
			margin-top: 24px;
			width: 100%;
			// height: 400px;
			display: block;
			border-radius: 8px;
		}
		.main {
			margin-top: 24px;
			display: flex;
			justify-content: space-between;
			gap: 40px;
			.lt {
				display: flex;
				flex-direction: column;
				gap: 24px;
				flex: 1;
				.item {
					display: flex;
					flex-direction: column;
					gap: 12px;
					> h2 {
						@include font20;
					}
					> h3 {
						@include font16;
						font-weight: 600;
					}
					> h4 {
						@include font14;
						font-weight: 400;
						color: $textColor2;
					}
					> div {
						display: flex;
						flex-direction: column;
						gap: 8px;
						p {
							@include font14;
							color: $textColor2;
							font-weight: 400;
						}
					}
					ul {
						padding-left: 12px;
						display: flex;
						flex-direction: column;
						gap: 8px;
						li {
							@include font14;
							color: $textColor2;
							position: relative;
							&::before {
								content: "";
								display: block;
								position: absolute;
								left: -12px;
								top: 9px;
								width: 4px;
								height: 4px;
								border-radius: 50%;
								background: $textColor2;
							}
						}
					}
				}
			}
			.rt {
				width: 520px;
				.check_box {
					display: flex;
					align-items: center;
					height: 20px;
					.check_box_text {
						@include font12;
						color: #707070;
					}
					.fs-checkbox {
						margin-right: 0;
					}
				}
				:deep(textarea) {
					resize: block !important;
				}
				.fs-button {
					width: 100%;
				}
				.iconfs_2024110818icon {
					margin-left: 8px;
					color: $textColor2;
					cursor: pointer;
				}
				.agreement_wrap {
					@include font12;
					padding-bottom: 4px;
					color: $textColor2;
				}
				.upload_file {
					:deep(.fs-button) {
						border: none;
						color: $textColor1;
						gap: 8px;
						font-size: 12px;
						line-height: 20px;
						.fs-button--prefix {
							margin-left: 0;
						}
					}
					:deep(.fs-tooltip__mobile--content .iconfont) {
						color: #707070;
						font-size: 20px;
						line-height: 1;
					}
				}
			}
		}
		.success {
			padding: 44px 0 124px;
			display: flex;
			flex-direction: column;
			align-items: center;
			@include mobile {
				padding-top: 4px;
				padding-bottom: 4px;
			}
			.text {
				text-align: center;
				margin-bottom: 32px;
				max-width: 650px;
				.title {
					display: flex;
					// align-items: center;
					justify-content: center;
					gap: 12px;
					@include mobile {
						padding: 0 8px;
						h3 {
							text-align: center;
						}
					}
					.iconfont {
						padding-top: 4px;
						padding-right: 8px;
						font-size: 20px;
						color: #10a300;
						font-weight: 400;
					}
					h3 {
						@include font24;
						font-weight: 600;
						color: $textColor1;
						flex: 1;
					}
				}

				p {
					margin-top: 24px;
					@include font14;
					color: $textColor2;
					@include mobile {
						padding: 0 8px;
					}
				}
			}
			.back_to_home {
				@include font14;
				color: $textColor5;
				text-decoration: none;
			}
		}
	}
}
@include pad {
}
@include mobile {
	.careers_page {
		.step_01 .main ul li {
			flex-direction: column;
			align-items: flex-start;
			gap: 12px;
		}
		.step_02 {
			.top .sub_tit {
				margin-top: 12px;
			}
			.main {
				flex-direction: column;
				gap: 40px;
				.rt {
					width: 100%;
				}
			}
			.success .text p {
				margin-top: 12px;
			}
		}
	}
}
</style>
