.fs-filter {
	.filter_handle_content {
		background-color: $bgColor2;
		padding: 16px 0;
		.common {
			@include contentWidth;
			height: 40px;
			display: flex;
			justify-content: space-between;
			.label_filter_box {
				display: flex;
				align-items: center;
				.filter_item_name {
					font-weight: 600;
					@include font12;
					margin-right: 8px;
				}
				.filter_item {
					margin-right: 8px;
				}
			}
			.sort_filter_box {
				display: flex;
				align-items: center;
				.list_sum {
					font-weight: 600;
					@include font12;
					margin-right: 12px;
				}
			}
		}
	}
	.filter_handle_content_m {
		display: none;
	}
	.filter_show_content {
		.common {
			@include contentWidth;
			> ul {
				display: flex;
				flex-flow: wrap;
				padding: 12px 0 0;
				li {
					border: 1px solid $bgColor5;
					border-radius: 30px;
					padding: 0 12px;
					@include font12;
					display: flex;
					align-items: center;
					color: $textColor1;
					min-height: 30px;
					margin-right: 8px;
					margin-top: 8px;
					cursor: pointer;
					&:hover {
						background-color: #f2f2f2;
					}
					span {
						margin: 0 8px;
					}
					b {
						font-weight: 600;
						flex-shrink: 0;
					}
					.iconfont {
						color: $textColor2;
						font-size: 12px;
						line-height: 1;
						cursor: pointer;
					}
					&.clear_all {
						border: none;
						margin-right: 0;
						padding: 0;
						cursor: pointer;
						&:hover {
							background-color: #fff;
						}
						span {
							font-style: italic;
							color: $textColor2;
							margin: 0 4px 0 0;
							&:hover {
								text-decoration: underline;
							}
						}
						i {
							display: none;
						}
					}
				}
			}
		}
	}
}
@include pad {
}
@include mobile {
	.fs-filter {
		.filter_handle_content {
			display: none;
		}
		.filter_handle_content_m {
			display: block;
			background-color: $bgColor5;
			.common {
				@include contentWidth;
				.filter_handle_content_m_top {
					display: flex;
					justify-content: space-between;
					padding: 8px 0;
					.lt {
						display: flex;
						// height: 40px;
						align-items: center;
						p {
							margin-right: 40px;
							display: flex;
							align-items: center;
							padding: 10px 0;
							color: $textColor2;
							&.active {
								color: $textColor1;
								span {
									font-weight: 600;
								}
							}
							&:last-child {
								margin-right: 0;
							}
							span {
								margin-right: 8px;
								font-weight: 600;
								@include font12;
							}
							.iconfont {
								font-size: 20px;
								line-height: 1;
							}
						}
					}
					.rt {
						@include font12;
						font-weight: 600;
						display: flex;
						align-items: center;
					}
				}
				.filter_handle_content_m_contain {
					.filter_box,
					.sort_box {
						padding-bottom: 20px;
						.filter_item {
							margin-bottom: 8px;

							&:last-of-type {
								margin-bottom: 0;
							}
						}
						:deep(.fs-tooltip__popper) {
							width: calc(100% - 32px);
							max-width: unset !important;
						}
					}
				}
			}
		}
	}
}
