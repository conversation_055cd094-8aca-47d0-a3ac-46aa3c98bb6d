<template>
	<div class="fs-filter">
		<div v-if="!isMobile" class="filter_handle_content">
			<div class="common">
				<div class="label_filter_box">
					<div class="filter_item_name">{{ localeLang("caseStudy.FilterBy") }}</div>
					<div v-for="(item, index) in filterData" :key="index" class="filter_item">
						<FilterSelect :data="item" :filterIndex="index" @changeOption="changeOption" />
					</div>
				</div>
				<div class="sort_filter_box">
					<div class="list_sum">
						{{ `${props.listSum} ${props.listSum > 1 ? localeLang("caseStudy.results") : localeLang("caseStudy.result")}` }}
					</div>
					<SortSelect />
				</div>
			</div>
		</div>
		<div v-else class="filter_handle_content_m">
			<div class="common">
				<div class="filter_handle_content_m_top">
					<div class="lt">
						<p v-for="(item, index) in mobileType" :key="index" :class="{ active: index === acticeIndex }" @click="mobileTypeChange(index)">
							<span>{{ item.name }}</span>
							<i class="iconfont" v-html="item.icon"></i>
						</p>
					</div>
					<div class="rt">
						<div class="list_sum">
							{{ `${props.listSum} ${props.listSum > 1 ? localeLang("caseStudy.results") : localeLang("caseStudy.result")}` }}
						</div>
					</div>
				</div>
				<div v-show="acticeIndex != -1" class="filter_handle_content_m_contain">
					<div v-show="acticeIndex === 0" class="filter_box">
						<div v-for="(item, index) in filterData" :key="index" class="filter_item">
							<FilterSelect :data="item" :filterIndex="index" @changeOption="changeOption" />
						</div>
					</div>
					<div v-show="acticeIndex === 1" class="sort_box">
						<SortSelect />
					</div>
				</div>
			</div>
		</div>
		<div v-if="efficientCheck?.length" class="filter_show_content">
			<div class="common">
				<ul>
					<template v-for="(item, index) in checkedArrId" :key="index">
						<li v-if="item" class="check_item" @click="deleteFn(item)">
							<b>{{ `${item?.p_title}:` }}</b>
							<span>{{ item.item.name }}</span>
							<i class="iconfont">&#xf30a;</i>
							<!-- <fs-tag :closable="true" color="#19191a" hoverColor="#f2f2f2" borderColor="#eee" InteractiveTag>
								<div>
									<span :style="{ 'font-weight': '600', marginRight: '8px' }">{{ `${item?.p_title}:` }}</span>
									<span>{{ item.item.tag_title }}</span>
								</div>
							</fs-tag> -->
						</li>
					</template>
					<li v-if="efficientCheck.length > 0" class="check_item clear_all" @click="clearAll">
						<span>{{ localeLang("caseStudy.Clear_All") }}</span>
						<i class="iconfont">&#xf30a;</i>
					</li>
				</ul>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsTag } from "fs-design";

import type { ICheckedArrIdItem } from "../../types";
import FilterSelect from "./FilterSelect.vue";
import SortSelect from "./SortSelect.vue";

const localeLang = useLocaleLang();
const route = useRoute();
const currentIndex = reactive({
	index: 0
});
const props = defineProps({
	filterData: {
		type: Array,
		default() {
			return [];
		}
	},
	listSum: {
		type: Number,
		default() {
			return 0;
		}
	}
});
const filterData = props.filterData as any;

filterData.forEach((element: any) => {
	element.check = "";
});
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);

const checkedArrId = ref<any[]>([]);

const changeOption = function (obj: ICheckedArrIdItem) {
	checkedArrId.value[obj.location] = obj;
};

const deleteFn = function (obj: ICheckedArrIdItem) {
	checkedArrId.value[obj.location] = null;
	filterData[obj.location].check = "";
};

const clearAll = function () {
	checkedArrId.value = [];
	filterData.forEach((item: ICheckedArrIdItem) => {
		item.check = "";
	});
	navigateTo({ path: route.path });
};

const acticeIndex = ref(-1);
const mobileType = [
	{
		type: "filter",
		icon: "&#xe714;",
		name: localeLang("caseStudy.Filter")
	},
	{
		type: "sort",
		icon: "&#xe717;",
		name: localeLang("caseStudy.Sort")
	}
];
const mobileTypeChange = function (index: number) {
	if (acticeIndex.value === index) {
		acticeIndex.value = -1;
		currentIndex.index = 99;
	} else {
		acticeIndex.value = index;
	}
};

const efficientCheck = ref();
watch(
	() => checkedArrId.value,
	async () => {
		efficientCheck.value = checkedArrId.value.filter(value => value !== null);
		const tagids = checkedArrId.value
			.filter(item => item !== null && typeof item.item === "object") // 过滤掉null和非对象
			.map(item => {
				if (item.item.childIds) {
					return item.item.childIds;
				} else {
					return item.item.id;
				}
			});
		const routeParams = { ...route.query };
		if (!tagids.length) {
			delete routeParams.tagids;
		} else {
			routeParams.tagids = tagids;
		}
		delete routeParams.current;
		await navigateTo({ path: route.path, query: { ...routeParams } });
	},
	{ deep: true }
);

onMounted(() => {
	console.log("filterData1111", filterData);
});
provide("currentIndex", currentIndex);
</script>

<style scoped lang="scss">
@import url("./CaseFilter.scss");
</style>
