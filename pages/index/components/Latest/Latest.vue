<template>
	<div class="latest">
		<div class="common_wrap">
			<p class="title">{{ contentData.title }}</p>
			<div class="list">
				<div class="list_cont">
					<div class="swiper_box">
						<swiper
							:modules="modules"
							:breakpoints="breakpoints"
							:pagination="pagination"
							:navigation="{
								nextEl: '.latest .swiper-button-next',
								prevEl: '.latest .swiper-button-prev'
							}"
							:class="{ has_pagination: show_pagination }">
							<swiper-slide v-for="(latest, index) in list" :key="index">
								<div v-for="(latestItem, i) in latest" :key="i" class="latestItem">
									<a :href="localeLink(latestItem.url)">
										<img :src="latestItem.img" :alt="latestItem.tit" />
										<p class="tit" :title="latestItem.tit">{{ latestItem.tit }}</p>
										<p class="desc" :title="latestItem.desc">{{ latestItem.desc }}</p>
									</a>
								</div>
							</swiper-slide>
						</swiper>
						<div class="swiper-button-prev swiper-btn" :class="{ hide_swiper_btn: !show_pagination }">
							<i class="iconfont iconfs_2024110802icon"></i>
						</div>
						<div class="swiper-button-next swiper-btn" :class="{ hide_swiper_btn: !show_pagination }">
							<i class="iconfont iconfs_2024110803icon"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination, Autoplay, Navigation, EffectFade } from "fs-design-swiper/modules";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";
import type { LatestData } from "../../types";
const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();

const props = defineProps<LatestData>();
const contentData = props?.contentData;

// Swiper配置
const modules = [Pagination, Autoplay, Navigation, EffectFade];
const pagination = { clickable: true };
const breakpoints = {
	320: {
		slidesPerView: 1,
		spaceBetween: 20,
		slidesPerGroup: 1
	},
	769: {
		slidesPerView: 4,
		spaceBetween: 20,
		slidesPerGroup: 4
	}
};

// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(contentData.list, 4) : ReorganizeArrays(contentData.list, 1)));
// 是否显示分页指示器
const show_pagination = computed(() => (deviceStore.screenWidth <= 768 ? list.value.length >= 2 : list.value.length >= 5));
</script>

<style lang="scss" scoped>
@import url("./Latest.scss");
</style>
