<template>
	<div class="certified_wrap">
		<div class="common-wrap">
			<h2 class="title">{{ certified.title }}</h2>
			<div class="certified_list">
				<swiper :modules="modules" :spaceBetween="spaceBetween" :pagination="pagination">
					<swiper-slide v-for="(item, index) in list" :key="index">
						<div class="certified-item">
							<a v-for="(t, i) in item" :key="i" :href="localeLink(t.url)" class="certified_card" @click="gaPoint(t.title)">
								<div class="img_box">
									<img v-lazy="t.icon" :alt="t.title" />
								</div>
								<div class="cont">
									<FsButton type="black" text arrowButton bold>{{ t.title }}</FsButton>
									<div v-if="t.desc" class="des">{{ t.desc }}</div>
								</div>
							</a>
						</div>
					</swiper-slide>
				</swiper>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { FsButton } from "fs-design";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";

import type { IHomeCertified } from "../../types";

const props = defineProps<IHomeCertified>();

const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();

const certified = props.contentData;

// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const spaceBetween = 16;

// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(certified.list, 3) : ReorganizeArrays(certified.list, 0)));

const gaPoint = function (c: string) {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Home Page",
			eventAction: "certified_module",
			eventLabel: c,
			nonInteraction: false
		});
	}
};

onMounted(() => {
	// console.log(props.contentData, "certifiedData", list);
});
</script>

<style scoped lang="scss">
.certified_wrap {
	background-color: #fff;
	color: $textColor1;
	.common-wrap {
		@include newPcHeaderWidth;

		padding-bottom: 48px;
	}
	.title {
		@include font24;
		font-weight: 600;
		text-align: center;
		margin-bottom: 24px;
	}
	.swiper {
		overflow: unset;
	}
	.certified_card {
		display: flex;
		flex-direction: column;
		color: $textColor1;
		text-decoration: none;
		background-color: $bgColor6;
		transition: all 0.3s;
		cursor: pointer;
		border-radius: 8px;
		overflow: hidden;
		background: #fafbfb;
		.img_box {
			position: relative;
			width: 100%;
			padding: 32px 0 0;
			display: flex;
			justify-content: center;
			img {
				width: 64px;
				display: block;
				max-width: 100%;
				height: auto;
			}
			.play {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				.fs-large-play-button-bg {
					fill: #212121;
					opacity: 0.8;
				}
			}
		}
		.cont {
			flex: 1;
			// background-color: $bgColor6;
			padding: 8px 32px 32px 32px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.tit {
				@include font14;
				font-weight: 600;
				margin-bottom: 4px;
				display: flex;
				justify-content: center;
				align-items: center;
				span {
					@include textOverflow;
				}
				.iconfont {
					margin-left: 4px;
					font-size: 12px;
					color: $textColor1;
				}
			}
			.des {
				@include font12;
				color: $textColor2;
				text-align: center;
			}
			.learnmore {
				color: $textColor1;
				margin-top: 16px;
				display: flex;
				align-items: flex-end;
				span {
					@include font13;
				}
				.iconfont {
					@include font12;
					margin-left: 4px;
				}
			}
		}
		&:hover {
			box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
			.cont {
				.tit span {
					text-decoration: underline;
				}
			}
		}
	}
	.certified-item {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-column-gap: 20px;
		grid-row-gap: 20px;
	}
	.swiper-container {
		display: none;
	}
}
@include pad {
	.certified_wrap {
		.common-wrap {
			width: 100%;
			padding: 0 24px 48px;
		}
	}
}
@media (max-width: 819px) {
	.certified_wrap {
		.common-wrap {
			width: 100%;
			padding: 0 16px 48px;
		}
	}
}
@include mobile {
	.certified_wrap {
		.common-wrap {
			padding: 0 16px 36px;
			width: 100%;
		}
		.title {
			@include font20;
		}
		:deep(.swiper-wrapper) {
			align-items: stretch;
		}
		.swiper-slide {
			height: auto;

			.certified-item {
				height: 100%;
				display: grid;
				grid-template-columns: repeat(1, 1fr);
				grid-column-gap: 20px;
				grid-row-gap: 20px;
			}
			.certified_card {
				padding: 32px;
				flex-direction: row;
				.img_box {
					width: auto;
					padding: 0 16px 0 0;
					img {
						width: 36px;
						height: 36px;
					}
				}
				.cont {
					padding: 0;
					.tit {
						// margin-bottom: 8px;
						justify-content: flex-start;
					}
					.des {
						text-align: left;
					}
				}
				&:hover {
					box-shadow: none;
					.cont {
						.tit {
							text-decoration: none;
						}
					}
				}
			}
		}

		.swiper {
			display: block;
			padding-bottom: 28px;
			overflow: hidden;
			&:deep(.swiper-pagination) {
				display: flex;
				justify-content: center;
				bottom: 0;
				.swiper-pagination-bullet {
					width: 8px;
					height: 8px;
					background: #707070;
					opacity: 0.4;
					transition: all 0.3s;
				}
				.swiper-pagination-bullet-active {
					width: 20px;
					opacity: 1;
					border-radius: 4px;
				}
			}
		}
	}
}
</style>
