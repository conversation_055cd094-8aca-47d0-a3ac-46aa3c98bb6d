.recommended {
	margin: 0 auto;
	.common_wrap {
		@include newP<PERSON><PERSON><PERSON>erWidth;
		margin-top: 48px;
		.title {
			@include font24;
			font-weight: 600;
			text-align: center;
			margin-bottom: 24px;
		}
		.list {
			max-width: 1360px;
			margin: 0 auto;
			&:hover {
				.list_cont {
					.swiper_box {
						.swiper-btn {
							opacity: 1;
						}
					}
				}
			}
			.list_cont {
				position: relative;
				.swiper_box {
					.swiper {
						padding-bottom: 48px;
						&.has_pagination {
							padding-bottom: 76px;
						}
						.swiper-slide {
							display: flex;
							height: auto;
							// 预先设置卡片的尺寸
							width: calc((100% - 60px) / 4);
							margin-right: 20px;
							@media (max-width: 768px) {
								width: 100%;
							}
						}
						:deep(.swiper-pagination) {
							display: flex;
							justify-content: center;
							bottom: 48px;
							.swiper-pagination-bullet {
								background: #707070;
								opacity: 0.4;
								transition: all 0.3s;
								border-radius: 100%;
								&.swiper-pagination-bullet-active {
									width: 20px;
									opacity: 1;
									background: #707070;
									border-radius: 4px;
								}
							}
						}
					}

					.swiper-btn {
						position: absolute;
						font-size: 32px;
						color: #fff;
						width: 48px;
						height: 48px;
						border-radius: 50%;
						transition: all 0.3s;
						box-sizing: border-box;
						background-color: rgba(0, 0, 0, 0.2);
						text-align: center;
						line-height: 48px;
						background-image: none;
						user-select: none;
						opacity: 0;
						margin-top: -60px;
						top: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						&.swiper-button-disabled {
							cursor: default;
						}
						&:hover {
							background-color: rgba(0, 0, 0, 0.4);
						}
						&::after {
							content: none;
						}
						@media (max-width: 1024px) {
							display: none;
						}
						.iconfont {
							color: $textColor6;
							font-size: 28px;
						}
					}
					.swiper-button-prev {
						left: -80px;
					}
					.swiper-button-next {
						right: -80px;
					}
					.swiper-button-disabled {
						background-color: rgba(0, 0, 0, 0.05) !important;
					}

					.hide_swiper_btn {
						display: none;
					}

					.recommendedItem {
						border-radius: 8px;
						transition: all 0.3s;
						background: #fafbfb;
						&:hover {
							box-shadow: 0px 15px 15px -10px rgba(0, 0, 0, 0.15);
							a {
								.tit {
									text-decoration: underline;
								}
							}
						}
						a {
							display: flex;
							flex-direction: column;
							padding: 20px;
							text-decoration: none;
							img {
								width: 100%;
								max-width: 180px;
								height: auto;
								margin: auto;
								mix-blend-mode: multiply;
							}
							.tit {
								@include font14;
								color: $textColor1;
								font-weight: 600;
								margin-top: 16px;
								@include textClampOverflow(1);
							}
							.desc {
								margin-top: 4px;
								@include font12;
								color: $textColor2;
							}
						}
					}
				}
			}
		}
	}
	@include pad {
		.common_wrap {
			.list {
				.list_cont {
					.swiper_box {
						.recommendedItem {
							a {
								.desc {
									@include textClampOverflow(2);
								}
							}
						}
					}
				}
			}
		}
	}

	@include mobile {
		.common_wrap {
			margin-top: 36px;
			.list {
				.list_cont {
					.swiper_box {
						.swiper {
							padding-bottom: 36px;
							&.has_pagination {
								padding-bottom: 64px;
							}
							.swiper-slide {
								display: grid;
								grid-template-columns: repeat(2, 1fr);
								gap: 20px;
							}
							:deep(.swiper-pagination) {
								bottom: 36px;
							}
						}
						.recommendedItem {
							a {
								img {
									max-width: 140px;
								}
								.desc {
									@include textClampOverflow(3);
								}
							}
						}
					}
				}
			}
		}
	}
}
