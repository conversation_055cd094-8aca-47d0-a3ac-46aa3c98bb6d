.solution_wrap {
	background-color: $bgColor6;
	color: $textColor1;
	.common-wrap {
		@include new<PERSON><PERSON><PERSON><PERSON>erWidth;
		padding-bottom: 32px;
		&.cn_common_wrap {
			padding-top: 40px;
			padding-bottom: 32px;
			@include new<PERSON><PERSON><PERSON><PERSON>erWidth;
		}
	}
	.title {
		@include font24;
		font-weight: 600;
		text-align: center;
		margin-bottom: 24px;
	}
	.solution_card {
		display: flex;
		flex-direction: column;
		color: $textColor1;
		text-decoration: none;
		transition: all 0.3s;
		cursor: pointer;
		.img_box {
			position: relative;
			width: 100%;
			img {
				border-radius: 8px 8px 0 0;
				display: block;
				width: 100%;
				height: auto;
			}
			.play {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				.fs-large-play-button-bg {
					fill: #212121;
					opacity: 0.8;
				}
			}
		}
		.cont {
			flex: 1;
			background-color: $bgColor6;
			padding: 20px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			border: 1px solid $borderColor1;
			border-top: none;
			border-radius: 0 0 8px 8px;
			.tit {
				@include font14;
				font-weight: 600;
				margin-bottom: 4px;
				@include txt-more-hid(1);
				&.no-des {
					margin-bottom: 0;
				}
			}
			.des {
				@include font12;
				color: $textColor2;
				@include txt-more-hid(3);
			}
			.learnmore {
				color: $textColor1;
				margin-top: 16px;
				display: flex;
				align-items: flex-end;
				span {
					@include font13;
				}
				.iconfont {
					@include font12;
					margin-left: 4px;
				}
			}
		}
		&:hover {
			box-shadow: 0px 15px 15px -10px rgba(0, 0, 0, 0.1);
			.cont {
				.tit {
					text-decoration: underline;
				}
			}
		}
	}
	.solution-item {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-column-gap: 20px;
		// grid-row-gap: 16px;
		padding-bottom: 16px;
		&.cn_bottompadd {
			padding-bottom: 0;
		}
	}
	.swiper-container {
		display: none;
	}
}
@include pad {
	.solution_wrap {
		.common-wrap {
			width: 100%;
			padding: 0 24px 48px;
			&.cn_common_wrap {
				padding: 48px 24px;
			}
		}
		.solution-item {
			padding-bottom: 0;
		}
	}
}
@media (max-width: 820px) {
	.solution_wrap {
		.common-wrap {
			width: 100%;
			padding: 0 16px 48px;
			&.cn_common_wrap {
				padding: 48px 16px;
			}
		}
	}
}
@include mobile {
	.solution_wrap {
		.common-wrap {
			padding: 0 0 36px;
			width: calc(100% - 32px);
			&.cn_common_wrap {
				padding: 36px 16px;
			}
		}
		.title {
			@include font20;
		}
		:deep(.swiper-wrapper) {
			align-items: stretch;
		}
		.swiper-slide {
			height: auto;

			.solution-item {
				display: block;
				height: 100%;
				padding-bottom: 0;
			}
			.solution_card {
				height: 100%;
				.cont {
					.tit {
						margin-bottom: 4px;
					}
				}
				&:hover {
					box-shadow: none;
					.cont {
						.tit {
							text-decoration: none;
						}
					}
				}
			}
		}

		.swiper {
			display: block;
			padding-bottom: 28px;
			&:deep(.swiper-pagination) {
				display: flex;
				justify-content: center;
				bottom: 0;
				.swiper-pagination-bullet {
					width: 8px;
					height: 8px;
					background: #707070;
					opacity: 0.4;
					transition: all 0.3s;
				}
				.swiper-pagination-bullet-active {
					width: 20px;
					opacity: 1;
					border-radius: 4px;
				}
			}
		}
	}
}
