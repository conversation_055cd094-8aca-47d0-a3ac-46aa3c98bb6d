<template>
	<div class="solution_wrap">
		<div class="common-wrap" :class="{ cn_common_wrap: isCn }">
			<h2 class="title">{{ certified.title }}</h2>
			<div class="solution_list">
				<swiper :modules="modules" :slidesPerView="slidesPerView" :spaceBetween="spaceBetween" :pagination="pagination">
					<swiper-slide v-for="(item, index) in list" :key="index">
						<div class="solution-item" :class="{ cn_bottompadd: isCn }">
							<a v-for="(t, i) in item" :key="i" :href="localeLink(t.url)" class="solution_card" @click="gaPoint(t.title)">
								<div class="img_box">
									<img v-lazy="t.img" :alt="t.title" />
								</div>
								<div class="cont">
									<div>
										<div class="tit" :class="{ 'no-des': !t.desc }">{{ t.title }}</div>
										<div v-if="t.desc" class="des" :title="t.desc">{{ t.desc }}</div>
									</div>
								</div>
							</a>
						</div>
					</swiper-slide>
				</swiper>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";

import type { IHomeSolution } from "../../types";

const props = defineProps<IHomeSolution>();

const localeLink = useLocaleLink();
const deviceStore = useDeviceStore();
const websiteStore = useWebsiteStore();
const { website } = storeToRefs(websiteStore);

const isCn = computed(() => {
	return ["cn", "hk", "mo", "tw"].includes(website.value);
});

const certified = props.contentData;

// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const slidesPerView = 1;
const spaceBetween = 16;

// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(certified.solutions, 1) : ReorganizeArrays(certified.solutions, 0)));

const gaPoint = function (c: string) {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Home Page",
			eventAction: "Solutions_module",
			eventLabel: c,
			nonInteraction: false
		});
	}
};

onMounted(() => {
	// console.log(props.contentData, "SolutionsData");
});
</script>

<style scoped lang="scss">
@import url("./Solutions.scss");
</style>
