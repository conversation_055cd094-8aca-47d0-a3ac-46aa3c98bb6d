<template>
	<div class="products">
		<div class="pc-product">
			<div ref="categoriesRef" class="categories">
				<ul v-if="products?.length">
					<li v-for="(c, i) in svgData" :key="i" ref="cate" :class="isActive === i ? 'active' : ''" @mouseenter.stop="svgChange(i)">
						<img :src="c.code" alt="image" width="38" height="38" />

						<p v-if="products[i]">
							<label ref="nameRef">{{ products[i].name }}</label>
							<span ref="lineRef" class="line"></span>
						</p>
					</li>
				</ul>
			</div>
			<div class="list">
				<div class="list_cont">
					<div class="swiper_box">
						<swiper
							v-if="shouldShowProduct"
							:modules="modules"
							:breakpoints="breakpoints"
							:pagination="pagination"
							:navigation="{
								nextEl: '.products .swiper-button-next',
								prevEl: '.products .swiper-button-prev'
							}"
							:class="{ 'swiper-no-swiping': !isTouch }"
							@swiper="setProductsRef">
							<swiper-slide v-for="(product, index) in list" :key="index">
								<div v-for="(t, i) in product" :key="i" class="product">
									<div class="pic">
										<a :href="localeLink(t.url)" @click.stop="GAPoint(isActive, t.url)">
											<img
												:src="t.image"
												:alt="t.name"
												@click.stop="
													localeLink(t.url);
													GAPoint(isActive, t.url);
												"
												@keyup.enter="localeLink(t.url)" />
										</a>
									</div>
									<div class="desc_box">
										<div class="tit" :title="t.name" :class="{ isNotEn: language !== 'English' }" @click.stop="localeLink(t.url)">
											<a :href="localeLink(t.url)" @click.stop="GAPoint(isActive, t.url)">{{ t.name }}</a>
											<i class="iconfont">&#xe726;</i>
										</div>
										<div v-if="t.children && t.children.length" class="category_items">
											<a
												v-for="items in t.children.slice(0, 5)"
												:key="items.id"
												:href="localeLink(items.url)"
												:title="items.name"
												:class="{ isHide: language !== 'English' }"
												@click.stop="GAPoint(isActive, items.url)"
												>{{ items.name }}</a
											>
										</div>
									</div>
								</div>
							</swiper-slide>
						</swiper>
						<div class="swiper-button-prev swiper-btn">
							<i class="iconfont iconfs_2024110802icon"></i>
						</div>
						<div class="swiper-button-next swiper-btn">
							<i class="iconfont iconfs_2024110803icon"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

import { Pagination, Autoplay, Navigation, EffectFade } from "fs-design-swiper/modules";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import type SwiperClass from "fs-design-swiper";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";
import "fs-design-swiper/css/navigation";
import "fs-design-swiper/css/effect-fade";
import type { ProductsData } from "../../types";

const props = defineProps<ProductsData>();
const modules = [Pagination, Autoplay, Navigation, EffectFade];
const pagination = { clickable: true };
const breakpoints = {
	320: {
		slidesPerView: 1,
		spaceBetween: 20,
		slidesPerGroup: 1
	},
	769: {
		slidesPerView: 4,
		spaceBetween: 20,
		slidesPerGroup: 4
	}
};
const { language } = useWebsiteStore();
const localeLink = useLocaleLink();
const svgData = [
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323v5tmih.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323v5tmih.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/202502211753231meigr.svg`,
		hover: `https://resource.fs.com/mall/generalImg/202502211753231meigr.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323a01opa.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323a01opa.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323wyfobd.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323wyfobd.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/2025022117532374zh0d.svg`,
		hover: `https://resource.fs.com/mall/generalImg/2025022117532374zh0d.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323dt7cki.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323dt7cki.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323u3v30l.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323u3v30l.svg`
	},
	{
		code: `https://resource.fs.com/mall/generalImg/20250221175323ri7z9g.svg`,
		hover: `https://resource.fs.com/mall/generalImg/20250221175323ri7z9g.svg`
	}
];

const products = props?.contentData?.data;
const deviceStore = useDeviceStore();

const shouldShowProduct = computed(() => {
	// console.log(products[isActive.value], isActive.value, "isActive.value");

	const activeProduct = products[isActive.value];
	return products.length && activeProduct && activeProduct.children && activeProduct.children.length;
});

// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(products[isActive.value]?.children, 4) : ReorganizeArrays(products[isActive.value]?.children, 1)));

const isTouch = ref(false);
watch(
	() => deviceStore.screenWidth,
	newVal => {
		isTouch.value = deviceStore.screenWidth <= 1024;
	},
	{
		immediate: true
	}
);
// 处理移动端自适应滑动
let solutionBanner: SwiperClass;
const setProductsRef = (swiper: SwiperClass) => {
	solutionBanner = swiper;
};
const solutionSwiper = computed(() => {
	return solutionBanner;
});
const categoriesRef = ref<HTMLElement | null>(null);
const isActive = ref(0);
const svgChange = (i: number) => {
	isActive.value = i;
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Home Page",
			eventAction: "product_category",
			eventLabel: products[i].name,
			nonInteraction: false
		});
	solutionSwiper.value.slideTo(0);
	if (deviceStore.screenWidth < 768) {
		if (categoriesRef.value?.clientWidth) {
			const sum = getScrollLeft(isActive.value) - categoriesRef.value?.clientWidth;
			if (i < sum) {
				categoriesRef.value.scrollTo({
					left: sum + 60,
					behavior: "smooth" // 这将确保滚动是平滑的
				});
			} else {
				categoriesRef.value.scrollTo({
					left: 0,
					behavior: "smooth" // 这将确保滚动是平滑的
				});
			}
		}
	}
};

// tab导航栏的红色下划线长度跟文字宽度保持一致
const lineRef = ref<HTMLSpanElement[]>([]);
const nameRef = ref<HTMLLabelElement[]>([]);
const initLineWidth = function () {
	products.forEach((item, index) => {
		if (nameRef.value[index]?.offsetWidth !== lineRef.value[index]?.offsetWidth) {
			lineRef.value[index].style.width = `${nameRef.value[index].offsetWidth}px`;
		}
	});
};

const GAPoint = function (i: number, url: string) {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Home Page",
			eventAction: "productCategoryList",
			eventLabel: `${products[i].name}_${url}`,
			nonInteraction: false
		});
};
onMounted(() => {
	initLineWidth();
	window.addEventListener("resize", initLineWidth);
});

onBeforeUnmount(() => {
	window.removeEventListener("resize", initLineWidth);
});

const getScrollLeft = (num: number) => {
	return 100 * (2 * num - 1);
};
</script>

<style lang="scss" scoped>
.products {
	margin: 24px auto 0;
	.pc-product {
		.desc_box {
			flex: 1;
			// border: 1px solid $borderColor1;
			border-top: none;
			display: flex;
			flex-direction: column;
			background: #fafbfb;
			border-bottom-left-radius: 8px;
			border-bottom-right-radius: 8px;
			.tit {
				font-size: 14px;
				line-height: 22px;
				height: 22px;
				color: #19191a;
				font-weight: 600;
				text-align: left;
				margin-top: 16px;
				padding: 0 16px;
				a {
					color: #19191a;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					max-width: 100%;
					width: 100%;
					display: block;
					text-align: center;
				}
			}
		}
		.categories {
			width: 84vw;
			max-width: 1200px;
			margin: 0 auto;
			// border-bottom: 1px solid $borderColor1;
			// padding-bottom: 16px;
			margin-bottom: 24px;
			position: relative;
			transition: scroll-margin-left 0.5s ease;
			ul {
				display: flex;
				gap: 12px;
				justify-content: space-between;
				li {
					padding: 8px 16px;
					// width: calc(100% / 8);
					text-align: center;
					cursor: pointer;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					border-radius: 8px;

					p {
						// padding-top: 12px;
						flex: 1;
						position: relative;
						@include font13;
						color: $textColor1;
						width: fit-content;
						cursor: pointer;
						@include txt-more-hid;
						label {
							cursor: pointer;
						}
						// .line {
						// 	position: absolute;
						// 	bottom: -16px;
						// 	width: 100%;
						// 	height: 2px;
						// 	border-radius: 1.5px;
						// 	background-color: $bgColor4;
						// 	left: 0;
						// 	right: 0;
						// 	margin: auto;
						// 	transition: all 0.2s;
						// 	opacity: 0;
						// }
						@media (max-width: 960px) {
							flex: 1;
						}
					}

					&.active {
						background: #ededf0;
						p {
							color: $textColor1;
							// font-weight: 600;

							.line {
								// opacity: 1;
							}
						}
					}
				}
			}
		}
		.pic {
			padding: 0;
			padding-top: 24px;
			// background: linear-gradient(0deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.02)),
			// 	linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.02) 100%);
			background: #fafbfb;
			display: flex;
			justify-content: center;

			img {
				margin: 10px;
				display: block;
				width: 160px;
				height: 160px;
				max-width: 100%;
				@include mobile {
					width: 121px;
					height: 121px;
				}
			}
			img:not([src]) {
				opacity: 0;
			}
		}
		.category_items {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: flex-start;
			padding: 12px 20px 24px;
			a {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				text-decoration: none;
				&:hover {
					color: $textColor1;
				}
				&:not(:last-child) {
					margin-bottom: 8px;
				}
			}
		}
		.list {
			max-width: 1360px;
			margin: 0 auto;
			.list_cont {
				position: relative;
				width: 84vw;
				max-width: 1200px;
				margin: 0 auto;

				.swiper_box {
					padding-bottom: 0;
					.swiper {
						padding-bottom: 28px;
					}
					.swiper-slide {
						display: flex;
						height: auto;
						width: calc((100% - 64px) / 5);
						margin-right: 16px;
						@media (max-width: 1024px) {
							width: calc((100% - 48px) / 4);
						}
						.product {
							display: flex;
							flex-direction: column;
							justify-content: flex-start;
							width: 100%;
							text-decoration: none;
							transition: all 0.3s;
							border-radius: 8px;
							overflow: hidden;
							.tit {
								display: flex;
								align-items: center;
								justify-content: flex-start;
								padding: 0 20px;
								margin-top: 0;
								span {
									flex: 1;
									@include font14;
									color: $textColor1;
									font-weight: 600;
									text-align: left;
									min-height: 24px;
								}
								i {
									display: none;
									width: 12px;
									height: 10px;
									font-size: 12px;
									margin-left: 8px;
									font-weight: normal;
									color: $textColor1;
								}

								&:hover {
									span {
										text-decoration: underline;
									}
								}
							}
							.isNotEn {
								span {
									&::after {
										display: none;
									}
								}
							}
							.category_items {
								flex: 1;
								a {
									display: block;
									width: 100%;
									min-height: 20px;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									text-align: center;
									&:hover {
										text-decoration: underline;
									}
								}
								.isHide {
									display: block;
									width: 100%;
									white-space: nowrap;
									text-overflow: ellipsis;
									overflow: hidden;
								}
							}
							&:hover {
								box-shadow: 0px 15px 15px -10px rgba(0, 0, 0, 0.15);
							}
						}
					}
					:deep(.swiper-pagination) {
						display: flex;
						justify-content: center;
						bottom: 0;
						.swiper-pagination-bullet {
							background: #707070;
							opacity: 0.4;
							transition: all 0.3s;
							border-radius: 100%;
							&.swiper-pagination-bullet-active {
								width: 20px;
								opacity: 1;
								background: #707070;
								border-radius: 4px;
							}
						}
					}
				}
				.swiper-btn {
					position: absolute;
					font-size: 32px;
					color: #fff;
					width: 48px;
					height: 48px;
					border-radius: 50%;
					transition: all 0.3s;
					box-sizing: border-box;
					background-color: rgba(0, 0, 0, 0.2);
					text-align: center;
					line-height: 48px;
					background-image: none;
					user-select: none;
					opacity: 0;
					margin-top: -60px;
					top: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;
					&.swiper-button-disabled {
						cursor: default;
					}
					&:hover {
						background-color: rgba(0, 0, 0, 0.4);
					}
					&::after {
						content: none;
					}
					@media (max-width: 1024px) {
						display: none;
					}
					.iconfont {
						color: $textColor6;
						font-size: 28px;
					}
				}
				.swiper-button-prev {
					left: -80px;
				}
				.swiper-button-next {
					right: -80px;
				}
				.swiper-button-disabled {
					background-color: rgba(0, 0, 0, 0.05) !important;
				}
			}
			&:hover {
				.list_cont {
					.swiper-btn {
						opacity: 1;
					}
				}
			}
			@media (max-widht: 1420px) {
				width: 100%;
			}
		}
	}
	@include pad {
		.pc-product {
			// .list .list_cont .swiper_box {
			// 	padding-bottom: 36px;
			// }
			.categories,
			.list .list_cont {
				width: 94vw;
			}
		}
	}

	@include mobile {
		margin-top: 0;
		.pc-product {
			.category_items a:not(:last-child) {
				margin-bottom: 8px;
			}
			.categories {
				overflow-x: auto;
				transition: all 0.2s;
				scrollbar-width: none;
				-ms-overflow-style: none;
				position: sticky;
				top: 48px;
				z-index: 20;
				background-color: #fff;
				margin: 0 auto;
				padding: 16px 0 24px;
				width: calc(100% - 32px);
				> ul {
					li {
						// width: 80px;
						flex-shrink: 0;
						margin-right: 0;
						// padding-bottom: 0;
						p .line {
							bottom: -6px;
						}
					}
				}
			}
			.list {
				.list_cont {
					width: calc(100% - 32px);
					.swiper_box {
						padding-bottom: 0;
						:deep(.swiper-pagination) {
							bottom: 0;
						}
						.swiper {
							padding-bottom: 28px;
						}
						.swiper-slide {
							margin: 0;
							width: 100%;
							display: grid;
							grid-template-columns: repeat(2, 2fr);
							grid-column-gap: 20px;
							grid-row-gap: 20px;
							.product {
								margin: 0;
								// width: calc((100% - 16px) / 2);
								max-height: 387px;
								// margin-bottom: 16px;
								.pic {
									padding: 0;
									> a {
										width: 100%;
										display: flex;
										justify-content: center;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
