<template>
	<div class="compare_body">
		<div class="back">
			<FsButton iconPlacement="prefix" text @click="back">
				<template #default>{{ localeLang("secondCategory.compare.back") }}</template>
				<template #icon>
					<i class="iconfont">&#xe702;</i>
				</template>
			</FsButton>
			<div class="m_highlight_switch">
				<div class="title">{{ localeLang("secondCategory.compare.highlight_differences") }}</div>
				<div class="content" :class="{ isOpen: highlightType, disableBtn: isDisableHighlight }" @click.stop="checkHighlightType">
					<div class="btn">
						<i class="iconfont">&#xf060;</i>
					</div>
				</div>
			</div>
		</div>
		<div class="head">
			<div class="title">{{ localeLang("secondCategory.compare.compare_products") }}</div>
			<div class="highlight_switch">
				<div class="title">{{ localeLang("secondCategory.compare.highlight_differences") }}</div>
				<div class="content" :class="{ isOpen: highlightType, disableBtn: isDisableHighlight }" @click.stop="checkHighlightType">
					<div class="btn">
						<i class="iconfont">&#xf060;</i>
					</div>
				</div>
			</div>
		</div>
		<div class="list_all">
			<div class="list" :class="{ sticky: sticky }">
				<div class="sticky_body">
					<div v-for="(item, index) in compareList?.productInfoList" :key="index" class="item">
						<GridCard
							:info="item"
							:inventory="sourceList?.[item.products_id]"
							:cardIndex="index"
							:comparison="comparison"
							:compareIds="compareIds"
							:cancelIds="cancelIds"
							type="pc"
							source="comparison"
							:sticky="sticky"
							@check="checkList" />
					</div>
				</div>
			</div>
			<div v-if="cancelIds.length != compareIds.length" class="tabs">
				<FsTabs>
					<FsTabPane v-for="(item, index) in compareTables" :key="index" :label="item" :name="`${index + 1}`">
						<template v-if="item === localeLang('secondCategory.compare.features')">
							<div class="table features">
								<template v-if="device !== 'mobile'">
									<div
										v-for="(chart, index_c) in compareList?.comparisonCharts"
										:key="index_c"
										class="tr"
										:class="{
											highlighted: completeData(chart, 'name') && highlightType,
											zebra: !highlightType,
											highlightFlag: completeData(chart, 'name')
										}">
										<div v-for="(t, i) in chart" :key="i" class="td">
											<template v-if="!hideFlag.includes(i - 1)">
												<template v-if="t.name === 'icon'">
													<img src="https://front-resource.fs.com/fs-platform/client/img/uphold.cb8232b.svg" />
												</template>
												<template v-else>
													<span v-html="t.name"></span>
												</template>
											</template>
										</div>
									</div>
								</template>
								<template v-else>
									<div
										v-for="(chart, index_c) in compareList?.comparisonCharts"
										:key="index_c"
										class="tr_m"
										:class="{ zebra: !highlightType, highlightFlag: completeData(chart, 'name') }">
										<div class="tr_title">
											<span>{{ chart[0].name }}</span>
										</div>
										<div class="tr_list">
											<div v-for="(t, i) in chart.slice(1)" :key="i" class="td" :class="{ highlighted: completeData(chart, 'name') && highlightType }">
												<template v-if="!hideFlag.includes(i)">
													<template v-if="t.name === 'icon'">
														<img src="https://front-resource.fs.com/fs-platform/client/img/uphold.cb8232b.svg" />
													</template>
													<template v-else>
														<span v-html="t.name"></span>
													</template>
												</template>
											</div>
										</div>
									</div>
								</template>
							</div>
						</template>
						<template v-else-if="item === localeLang('secondCategory.compare.specifications')">
							<div class="table specifications">
								<template v-if="device !== 'mobile'">
									<div
										v-for="(speci, index_s) in compareList?.specifications"
										:key="index_s"
										class="tr"
										:class="{
											highlighted: completeData(speci, 'value') && highlightType,
											zebra: !highlightType,
											highlightFlag: completeData(speci, 'value')
										}">
										<div v-for="(t, i) in speci" :key="i" class="td">
											<template v-if="!hideFlag.includes(i - 1)">
												<span v-html="t.value"></span>
											</template>
										</div>
									</div>
								</template>
								<template v-else>
									<div
										v-for="(speci, index_s) in compareList?.specifications"
										:key="index_s"
										class="tr_m"
										:class="{ zebra: !highlightType, highlightFlag: completeData(speci, 'value') }">
										<div class="tr_title">
											<span>{{ speci[0].value }}</span>
										</div>
										<div class="tr_list">
											<div v-for="(t, i) in speci.slice(1)" :key="i" class="td" :class="{ highlighted: completeData(speci, 'value') && highlightType }">
												<template v-if="!hideFlag.includes(i)">
													<span v-html="t.value"></span>
												</template>
											</div>
										</div>
									</div>
								</template>
							</div>
						</template>
					</FsTabPane>
				</FsTabs>
			</div>
		</div>
		<AddCart :id="addCartId" v-model="addCartStatus" />
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { FsButton, FsTabs, FsTabPane } from "fs-design";
import GridCard from "../SecondCategory/components/GridCard/index.vue";
import type { CardType, CategoryContextKey } from "../SecondCategory/types";
import { isObject } from "@/utils/types";
import AddCart from "@/popup/AddCartPopup/index.vue";
definePageMeta({
	layout: "common"
});
const localeLang = useLocaleLang();
const localeLink = useLocaleLink();
// 加购信息
const displayType: Ref<CardType> = ref("grid");
const addCartId = ref(0);
const addCartStatus = ref(false);

// 获取路由信息
const route = useRoute();
const router = useRouter();

// compareList产品列表、sourceList库存列表
const compareList = ref<any>([]);
const sourceList = ref([]);

// 复选框开启
const comparison = ref(true);

// 选中id
const compareIds = computed(() => {
	return compareList.value?.productInfoList.map((item: { products_id: any }) => {
		return item.products_id;
	});
});

// 取消的id
const cancelIds = ref<any>([]);

// 高亮状态
const highlightType = ref(false);

// 切换高亮状态
const checkHighlightType = () => {
	if (!isDisableHighlight.value) {
		highlightType.value = !highlightType.value;
	}
};

// 隐藏的产品下标
const hideFlag = ref<any>([]);

// 判断是否取消显示
const findIndices = (searchArray: any[], targetArray: string | any[]) => {
	return searchArray.reduce((indices: any[], value: any) => {
		const index = targetArray.indexOf(value);
		if (index !== -1) {
			indices.push(index);
		}
		return indices;
	}, []); // 初始值是一个空数组
};

// 取消和显示勾选对比
const checkList = (item: any) => {
	if (cancelIds.value.includes(item)) {
		cancelIds.value = cancelIds.value.filter((i: any) => i !== item);
	} else {
		cancelIds.value.push(item);
	}
	hideFlag.value = findIndices(cancelIds.value, compareIds.value);
};

// 是否为完整的一行对比数据
const completeData = (data: any[], type: string): boolean => {
	// 首先，过滤掉无效的元素
	const filteredData = data.filter(item => {
		if (type === "name") {
			return isObject(item) && item.hasOwnProperty("name") && typeof item.name === "string" && item.name !== "—";
		} else {
			return isObject(item) && item.hasOwnProperty("value") && typeof item.value === "string" && item.value !== "—";
		}
	});
	// 如果过滤后的数组长度与原始数组不同，说明有无效元素被移除了
	if (filteredData.length !== data.length) {
		return false;
	}

	// 使用 Set 来检查 name 是否唯一
	const nameSet = new Set<string>();
	for (const item of filteredData) {
		if (type === "name") {
			if (nameSet.has(item.name.trim())) {
				return false; // 发现重复的 name
			}
			nameSet.add(item.name.trim());
		} else {
			if (nameSet.has(item.value.trim())) {
				return false; // 发现重复的 name
			}
			nameSet.add(item.value.trim());
		}
	}

	// 如果没有发现重复的 name，则返回 true
	return true;
};
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
const device = computed(() => deviceStore.device);
const sticky = ref(false);
const scrollTop = ref(0);
const compareTop = ref(0);
const onScroll = () => {
	if (document.querySelector(".list") && !isMobile.value) {
		if (!sticky.value) {
			compareTop.value = getElementTop(document.querySelector(".list") as HTMLElement);
		}
		scrollTop.value = document.documentElement.scrollTop || document.body.scrollTop;
		if (scrollTop.value >= compareTop.value - (deviceStore.device != "pc" ? 48 : 0) && scrollTop.value) {
			sticky.value = true;
		} else {
			sticky.value = false;
		}
	}
	document?.querySelector(".fs_header_container")?.classList.remove("fix_top");
};
useEventListener("window", "scroll", onScroll);

// 返回
const back = () => {
	// router.go(-1);
	router.push({ path: localeLink(`/c/${route.query.now_cid}`) });
};

// 获取初始化数据
const getCompareData = async (): Promise<void> => {
	if (route.query.ids && String(route.query.ids).split(",").length > 1) {
		const arr = String(route.query.ids).split(",");
		const [{ data }, { data: newSource }] = await Promise.all([
			useRequest.post(`/api/category/productCompare`, {
				data: {
					productIds: arr
				}
			}),
			useRequest.post(`/api/category_new/newSource`, {
				data: {
					product_ids: arr
				}
			})
		]);
		if (data.value.code !== 200) return;
		compareList.value = data.value.data;
		sourceList.value = newSource.value.data;
	}
};
await getCompareData();
// 对比tables
const compareTables = ref<any>([]);
if (compareList.value?.specifications && compareList.value?.specifications.length > 0) {
	compareTables.value.push(localeLang("secondCategory.compare.specifications"));
}
if (compareList.value?.comparisonCharts && compareList.value?.comparisonCharts.length > 0) {
	compareTables.value.push(localeLang("secondCategory.compare.features"));
}

provide("CategoryContextKey", {
	displayType,
	addCartStatus,
	addCartId
} as CategoryContextKey);

const isDisableHighlight = ref(false);

// 存储产品信息
onMounted(() => {
	if (compareList?.value?.productInfoList && compareList?.value?.productInfoList.length) {
		window.sessionStorage.setItem(
			"session_compare",
			JSON.stringify({
				now_cid: route.query.now_cid,
				list: compareList.value?.productInfoList.map((item: any) => {
					return { id: item.products_id, img: item.image, now_cid: route.query.now_cid };
				})
			})
		);
	}
	if (document?.querySelectorAll(".highlightFlag").length) {
		isDisableHighlight.value = false;
	} else {
		isDisableHighlight.value = true;
	}
});
</script>
<style scoped lang="scss">
@import url("./index.scss");
</style>
