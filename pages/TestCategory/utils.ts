import type { NarrowItemProps, NarrowProps } from "./SecondCategory/components/FilterSelectMenu/types";
import type { MetaLilterList, MetaCrumbs } from "./SecondCategory/types";
import { filterParameters } from "@/utils/utils";

/**
 *
 * @param path
 * @description 获取分类id
 * @returns 分类id
 */
export const getCategoryId = (path: string): number[] => {
	const cleanedPath = path.split(".html")[0].split("?")[0];
	const lastUnderscoreIndex = cleanedPath.lastIndexOf("_");
	const lastDashIndex = cleanedPath.lastIndexOf("-");
	const index = lastUnderscoreIndex > lastDashIndex ? lastUnderscoreIndex : lastDashIndex;
	const id = cleanedPath.substring(index + 1);
	return [+id, index];
};

export const trimString = (str: string): string => {
	return str.replace(/&nbsp;/g, " ");
};

/**
 *
 * @param data  筛选的数据源
 * @param reset  是否重置
 * @returns  映射之后的筛选数据源
 */
export const reMapDataIsCheck = (data: NarrowProps | NarrowProps[], reset?: boolean): NarrowProps | NarrowProps[] => {
	if (Array.isArray(data)) {
		return data.map((item: NarrowProps) => ({
			...item,
			narrow: item.narrow.map((narrowItem: NarrowItemProps) => {
				return {
					...narrowItem,
					checked: reset ? false : narrowItem.isCheck === 2,
					disabled: narrowItem.isCheck === 3
				};
			})
		}));
	} else {
		return {
			...data,
			narrow: data.narrow.map((item: NarrowItemProps) => ({
				...item,
				checked: reset ? false : item.isCheck === 2,
				disabled: item.isCheck === 3
			}))
		};
	}
};

// 链接处理,跳转或返回处理后链接
export const linkTo = (link: string, type?: string) => {
	const localeLink = useLocaleLink();
	if (type === "jump") {
		location.href = localeLink(link);
	} else {
		if (!["/"].includes(link)) {
			return localeLink(link);
		} else {
			return `javascript:void(0);`;
		}
	}
};

/**
 * 设置meta数据
 * @param query 当前route.query
 * @param data  分类页接口返回数据
 * @returns
 */
export const setCategoryMeta = (query: any, data: any) => {
	let og_image: string = "";
	const filterArr: Number[] = [];
	const filterList: MetaLilterList[] = [];
	let origin_crumbs = [];
	if (data?.crumbs?.length) {
		origin_crumbs = JSON.parse(JSON.stringify(data.crumbs));
	}
	let meta_title = data?.metaTag?.title || data?.metaTag?.defaultTitle || "";
	let meta_description = data?.metaTag?.description || data?.metaTag?.defaultDescription || "";
	for (const i in query) {
		if (filterParameters(i)) {
			filterArr.push(Number(query[i]));
		}
	}
	if (data?.categoryProducts?.length > 0) {
		og_image = data?.categoryProducts[0]?.image;
		if (filterArr.length) {
			data.narrowFilter.forEach((item: any) => {
				item.narrow.forEach((it: any) => {
					if (filterArr.includes(it.narrowId)) {
						filterList.push({
							title: item?.enTitle,
							val: { id: it?.narrowId, name: it?.name }
						});
					}
				});
			});
		}
		if (meta_title.includes("##TYPE##")) {
			let all_cate: string = "";
			let crumbs = JSON.parse(JSON.stringify(origin_crumbs));
			crumbs.shift();
			crumbs = crumbs.reverse();
			const ca: String[] = [];
			crumbs.forEach((item: any) => {
				ca.push(item?.name);
			});
			all_cate = ca.join("-");
			let current_cate_list = "";
			let filter_name = "";
			if (data?.categoryFilter?.categories?.length) {
				data.categoryFilter.category.forEach((item: any) => {
					current_cate_list += `${item?.name} `;
				});
			}

			filterList.forEach((item: any) => {
				filter_name += `${item?.val?.name} `;
			});

			let p1: string = "";
			let p2: string = "";
			const desc_category: any = {};
			if (data?.categoryFilter?.categories?.length) {
				// data.categoryFilter.forEach((item: any) => {
				if (["Categories", "Product Type"].includes(data?.categoryFilter?.enTitle)) {
					const arr: String[] = [];
					if (data?.categoryFilter?.categories?.length) {
						data?.categoryFilter?.categories.forEach((citem: any) => {
							arr.push(citem?.name);
						});
					}
					desc_category[data?.categoryFilter?.enTitle] = arr;
				}
				// });
			}

			if (data?.narrowFilter?.length && !desc_category["Product Type"]) {
				data?.narrowFilter.forEach((item: any) => {
					if (["Product Type"].includes(item?.enTitle)) {
						const arr: String[] = [];
						if (item?.narrow?.length) {
							item?.narrow?.forEach((citem: any) => {
								arr.push(citem?.name);
							});
						}
						desc_category[item.enTitle] = arr;
					}
				});
			}

			let desc_crumbs = JSON.parse(JSON.stringify(origin_crumbs));

			desc_crumbs.shift();

			if (desc_crumbs.length === 2) {
				p1 = `${desc_crumbs[1].name} `;
				p2 = desc_category?.Categories && desc_category?.Categories.length ? desc_category?.Categories.join(",") : "";
			} else if (desc_crumbs.length === 3) {
				p1 = `${desc_crumbs[2].name} `;
				p2 = desc_category["Product Type"] && desc_category["Product Type"].length ? desc_category["Product Type"].join(",") : "";
			} else if (desc_crumbs.length > 3) {
				desc_crumbs = desc_crumbs.reverse();
				p1 = `${desc_crumbs[0].name},`;
				desc_crumbs.shift();
				desc_crumbs.pop();
				const p_name: String[] = [];
				desc_crumbs.forEach((c: any) => {
					p_name.push(c.name);
				});
				p2 = p_name.join(",");
			}

			if (filterList.length) {
				meta_title = meta_title.replace("##TYPE##", `${all_cate}`).replace("##FILTER##", `${filter_name}-`);
			} else {
				meta_title = meta_title.replace("##TYPE##", `${all_cate}`).replace("##FILTER##", "");
			}
			meta_description = meta_description.replace("##PARAM1##", p1).replace("##PARAM2##", p2).replace("\u00A0", " ").replace("&nbsp;", " ");
		}
	}
	const combineMetaData = Object.assign({}, data.metaTag, {
		og_image,
		title: meta_title,
		description: meta_description
	});

	return combineMetaData;
};
