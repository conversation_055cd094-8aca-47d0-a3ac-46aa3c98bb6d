export interface Module {
	icon: string;
	sort: number;
	description: string;
	mainDescription: string;
	id: number;
	href: string;
	image: string;
	title: string;
	iconUrl: string;
	url: string;
	mainTitle: string;
	link: string;
	text: string;
	trade: string;
	region: string;
	mainText: string;
	mainTrade: string;
	mainRegion: string;
	videoCover: string;
}

interface Content {
	image: string;
	title: string;
	video: string;
	modules: Module[];
	mainTitle: string;
	description: string;
	mainDescription: string;
	padImage: string;
	alignment: number;
	fontColor: string;
	mobileImage: string;
	url: string;
	text: string;
	mainText: string;
	info: string;
	comment: string;
	mainInfo: string;
	mainComment: string;
	videoTime: string;
	href: string;
}

export interface SpecialDatum {
	content: Content;
	componentId: number;
}

interface Data {
	id: number;
	type: number;
	url: string;
	specialData: SpecialDatum[];
}

export interface RootObject {
	status: string;
	code: number;
	message: string;
	data: Data;
	errors: any[];
}

export interface propsData {
	contentData: SpecialDatum;
}
