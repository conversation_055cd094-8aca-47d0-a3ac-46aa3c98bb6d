<template>
	<main>
		<SpecialComponents v-for="item in list" :key="item.componentId" :contentData="item"></SpecialComponents>
	</main>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getCategoryId } from "../utils";
import type { SpecialDatum } from "./types";
import SpecialComponents from "./components/SpecialComponents/SpecialComponents.vue";
const localeLang = useLocaleLang();
defineOptions({
	name: "FirstCategory"
});
// 路由数据
const route = useRoute();
const setMeta = useSetMeta();
const localeLink = useLocaleLink();
// 重定向链接
const redirectUrl = ref<any>([]);
// 获取分类模块数据
const list = ref<SpecialDatum[]>([]);
const getCategoryData = async (): Promise<void> => {
	const [id] = getCategoryId(route.path);
	const categoryString = `/cms/api/fsSubject/detail/${id}`;
	const [{ data }, { data: metaData }] = await Promise.all([
		useRequest.post(`/static`, {
			data: {
				method: "GET",
				url: categoryString,
				filterId: id, // 标识字段
				moduleName: "firstCategory" // 模块名称
			}
		}),
		useRequest.post(`/api/metaTag/getMetaTag`, {
			data: {
				meta_id: id,
				meta_type: 2
			}
		})
	]);
	redirectUrl.value = data?.value?.data?.url;
	list.value = data?.value?.data?.specialData;

	if (metaData.value && metaData.value.data) {
		setMeta(metaData.value.data);
	}
};
await getCategoryData();
useHeadMeta();

onMounted(async () => {
	await nextTick();
	if (redirectUrl.value !== route.path) {
		// navigateTo({ path: localeLink(redirectUrl.value), query: route.query });
	}
	const [id] = getCategoryId(route.path);
	if (id == 4222) {
		list.value.splice(list.value.length - 1, 0, {
			componentId: 4222,
			content: {
				title: localeLang("firstCategory.DC.tit"),
				description: localeLang("firstCategory.DC.desc"),
				href: localeLang("firstCategory.DC.url"),
				image: localeLang("firstCategory.DC.img"),
				btn: localeLang("firstCategory.DC.btn")
			}
		});
	}
	useRequest.get(`/cms/api/fsSubject/detail/${id}`);
});

console.log("911");
</script>

<style scoped lang="scss">
@import url("./index.scss");
</style>
