<template>
	<div class="dc_tool">
		<div class="common">
			<div class="lt">
				<h3 class="tit">{{ contentData.content.title }}</h3>
				<p class="desc">{{ contentData.content.description }}</p>
				<a :href="contentData.content.href">
					<fs-button type="black">{{ contentData.content.btn }}</fs-button>
				</a>
			</div>
			<div class="rt">
				<img :src="contentData.content.image" alt="" />
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { FsButton } from "fs-design";
const props = defineProps({
	contentData: {
		type: Object,
		default() {
			return {};
		}
	}
});
const { contentData } = props;
</script>
<style lang="scss" scoped>
.dc_tool {
	@include contentWidth;
	padding: 40px 0;
	margin-top: -8px;
	.common {
		display: flex;
		> div {
			width: 50%;
		}
		.lt {
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding: 20px 36px 20px 0;
			.tit {
				@include font20;
			}
			.desc {
				@include font14;
				margin: 8px 0 16px;
				color: $textColor2;
			}
			a {
				width: fit-content;
				text-decoration: none;
			}
		}
		.rt {
			img {
				width: 100%;
				border-radius: 8px;
				display: block;
			}
		}
	}
}
@include mobile {
	.dc_tool {
		margin-top: 0;
		padding: 36px 0;
		.common {
			flex-direction: column-reverse;
			gap: 20px;
			> div {
				width: 100%;
			}
			.lt {
				padding: 0;
				.tit {
					@include font14;
				}
				.desc {
					@include font12;
					margin: 8px 0 16px;
				}
			}
		}
	}
}
</style>
