<template>
	<section class="fs-labs">
		<div class="fs-labs-view">
			<div class="fs-labs-video">
				<img :src="contentData.content.image" :alt="contentData.content.videoTime" />
				<div class="play" @click="dialogVisible = true">
					<i class="iconfont iconfont_play">&#xf205;</i>
					<span>{{ contentData.content.videoTime }}</span>
				</div>
			</div>
			<ul class="fs-labs-view-list">
				<li v-for="(item, index) in contentData.content.modules" :key="index" class="fs-labs-view-item">
					<img :src="item.icon" :alt="item.description" />
					<strong>{{ item.description }}</strong>
				</li>
			</ul>
		</div>
		<FsDialog v-model="dialogVisible" className="fs-labs-dialog" :title="contentData.content?.title">
			<FsVideo v-if="dialogVisible" id="video-player2" class="fs-labs-video-view" :src="contentData.content?.video" :autoplay="true" width="auto" />
		</FsDialog>
	</section>
</template>

<script setup lang="ts">
import { FsDialog, FsVideo } from "fs-design";
import type { propsData } from "../../types";
defineOptions({
	name: "FSLabs"
});
// 传参数据
defineProps<propsData>();
const dialogVisible = ref();
</script>

<style lang="scss" scoped>
@import url("./FSLabs.scss");
</style>
<style lang="scss">
.fs-labs-dialog {
	&.fs-dialog__content,
	&.fs-dialog__contentM {
		width: 100%;
		max-width: 750px;

		.fs-dialog__content--header,
		.fs-dialog__contentM--header {
			padding: 14px 32px;

			.fs-dialog__content--title,
			.fs-dialog__contentM--title {
				@include font20;
				color: $textColor1;
				font-weight: 600;
			}

			.fs-icon {
				font-size: 16px;
				color: $textColor2;
			}
		}

		.fs-dialog__content--box,
		.fs-dialog__contentM--box {
			padding: 0;
			flex: 1;
			display: flex;
			align-items: center;
		}

		@media (max-width: 768px) {
			height: 100%;
			min-width: 100%;
			max-height: 100%;
			background-color: #000;
			display: flex;
			flex-direction: column;

			.fs-dialog__content--header,
			.fs-dialog__contentM--header {
				border: none;

				.fs-dialog__content--close,
				.fs-dialog__contentM--close {
					color: $textColor6;
				}
			}

			.fs-labs-video-view {
				margin-top: -70px;
			}
		}
	}
}
// 等video公共组件之后优化
</style>
