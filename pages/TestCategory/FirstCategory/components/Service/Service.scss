.fs-service {
	width: 100%;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-image: url(https://img-en.fs.com/network_img_new/cate/fs-stay/web.png);
	.fs-service-content {
		@include contentWidth;
		height: 200px;
		overflow: hidden;
		display: flex;
		.fs-service-title {
			display: flex;
			align-items: center;
			> p {
				width: 400px;
				@include font20;
				font-weight: 600;
				color: #19191a;
				padding-right: 56px;
				border-right: 1px solid $borderColor1;
			}
		}
		.fs-service-list {
			flex: 1;
			display: flex;
			align-items: center;
			> li {
				flex: 1;
				margin-left: 78.5px;
				> figure {
					> img {
						width: 36px;
						height: 36px;
					}
					> figcaption {
						> a {
							@include font14;
							font-weight: 600;
							color: $textColor1;
							margin-top: 8px;
						}
						> p {
							@include font12;
							color: $textColor2;
							margin-top: 8px;
						}
					}
				}
			}
		}
	}
	@include pad {
		background-image: url(https://img-en.fs.com/network_img_new/cate/fs-stay/pad.png);
		.fs-service-content {
			height: auto;
			flex-direction: column;
			.fs-service-title {
				text-align: center;
				> p {
					width: 100%;
					padding: 48px 0;
					border-right: none;
					border-bottom: 1px solid $borderColor1;
				}
			}
			.fs-service-list {
				padding: 24px 0;
				> li {
					margin: 0;
					padding: 24px 28px;
					text-align: center;
					> figure {
						> img {
							margin: 0 auto;
						}
					}
				}
			}
		}
	}
	@include mobile {
		background-image: url(https://img-en.fs.com/network_img_new/cate/fs-stay/m.png);
		.fs-service-content {
			height: auto;
			flex-direction: column;
			.fs-service-title {
				text-align: center;
				> p {
					width: 100%;
					padding: 36px 0;
					border-right: none;
					border-bottom: 1px solid $borderColor1;
				}
			}
			.fs-service-list {
				flex-direction: column;
				align-items: flex-start;
				> li {
					width: 100%;
					margin: 0;
					padding: 36px 28px;
					> figure {
						display: flex;
						align-items: center;
						> img {
							margin: 0;
						}
						> figcaption {
							text-align: left;
							margin-left: 16px;
							> a {
								margin-top: 0;
							}
						}
					}
				}
			}
		}
	}
}
