<template>
	<section class="fs-why-us">
		<ul class="fs-why-us-list">
			<swiper :modules="modules" :slidesPerView="slidesPerView" :spaceBetween="spaceBetween" :pagination="pagination">
				<swiper-slide v-for="(item, index) in list" :key="index">
					<div class="fs-why-us-item">
						<a v-for="(t, i) in item" :key="i" :href="linkTo(t.link)" :class="{ unClick: isClick(t.link) }" @click="bdClick(t.link)">
							<figure>
								<img :src="t.image" :alt="t.title" />
								<figcaption>
									<strong class="title">{{ t.title }}</strong>
									<p class="describe">{{ t.description }}</p>
									<FsButton v-if="!['/'].includes(t.link)" class="more" iconPlacement="suffix" text>
										<template #default>
											<template v-if="index === 0">
												<span v-if="i === 0">{{ localeLang("firstCategory.Why_Us.FSs_Labs") }}</span>
												<span v-if="i === 1">{{ localeLang("firstCategory.Why_Us.Solution_Design_Capabilities") }}</span>
												<span v-if="i === 2">{{ localeLang("firstCategory.Why_Us.Testing_Capabilities") }}</span>
												<span v-if="i === 3">{{ localeLang("firstCategory.Why_Us.Global_Warehouse") }}</span>
												<span v-if="i === 4">{{ localeLang("firstCategory.Why_Us.Tech_Support") }}</span>
												<span v-if="i === 5">{{ localeLang("firstCategory.Why_Us.Quality_Certification") }}</span>
											</template>
											<template v-else-if="index === 1">
												<span v-if="i === 0">{{ localeLang("firstCategory.Why_Us.Testing_Capabilities") }}</span>
												<span v-if="i === 1">{{ localeLang("firstCategory.Why_Us.Global_Warehouse") }}</span>
											</template>
											<template v-else-if="index === 2">
												<span v-if="i === 0">{{ localeLang("firstCategory.Why_Us.Tech_Support") }}</span>
												<span v-if="i === 1">{{ localeLang("firstCategory.Why_Us.Quality_Certification") }}</span>
											</template>
										</template>
										<template #icon>
											<span class="iconfont right">&#xe703;</span>
										</template>
									</FsButton>
								</figcaption>
							</figure>
						</a>
					</div>
				</swiper-slide>
			</swiper>
		</ul>
	</section>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import { computed } from "vue";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";

import { linkTo } from "../../../utils";
import type { propsData } from "../../types";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";

defineOptions({
	name: "WhyUs"
});
// 语言包配置
const localeLang = useLocaleLang();
// 传参设备信息和路由配置
const props = defineProps<propsData>();
const deviceStore = useDeviceStore();
const bdRequest = useBdRequest();
// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const slidesPerView = 1;
const spaceBetween = 16;
// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(props.contentData.content.modules, 2) : ReorganizeArrays(props.contentData.content.modules, 0)));
// 是否可点击
const isClick = (link: string) => link === "/";

const bdClick = (url: string) => {
	if (url.includes("solution_design.html") || url.includes("solution_support.html")) {
		bdRequest([
			{
				logidUrl: location.href,
				newType: 20
			}
		]);
	}
};
</script>

<style scoped lang="scss">
@import url("./WhyUs.scss");
</style>
