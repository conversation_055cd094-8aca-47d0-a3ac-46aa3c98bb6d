.fs-why-us {
	@include contentWidth;
	.fs-why-us-describe {
		margin-top: 12px;
		@include font14;
		color: $textColor2;
		text-align: center;
	}
	.fs-why-us-list {
		.fs-why-us-item {
			margin-top: 24px;
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-gap: 20px;
			> a {
				padding: 32px;
				border-radius: 8px;
				border: 1px solid $borderColor1;
				transition: all 0.3s ease-in-out;
				&:hover {
					text-decoration: none;
					box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
					.more {
						:deep(.fs-button--suffix) {
							text-decoration: underline;
						}
					}
				}
				&.unClick {
					cursor: default;
				}
				figure {
					img {
						width: 36px;
						height: 36px;
						margin: 0 auto 8px;
					}
					figcaption {
						text-align: center;
						.title {
							@include font14;
							color: $textColor1;
						}
						.describe {
							@include font12;
							color: $textColor2;
							margin-top: 4px;
						}
						.more {
							justify-content: center;
							text-decoration: none;
							.right {
								font-size: 12px;
								line-height: 1;
								color: $textColor1;
							}
							&:hover {
								.right {
									text-decoration: none;
								}
							}
						}
					}
				}
				&.unClick {
					cursor: default;
					&:hover {
						box-shadow: none;
					}
				}
			}
		}
	}
	@include mobile {
		padding: 0 0 20px;
		overflow: hidden;
		.fs-why-us-list {
			.fs-why-us-item {
				grid-template-columns: repeat(1, 1fr);
			}
		}
	}
}
