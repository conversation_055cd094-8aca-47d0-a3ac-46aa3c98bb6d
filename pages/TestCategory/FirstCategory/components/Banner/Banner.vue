<template>
	<section class="fs-banner">
		<h1 class="fs-banner-title" :style="`color: ${contentData.content.fontColor};`">{{ contentData.content.title }}</h1>
	</section>
</template>

<script setup lang="ts">
import type { propsData } from "../../types";
defineOptions({
	name: "Banner"
});
// 获取设备数据和传参
const props = defineProps<propsData>();
// PC、Pad、Mobile
const bannerUrlPc = `url(${props.contentData.content.image})`;
const bannerUrlPad = `url(${props.contentData.content.padImage || props.contentData.content.image})`;
const bannerUrlMobile = `url(${props.contentData.content.mobileImage})`;
</script>

<style scoped lang="scss">
@import url("./Banner.scss");
.fs-banner {
	@include pc() {
		background-image: v-bind(bannerUrlPc);
	}
	@include pad() {
		background-image: v-bind(bannerUrlPad);
	}
	@include mobile() {
		background-image: v-bind(bannerUrlMobile);
	}
}
</style>
