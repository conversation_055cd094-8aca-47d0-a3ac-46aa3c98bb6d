<template>
	<section class="fs-testing">
		<ul class="fs-testing-list">
			<swiper :modules="modules" :slidesPerView="slidesPerView" :spaceBetween="spaceBetween" :pagination="pagination">
				<swiper-slide v-for="(item, index) in list" :key="index">
					<div class="fs-testing-item">
						<a v-for="(t, i) in item" :key="i" :href="linkTo(t.link)">
							<figure>
								<div>
									<img :src="t.videoCover" alt="video cover" />
								</div>
								<figcaption>
									<div class="text">
										<p class="title">{{ t.title }}</p>
										<p class="describe">{{ t.description }}</p>
									</div>
									<FsButton v-if="!['/'].includes(t.link)" class="more" iconPlacement="suffix" text>
										<template #default>{{ localeLang("firstCategory.Why_Us.FSs_Labs") }}</template>
										<template #icon>
											<span class="iconfont right">&#xe703;</span>
										</template>
									</FsButton>
								</figcaption>
							</figure>
						</a>
					</div>
				</swiper-slide>
			</swiper>
		</ul>
	</section>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import { computed } from "vue";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";

import { linkTo } from "../../../utils";
import type { propsData } from "../../types";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";

defineOptions({
	name: "Testing"
});
// 语言包配置
const localeLang = useLocaleLang();
// 设备和传参路由数据
const deviceStore = useDeviceStore();
const props = defineProps<propsData>();
// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const slidesPerView = 1;
const spaceBetween = 16;
// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(props.contentData.content.modules, 1) : ReorganizeArrays(props.contentData.content.modules, 0)));
</script>

<style lang="scss" scoped>
@import url("./Testing.scss");
</style>
