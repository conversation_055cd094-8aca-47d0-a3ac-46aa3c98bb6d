.fs-design {
	overflow: hidden;
	@include contentWidth;
	.fs-design-list {
		display: flex;
		margin-top: 24px;
		.fs-design-item {
			width: 25%;
			> figure {
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				.step {
					width: 68px;
					height: 68px;
				}
				.arrow {
					width: 36px;
					height: 16px;
					position: absolute;
					top: 25px;
					right: -18px;
				}
			}
			> .fs-design-description {
				margin-top: 12px;
				text-align: center;
				@include font14;
				color: $textColor1;
				font-weight: 600;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
			&:last-child {
				.arrow {
					display: none;
				}
			}
		}
	}
	.containM {
		padding: 16px;
		display: flex;
		margin-top: 24px;
		border-radius: 4px;
		box-sizing: border-box;
		border: 1px solid #e5e5e5;
		.step_list {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			gap: 24px;
			> li {
				width: 100%;
				.bottom {
					display: flex;
					flex-direction: row;
					gap: 12px;
					.num {
						width: 20px;
						height: 20px;
						border-radius: 10px;
						box-sizing: border-box;
						border: 1px solid #19191a;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 12px;
						font-weight: 600;
						line-height: 20px;
						color: #19191a;
					}
					h3 {
						@include font14;
						font-weight: 600;
						color: $textColor1;
						text-align: left;
						line-height: 22px;
					}
				}
				&:last-child {
					.top {
						.arrow {
							display: none;
						}
					}
				}
			}
		}
	}
	.fs-design-submit {
		display: block;
		margin: 24px auto 0;
	}
	// @include mobile {
	// 	.fs-design-list {
	// 		.fs-design-item {
	// 			> figure {
	// 				.step {
	// 					width: 60px;
	// 					height: 60px;
	// 				}
	// 				.arrow {
	// 					width: 26px;
	// 					height: 14px;
	// 					top: 23px;
	// 					right: -13px;
	// 				}
	// 			}
	// 		}
	// 	}
	// }
}
