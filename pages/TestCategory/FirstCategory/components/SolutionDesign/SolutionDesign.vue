<template>
	<section class="fs-design">
		<div v-if="!isMobile" class="fs-design-list">
			<div v-for="(item, index) in contentData.content.modules" :key="index" class="fs-design-item">
				<figure>
					<img class="step" :src="item.iconUrl" alt="step icon" />
					<img class="arrow" src="https://img-en.fs.com/includes/templates/fiberstore/images/new_special_page/first_level_cate/arrows.png" alt="arrow" />
				</figure>
				<p class="fs-design-description">{{ item.description }}</p>
			</div>
		</div>
		<div v-if="isMobile" class="containM">
			<ul class="step_list">
				<li v-for="(item, index) in contentData.content.modules" :key="index">
					<div class="bottom">
						<div class="num">{{ index + 1 }}</div>
						<h3>{{ item.description }}</h3>
					</div>
				</li>
			</ul>
		</div>
		<FsButton type="red" class="fs-design-submit" @click="designClick(contentData.content.url, 'jump')">
			{{ contentData.content.text }}
		</FsButton>
	</section>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import { linkTo } from "../../../utils";
import type { propsData } from "../../types";
const bdRequest = useBdRequest();
defineOptions({
	name: "SolutionDesign"
});

const designClick = (url: string, type: string) => {
	linkTo(url, type);
	bdRequest([
		{
			logidUrl: location.href,
			newType: 20
		}
	]);
};
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
// 传参数据
defineProps<propsData>();
</script>

<style lang="scss" scoped>
@import url("./SolutionDesign.scss");
</style>
