.fs-products {
	@include contentWidth;
	.fs-products-list {
		margin-top: 24px;
		.fs-products-item {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			grid-gap: 20px;
			> a {
				background-color: $bgColor6;
				transition: all 0.3s ease-in-out;
				background-color: $bgColor6;
				> figure {
					height: 100%;
					background-color: $bgColor1;
					display: flex;
					flex-direction: column;
					border-radius: 8px;
					> div {
						position: relative;
						padding-top: 93%;
						> img {
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							width: 100%;
							max-width: 208px;
							margin: auto;
						}
					}
					> figcaption {
						flex: 1;
						background-color: $bgColor6;
						padding: 20px;
						border: 1px solid $borderColor1;
						border-top: none;
						border-radius: 0 0 8px 8px;
						.text {
							.title {
								@include font14;
								color: $textColor1;
								font-weight: 600;
							}
							.describe {
								@include font12;
								color: $textColor2;
								margin-top: 4px;
							}
						}
					}
				}
				&:hover {
					text-decoration: none;
					box-shadow: 0 15px 15px -10px rgba(0, 0, 0, 0.15);
					> figure {
						> figcaption {
							.text {
								> .title {
									text-decoration: underline;
								}
							}
						}
					}
				}
			}
		}
	}
	@include pad {
		.fs-products-list {
			.fs-products-item {
				> a {
					> figure {
						> div {
							> img {
								max-width: 170px;
							}
						}
					}
				}
			}
		}
	}
	@include mobile {
		padding: 0 0 20px;
		overflow: hidden;
		.fs-products-list {
			.fs-products-item {
				grid-template-columns: repeat(1, 1fr);
				> a {
					> figure {
						> div {
							padding-top: 35.7%;
							> img {
								max-width: 200px;
							}
						}
					}
				}
			}
		}
	}
}
