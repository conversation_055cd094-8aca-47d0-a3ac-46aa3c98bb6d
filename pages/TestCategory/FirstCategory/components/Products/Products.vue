<template>
	<section class="fs-products">
		<ul class="fs-products-list">
			<swiper :modules="modules" :slidesPerView="slidesPerView" :spaceBetween="spaceBetween" :pagination="pagination">
				<swiper-slide v-for="(item, index) in list" :key="index">
					<div class="fs-products-item">
						<a v-for="(t, i) in item" :key="i" :href="linkTo(t.href)">
							<figure>
								<div>
									<img :src="t.image" :alt="t.title" />
								</div>
								<figcaption>
									<div class="text">
										<p class="title">{{ t.title }}</p>
										<p class="describe">{{ t.description }}</p>
									</div>
								</figcaption>
							</figure>
						</a>
					</div>
				</swiper-slide>
			</swiper>
		</ul>
	</section>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Swiper, SwiperSlide } from "fs-design-swiper/vue";
import { Pagination } from "fs-design-swiper/modules";

import { linkTo } from "../../../utils";
import type { propsData } from "../../types";
import "fs-design-swiper/css";
import "fs-design-swiper/css/pagination";

defineOptions({
	name: "Products"
});
// 传参设备路由数据
const props = defineProps<propsData>();
const deviceStore = useDeviceStore();
// Swiper配置
const modules = [Pagination];
const pagination = { clickable: true };
const slidesPerView = 1;
const spaceBetween = 16;
// 动态获取列表渲染数据
const list = computed(() => (deviceStore.screenWidth <= 768 ? ReorganizeArrays(props.contentData.content.modules, 1) : ReorganizeArrays(props.contentData.content.modules, 0)));
</script>

<style lang="scss" scoped>
@import url("./Products.scss");
</style>
