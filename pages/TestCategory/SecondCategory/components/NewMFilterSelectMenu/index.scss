.mFilterSelectMenu {
	&__list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px 16px 24px 16px;

		&__left {
			display: flex;
			flex: 1;
			grid-gap: 4px;
			// height: 40px;
			margin-right: 8px;
		}

		.list__left__item {
			display: flex;
			align-items: center;
			padding: 4px 4px;
			@include font12;
			overflow: hidden;
			border-radius: 999px;
			background: #f6f6f8;

			span {
				padding: 4px 8px;
				color: $textColor2;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;

				&.isAct {
					background-color: #fff;
					border-radius: 9999px;
					color: $textColor1;
				}
			}

			i {
				display: none;
				font-size: 12px;
				margin-left: 4px;
				color: $textColor2;
			}

			&.active {
				span {
					background-color: #fff;
					border-radius: 9999px;
				}

				i {
					color: #19191a;
					font-weight: 600;
					transform: rotateX(-180deg);
					transition: all 0.3s;
				}
			}

			&.comparison_btn {
				display: flex;
				align-items: center;
				margin-left: auto;
				margin-right: 0;

				&.disabled {
					opacity: 0.3;
					cursor: no-drop;
				}
			}

			> img {
				width: 16px;
				height: 16px;
				display: block;
				margin-right: 4px;
			}
		}

		&__right {
			flex-shrink: 0;
			padding: 8px;
			// @include font14;
			color: $textColor1;
			border-radius: 4px;
			border: 1px solid $borderColor1;
			background-color: $bgColor1;

			i {
				font-size: 18px;
				line-height: 1;
				display: block;
				color: $textColor1;
			}

			span {
				display: none;
			}

			&.isAct {
				position: relative;

				&::after {
					content: "";
					position: absolute;
					right: 4px;
					top: 4px;
					width: 4px;
					height: 4px;
					background-color: #4080ff;
					border-radius: 50%;
				}

				// border-color: $borderColor4;

				i {
					color: $textColor1;
				}
			}
		}

		&__all {
			padding: 4px;
			color: $textColor1;
			background-color: #f6f6f8;
			border-radius: 9999px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 12px;

			span {
				padding: 4px 8px;
				color: #707070;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}

			&.isAct {
				span {
					border-radius: 9999px;
					background-color: #fff;
					color: $textColor1;
				}
			}
		}
	}

	.justifyBetween {
		justify-content: space-between;
	}

	&__banner {
		position: relative;
		margin: 0 16px 16px;

		> a {
			> img {
				width: 100%;
				border-radius: 999px;
			}
		}

		.txt {
			position: absolute;
			top: 12px;
			right: 12px;
			display: flex;
			align-items: center;
			width: 78.426%;
			height: 66.67%;

			.title {
				width: 79.443%;
				@include font12;
				color: $textColor1;
				margin-right: 12px;
				font-weight: 600;
				@include textClampOverflow(2);
			}

			.btn {
				width: 16.023%;
				height: 100%;
				border-radius: 50%;
				background-color: $textColor11;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);

				> span {
					@include font14;
					color: $textColor1;
				}
			}
		}
	}

	&__switch {
		padding: 0 16px 16px;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.total {
			@include font12;
			color: $textColor1;
			font-weight: 400;
			margin-right: 20px;
		}

		.list {
			flex: 1;
			display: flex;
			grid-gap: 12px;

			.item {
				width: 60px;
				height: 60px;

				> img {
					display: block;
					width: 100%;
					height: 100%;
				}
			}
		}

		.option {
			display: flex;
			align-items: center;

			.iconfont {
				margin-left: 8px;
				padding: 6px;
				border-radius: 3px;
				font-size: 16px;
				line-height: 1;
				color: $textColor2;
				cursor: pointer;

				&:hover {
					color: $textColor1;
					background: rgba(25, 25, 26, 0.04);
				}
			}
		}
	}

	&__result {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px 16px 16px;

		&__total {
			@include font12;
			color: $textColor2;
		}
	}
}

.mFilterSelectMenu__list__rest {
	color: $textColor1;
	font-size: 12px;
	border-radius: 999px;
	background: #f6f6f8;
	// width: 36px;
	// height: 36px;
	padding: 4px;
	display: flex;
	align-items: center;
	justify-content: center;

	span {
		padding: 4px 8px;
		color: $textColor1;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		display: inline-block;

		&.isAct {
			background-color: #fff;
			border-radius: 999px;
		}
	}
}

.mFilterSelectMenu__dropdown {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.mFilterSelectMenu__dropdown__item {
	display: flex;
	align-items: center;
	padding: 4px 4px;
	font-size: 12px;
	line-height: 20px;
	overflow: hidden;
	border-radius: 999px;
	background: #f6f6f8;

	span {
		padding: 4px 8px;
		color: #707070;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		display: inline-block;

		&.isAct {
			background-color: #fff;
			border-radius: 999px;
		}
	}
}
