.mFilterSelectMenu {
	&__head {
		display: flex;
		align-items: center;
		margin: 0 16px 16px;
		border-radius: 3px;
		padding: 0 3px;
		// width: fit-content;
		justify-content: space-between;

		span {
			@include font20;
			color: $textColor1;
			font-weight: 600;
		}

		i {
			color: $textColor1;
			font-size: 12px;
			line-height: 1;
			margin-left: 8px;
			transition: all 0.3s;
		}

		.active {
			transform: rotateX(-180deg);
		}

		.mFilterSelectMenu__head__switch {
			display: flex;
			align-items: center;

			.title {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				margin-right: 8px;
			}

			.content {
				width: 44px;
				height: 24px;
				padding: 2px;
				border-radius: 999px;
				background-color: $bgColor7;
				cursor: pointer;
				transition: all 0.2s;
				position: relative;

				.btn {
					width: 20px;
					height: 20px;
					background-color: $bgColor6;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: absolute;
					top: 2px;
					left: 2px;

					.iconfont {
						display: block;
						font-size: 12px;
						line-height: 1;
						color: $textColor2;
						opacity: 0;
						transition: all 0.2s;
						margin-left: 0;
					}
				}

				&.isOpen {
					background-color: $bgColor8;

					.btn {
						left: 22px;

						.iconfont {
							opacity: 1;
						}
					}
				}
			}
		}
	}

	&__list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px 16px;

		&__left {
			display: flex;
			flex: 1;
			grid-gap: 4px;
			height: 36px;
			margin-right: 20px;
		}

		.list__left__clear {
			font-size: 12px;
			font-style: italic;
			color: $textColor2;
			margin-left: 8px;
			display: flex;
			align-items: center;
			white-space: nowrap;
		}

		.list__left__item {
			display: flex;
			align-items: center;
			padding: 10px 16px;
			@include font12;
			// max-width: 110px;
			overflow: hidden;
			border-radius: 999px;
			border: 1px solid $borderColor1;

			&.isLine {
				border-color: $borderColor4;
			}

			span {
				color: $textColor1;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				// font-weight: 600;

				&.isAct {
					color: $textColor1;
				}
			}

			i {
				display: none;
				font-size: 12px;
				margin-left: 4px;
				color: $textColor2;
			}

			&.active {
				i {
					color: #19191a;
					font-weight: 600;
					transform: rotateX(-180deg);
					transition: all 0.3s;
				}
			}

			&.comparison_btn {
				display: flex;
				align-items: center;
				margin-left: auto;
				margin-right: 0;

				&.disabled {
					opacity: 0.3;
					cursor: no-drop;
				}
			}

			> img {
				width: 16px;
				height: 16px;
				display: block;
				margin-right: 4px;
			}
		}

		&__right {
			flex-shrink: 0;
			padding: 8px;
			// @include font14;
			color: $textColor1;
			border-radius: 4px;
			border: 1px solid $borderColor1;
			background-color: $bgColor1;

			i {
				font-size: 18px;
				line-height: 1;
				display: block;
				color: $textColor1;
				// margin-right: 4px;
			}

			span {
				display: none;
			}

			&.isAct {
				position: relative;

				&::after {
					content: "";
					position: absolute;
					top: 4px;
					right: 4px;
					width: 4px;
					height: 4px;
					border-radius: 50%;
					background-color: #4080ff;
				}

				// border-color: $borderColor4;

				i {
					color: $textColor1;
				}
			}
		}
	}

	&__banner {
		position: relative;
		margin: 16px;

		> a {
			> img {
				width: 100%;
				border-radius: 999px;
			}
		}

		.txt {
			position: absolute;
			top: 12px;
			right: 12px;
			display: flex;
			align-items: center;
			width: 78.426%;
			height: 66.67%;

			.title {
				width: 79.443%;
				@include font12;
				color: $textColor1;
				margin-right: 12px;
				font-weight: 600;
				@include textClampOverflow(2);
			}

			.btn {
				width: 16.023%;
				height: 100%;
				border-radius: 50%;
				background-color: $textColor11;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);

				> span {
					@include font14;
					color: $textColor1;
				}
			}
		}
	}

	&__switch {
		padding: 0 16px 16px;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.total {
			@include font12;
			color: $textColor1;
			font-weight: 400;
			margin-right: 20px;
		}

		.list {
			flex: 1;
			display: flex;
			grid-gap: 8px;

			.item {
				width: 60px;
				height: 60px;

				> img {
					display: block;
					width: 100%;
					height: 100%;
				}
			}
		}

		.option {
			display: flex;
			align-items: center;

			.iconfont {
				margin-left: 8px;
				padding: 6px;
				border-radius: 3px;
				font-size: 16px;
				line-height: 1;
				color: $textColor2;
				cursor: pointer;

				&:hover {
					color: $textColor1;
					background: rgba(25, 25, 26, 0.04);
				}
			}
		}
	}

	&__result {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 16px 0;

		&__total {
			@include font12;
			color: $textColor2;
		}
	}
}

.splitline {
	border-top: 1px solid #eee;
}
