<template>
	<div class="filterSelectMenu" :class="{ noNarrow: narrow.length === 0 }">
		<div v-if="narrow?.length" class="filterSelectMenu__list">
			<ClientOnly>
				<template v-for="v in narrow.slice(0, computedState)" :key="v.productsNarrowByOptionsId">
					<FilterSelect :options="v" @changeOption="changeOption" />
				</template>
				<div v-if="narrow.slice(computedState)?.length" class="filterSelectMenu__more">
					<FilterSelect :options="narrow.slice(computedState)" @changeOption="changeOption" />
				</div>
			</ClientOnly>
			<div v-if="filterKeys.length > 0" class="filterSelectMenu__clear" @click="removeAll">
				<template v-if="narrow.length">
					<FsButton iconPlacement="suffix" text type="gray">
						<span>{{ localeLang("secondCategory.menu.clear_all") }}</span>
						<template #icon>
							<i class="iconfont">&#xf30a;</i>
						</template>
					</FsButton>
				</template>
				<template v-else>
					<i class="iconfont">&#xe702;</i>
					<span>{{ localeLang("secondCategory.back") }}</span>
				</template>
			</div>
		</div>
		<div class="filterSelectMenu__type">
			<span class="filterSelectMenu__type__total">{{ Results }}</span>
			<FilterSelectType />
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import FilterSelect from "../FilterSelect/index.vue";
import FilterSelectType from "../FilterSelectType/index.vue";
import useRouteInfo from "../../hooks/useRouteInfo";
import type { CardType, CategoryContextKey } from "../../types";
import { useGetCategoryNumber } from "../../hooks/useGetCategoryNumber";
import type { FilterSelectMenuProps, ChangeOptionProps } from "./types";
const props = defineProps<FilterSelectMenuProps>();
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const route = useRoute();
const router = useRouter();
const state = useGetCategoryNumber(props.narrow.length);
const localeLang = useLocaleLang();
// const isGrid = computed(() => injectState.displayType.value === "grid");
const { filterKeys, filterVals } = useRouteInfo();
const computedState = computed(() => {
	return props.crumbs.length === 3 ? state.value - 1 : state.value;
});
const Results = computed(() => {
	return props.total ? localeLang("secondCategory.results").replace("xxxx", props.total) : "";
});

const emit = defineEmits();

const websiteStore = useWebsiteStore();
// const { website } = storeToRefs(websiteStore);

//	开关切换
const checkbox = () => {
	emit("change");
};
/**
 *
 * @param obj {selectedItem: {narrow: {narrowId: number}}, selectedIndex: number}
 * @description 选中选项后触发 点击跳转新url路由 重新更新试图
 * @returns {Promise<void>}
 */
const changeOption = async (obj: ChangeOptionProps): Promise<void> => {
	const narrowFilterId = obj.option_id;
	const newRouterQuery = JSON.parse(JSON.stringify(route.query));
	console.log(obj.selectedItem.narrow[obj.selectedIndex], 22222);
	const matchKeys = obj.selectedItem.narrow[obj.selectedIndex].narrowId.toString();
	if (filterVals.value.includes(+matchKeys)) {
		if (newRouterQuery[narrowFilterId].split(",").length > 1) {
			newRouterQuery[narrowFilterId] = newRouterQuery[narrowFilterId]
				.split(",")
				.filter((item: string) => item !== matchKeys)
				.join(",");
		} else {
			delete newRouterQuery[narrowFilterId];
		}
		// 重置分页到第一页
		newRouterQuery.page && delete newRouterQuery.page;
		await navigateTo({ path: route.path, query: newRouterQuery });
	} else {
		if (filterKeys.value.includes(narrowFilterId)) {
			newRouterQuery[narrowFilterId] = newRouterQuery[narrowFilterId] + "," + matchKeys;
		} else {
			newRouterQuery[narrowFilterId] = matchKeys;
		}
		// 重置分页到第一页
		newRouterQuery.page && delete newRouterQuery.page;
	}
	await navigateTo({ path: route.path, query: newRouterQuery });
	gaEventNarrow(obj);
};

const categoriesId = computed(() => {
	const str = String(route.params.id);
	const last = route.params.id.lastIndexOf("-");
	return str.substring(last + 1);
});

const gaEventNarrow = (obj: any) => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: `productListPage_${categoriesId.value}`,
			eventAction: "productlist_filter",
			eventLabel: `${obj.selectedItem.en_title}_${obj.selectedItem.narrow[obj.selectedIndex].narrowId.toString()}`,
			nonInteraction: false
		});
	}
};

const removeAll = () => {
	if (props.narrow?.length) {
		navigateTo({ replace: true, query: {} });
	} else {
		router.go(-1);
	}
};
/**
 * @param type {list | grid}
 * @description 切换卡片类型
 * @returns {void}
 */
const changeListType = (type: CardType): void => {
	injectState.displayType.value = type;
};
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
