.filterSelectMenu {
	display: flex;
	justify-content: space-between;
	@include width1420;

	@media (min-width: 768px) and (max-width: 1024px) {
		width: 100%;
	}

	margin: 20px auto;

	&.noNarrow {
		display: flex;
		justify-content: flex-end;
	}

	&__list {
		display: flex;
		align-items: center;

		:deep(.filterSelect__input) {
			height: 32px;
			border-radius: 999px;
			display: flex;
		}
	}

	&__clear {
		display: flex;
		align-items: center;
		cursor: pointer;
		color: $textColor2;
		font-size: 12px;

		.fs-button {
			font-size: 12px;

			i {
				font-size: 12px;
			}
		}

		// span {
		//   white-space: nowrap;

		//   &:hover {
		//     text-decoration: underline;
		//   }
		// }

		// i {
		//   color: $textColor2;
		//   margin-left: 4px;
		//   transform: scale(0.5);
		// }
	}

	&__type {
		display: flex;
		align-items: center;

		.com_switch {
			display: flex;
			align-items: center;
			margin-right: 20px;

			.title {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				margin-right: 8px;
			}

			.content {
				width: 44px;
				height: 24px;
				padding: 2px;
				border-radius: 999px;
				background-color: $bgColor7;
				cursor: pointer;
				transition: all 0.2s;
				position: relative;

				.btn {
					width: 20px;
					height: 20px;
					background-color: $bgColor6;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: absolute;
					top: 2px;
					left: 2px;

					.iconfont {
						display: block;
						font-size: 12px;
						line-height: 1;
						color: $textColor2;
						opacity: 0;
						transition: all 0.2s;
					}
				}

				&.isOpen {
					background-color: $bgColor8;

					.btn {
						left: 22px;

						.iconfont {
							opacity: 1;
						}
					}
				}
			}
		}

		&__total {
			box-sizing: content-box;
			color: $textColor2;
			@include font12;
			margin-right: 12px;
			white-space: nowrap;
		}

		:deep(.filterSelectType__input) {
			border-radius: 999px;
		}

		:deep(.fs-input) {
			margin-right: 20px;
			max-width: 148px;

			.fs-input__inner {
				font-weight: 600;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;

				&::placeholder {
					color: $textColor1;
				}
			}
		}

		&__display {
			display: flex;
			align-items: center;
			margin-left: 20px;

			i {
				display: inline-block;
				font-size: 20px;
				width: 32px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				color: $textColor2;
				cursor: pointer;
				border-radius: 3px;

				&:hover {
					color: $textColor1;
					background: rgba(0, 0, 0, 0.04);
				}
			}

			&--active {
				color: $textColor1 !important;
			}
		}
	}
}

.comparisonNav {
	padding: 10px 0;
}
