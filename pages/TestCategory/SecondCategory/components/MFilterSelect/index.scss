.mFilterSelectMenu {
	&__head {
		i {
			font-size: 20px;
		}
	}

	&__category {
		:deep(.fs-radio) {
			.fs-radio-box {
				.fs-radio__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}

				.fs-radio__label {
					color: $textColor2;
				}
			}

			&.is-checked {
				.fs-radio__icon {
					color: $textColor2;
				}

				.fs-radio__label {
					color: $textColor1;
				}
			}
		}
	}

	&__narrow {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-column-gap: 24px;

		:deep(.fs-checkbox) {
			width: 100%;
			margin-right: 0;
			@include font14;
			height: auto;
			padding: 8px 0;

			.fs-checkbox-box {
				width: 100%;

				.fs-checkbox__label {
					color: $textColor2;
					@include font14;
					max-width: 147px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.fs-checkbox__icon {
					line-height: 1;

					&::before {
						display: block;
					}
				}
			}

			&.is-checked {
				.fs-checkbox__icon {
					color: $textColor2;
				}

				.fs-checkbox__label {
					color: $textColor1;
				}
			}

			&.is-disabled {
				.fs-checkbox__label {
					color: $textColor4;
				}
			}
		}

		.special {
			width: fit-content !important;

			:deep(.fs-checkbox-box) {
				width: 100% !important;

				.fs-checkbox__label {
					max-width: 100% !important;
				}
			}
		}
	}

	&__list {
		&__item {
			margin-top: 10px;
			margin-bottom: -8px;

			&:first-of-type {
				margin-top: 0;
			}
		}

		&__title {
			@include font14;
			padding: 6px 0;
			font-weight: 600;
		}

		&__content {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-column-gap: 24px;

			:deep(.fs-checkbox) {
				width: 100%;
				margin-right: 0;
				@include font14;
				height: auto;
				padding: 8px 0;

				.fs-checkbox-box {
					width: 100%;

					.fs-checkbox__label {
						color: $textColor2;
						@include font14;
						max-width: 147px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.fs-checkbox__icon {
						line-height: 1;

						&::before {
							display: block;
						}
					}
				}

				&.is-checked {
					.fs-checkbox__icon {
						color: $textColor2;
					}

					.fs-checkbox__label {
						color: $textColor1;
					}
				}

				&.is-disabled {
					.fs-checkbox__label {
						color: $textColor4;
					}
				}
			}

			.special {
				width: fit-content !important;

				:deep(.fs-checkbox-box) {
					width: 100% !important;

					.fs-checkbox__label {
						max-width: 100% !important;
					}
				}
			}
		}
	}

	&__footer {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
	}
}

:deep(.fs-radio) {
	width: 100%;

	.fs-radio-box {
		width: 100%;
		height: 100%;
	}
}

.fs-button--gray {
	color: $textColor2;
}
