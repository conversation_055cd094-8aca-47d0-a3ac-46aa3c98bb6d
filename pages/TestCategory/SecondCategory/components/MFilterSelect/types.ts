import type { CrumbsProps, CategoryFilterItemProps, CategoryFilterProps } from "../../types";
import type { NarrowProps } from "../FilterSelectMenu/types";
export type showType = "category" | "narrow" | "filter";
export interface MCategoryItemProps extends CategoryFilterItemProps {
	checked?: boolean;
}

export interface MCategoryProps extends CategoryFilterProps {
	category: MCategoryItemProps[];
}

export interface MFilterSelectProps {
	showType?: showType;
	modelValue: boolean;
	narrrow: NarrowProps[];
	options: any;
	crumbs: CrumbsProps[];
	showNum: number;
}
export interface MFilterSelectEmits {
	(e: "submit", status: boolean, value: object): void;
	(e: "update:modelValue", value: boolean): void;
}

export interface MFilterSelectTypeProps {
	options: MCategoryItemProps[];
	modelValue: boolean;
}
