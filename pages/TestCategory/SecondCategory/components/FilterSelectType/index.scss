.filterSelectType__input {
	max-width: 160px;
	padding: 4px 16px;
	font-size: 14px;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
	user-select: none;
	border: 1px solid #dee0e3;

	&__sortBy {
		display: none;
	}

	&__sortByPc {
		display: block;
		color: $textColor1;

		@include mobile {
			display: none;
		}
	}

	&__sortByCheck {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		color: $textColor1;
	}

	@include mobile {
		display: flex;
		align-items: center;
		padding: 0;
		max-width: 100%;
		font-size: 12px;
		border: none;
		background-color: transparent;

		i {
			font-size: 12px;
		}

		&__sortBy {
			display: block;
			margin-right: 3px;
		}
	}

	&__content {
		display: flex;
		align-items: center;
		@include font12;
		margin: 0 4px;
		color: $textColor1;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;

		@include mobile {
			color: $textColor1;
			font-weight: 600;
		}
	}

	i {
		font-size: 14px;
		color: $textColor1;
	}

	&__list {
		display: flex;
		flex-direction: column;
		max-width: 214px;
		border-radius: 8px;
		padding: 8px 0;

		&__sortBy {
			padding: 8px 16px;
			color: $textColor2;
			cursor: pointer;
			font-size: 12px;
			position: relative;

			span {
				margin-left: 16px;
			}

			&:hover {
				background: #f6f6f8;
				color: $textColor1;
			}

			i {
				position: absolute;
				color: $textColor1;
				font-size: 8px;
				top: 50%;
				transform: translateY(-50%);
				left: 16px;
			}

			&.active {
				color: $textColor1;

				i {
					color: $textColor1;
				}
			}
		}

		:deep(.fs-radio) {
			margin-right: 0px;
			padding: 8px;
			@include font12;

			&:hover {
				background-color: #f7f7f7;

				.fs-radio__icon {
					color: $textColor2;
				}

				.fs-radio__label {
					color: $textColor1;
				}
			}

			.fs-radio__label {
				@include font12;
				color: $textColor2;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				max-width: 166px;
			}

			.fs-radio__icon {
				line-height: 1;

				&::before {
					display: block;
				}
			}

			&.is-checked {
				.fs-radio__icon {
					color: $textColor2;
				}

				.fs-radio__label {
					color: $textColor1;
				}
			}
		}
	}

	&__active {
		border-color: #19191a;

		.filterSelectType__input__icon {
			.filterSelectType__input__arrow {
				transform: rotateX(180deg);
				color: $textColor1;
				transition: all 0.3s ease;
			}
		}

		.filterSelectType__input__sortByPc {
			color: $textColor1;
		}
	}
}

.filterSelectType__input__icon {
	padding: 4px;
	display: flex;
	align-items: center;
	justify-content: center;

	.iconfont {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 14px;
		height: 14px;
	}

	.filterSelectType__input__arrow {
		color: $textColor2;
	}

	@include mobile {
		padding: 0;
	}
}

.filterSelectType__input__prefixIcon {
	color: $textColor1;
}

:deep(.fs-tooltip__popper) {
	background-color: #19191a;
}
