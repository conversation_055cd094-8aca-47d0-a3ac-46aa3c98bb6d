.filterSelectMenu {
	display: flex;
	justify-content: space-between;
	@include width1200;

	@media (min-width: 768px) and (max-width: 1024px) {
		width: 100vw;
	}

	margin: 20px auto;

	&.noNarrow {
		display: flex;
		justify-content: flex-end;
		position: relative;
	}

	&__list {
		display: flex;
		align-items: center;
		overflow: auto;
		margin-right: 100px;
		background-color: #f6f6f8;
		border-radius: 9999px;

		&::-webkit-scrollbar {
			display: none;
		}

		:deep(.filterSelect__input) {
			height: 40px;
			border-radius: 999px;
			display: flex;
		}

		&__narrow {
			background-color: #f6f6f8;
			border-radius: 9999px;
			padding: 4px;
			font-size: 12px;
			display: flex;
			align-items: center;
			// justify-content: center;
			color: $textColor2;
			cursor: pointer;
			white-space: nowrap;
		}

		&__all {
			padding: 4px 8px;
			margin-right: 4px;

			span {
				display: inline-block;
				line-height: 20px;
			}

			&:hover {
				background-color: #fff;
				border-radius: 9999px;
			}
		}

		&__item {
			padding: 4px 8px;
			margin-right: 4px;

			span {
				display: inline-block;
				line-height: 20px;
			}

			&:last-of-type {
				margin-right: 0;
			}

			&:hover {
				background-color: #fff;
				border-radius: 9999px;
			}
		}

		.afterShadow {
			position: absolute;
			top: 0px;
			right: 25px;
			width: 36px;
			height: 36px;
			z-index: 2;
			background: linear-gradient(270deg, #f6f6f8 36%, rgba(246, 246, 248, 0.6) 72%, rgba(246, 246, 248, 0) 98%);
		}
	}

	&__clear {
		display: flex;
		align-items: center;
		cursor: pointer;
		color: $textColor2;
		@include font12;

		span {
			white-space: nowrap;

			&:hover {
				text-decoration: underline;
			}
		}

		i {
			color: $textColor2;
			margin-left: 4px;
			transform: scale(0.5);
		}
	}

	&__type {
		display: flex;
		align-items: center;

		.com_switch {
			display: flex;
			align-items: center;
			margin-right: 20px;

			.title {
				@include font12;
				font-weight: 400;
				color: $textColor2;
				margin-right: 8px;
			}

			.content {
				width: 44px;
				height: 24px;
				padding: 2px;
				border-radius: 999px;
				background-color: $bgColor7;
				cursor: pointer;
				transition: all 0.2s;
				position: relative;

				.btn {
					width: 20px;
					height: 20px;
					background-color: $bgColor6;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: absolute;
					top: 2px;
					left: 2px;

					.iconfont {
						display: block;
						font-size: 12px;
						line-height: 1;
						color: $textColor2;
						opacity: 0;
						transition: all 0.2s;
					}
				}

				&.isOpen {
					background-color: $bgColor8;

					.btn {
						left: 22px;

						.iconfont {
							opacity: 1;
						}
					}
				}
			}
		}

		&__total {
			box-sizing: content-box;
			color: $textColor2;
			@include font12;
			margin-right: 20px;
			white-space: nowrap;
		}

		:deep(.filterSelectType__input) {
			border-radius: 999px;
		}

		:deep(.fs-input) {
			margin-right: 20px;
			max-width: 148px;

			.fs-input__inner {
				font-weight: 600;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;

				&::placeholder {
					color: $textColor1;
				}
			}
		}

		&__display {
			display: flex;
			align-items: center;
			margin-left: 20px;

			i {
				display: inline-block;
				font-size: 20px;
				width: 32px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				color: $textColor2;
				cursor: pointer;
				border-radius: 3px;

				&:hover {
					color: $textColor1;
					background: rgba(0, 0, 0, 0.04);
				}
			}

			&--active {
				color: $textColor1 !important;
			}
		}
	}
}

.filterSelectMenu__type__space {
	margin-right: 12px;
}

.comparisonNav {
	padding: 10px 0;
}

.isAct {
	background-color: #ffffff;
	color: $textColor1;
	border-radius: 9999px;
}

.notClick {
	cursor: not-allowed;
	span {
		color: #ccc;
	}
	&:hover {
		background-color: #f6f6f8;
	}
}
