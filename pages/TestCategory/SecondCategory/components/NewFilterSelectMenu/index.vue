<template>
	<div class="filterSelectMenu" :class="{ noNarrow: narrow.length === 0 }">
		<div v-if="narrow?.length" ref="allWidth" class="filterSelectMenu__list">
			<!-- <div v-if="isMaskLeft" class="container__beforeBtn" @click="scrollTabs('left')">
				<i class="iconfont">&#xe702;</i>
			</div> -->
			<div v-if="isFourthLevelDisplay" class="filterSelectMenu__list__narrow">
				<div ref="all" class="filterSelectMenu__list__all" :class="{ isAct: activeId === 0 }" @click="changeFilter(99)">
					<span>{{ localeLang("secondCategory.all") }} ({{ allCount }})</span>
				</div>
				<template v-for="(v, i) in firstNarrow" :key="v.narrowId">
					<div :ref="setRefs(i)" class="filterSelectMenu__list__item" :class="{ isAct: activeId === v.narrowId, notClick: v.count === 0 }" @click="changeFilter(i, v.count)">
						<span>{{ v.name }} ({{ v.count }})</span>
					</div>
				</template>
			</div>
		</div>
		<div class="filterSelectMenu__type">
			<span class="filterSelectMenu__type__space"><FilterSelectType /></span>
			<!-- <NewFilterSelect :options="narrowMapData" @changeOption="changeOption" /> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import NewFilterSelect from "../NewFilterSelect/index.vue";
import FilterSelectType from "../FilterSelectType/index.vue";
import useRouteInfo from "../../hooks/useRouteInfo";
import type { FilterSelectMenuProps, ChangeOptionProps } from "./types";
const props = defineProps<FilterSelectMenuProps>();
const route = useRoute();
const activeId = ref(0);
const { filterKeys, filterVals } = useRouteInfo();
const localeLang = useLocaleLang();
const refs: any[] = [];
const all = ref();
const allWidth = ref();
const isMaskRight = ref(false);
// 是否平铺
const isFourthLevelDisplay = computed(() => props.narrow?.[0]?.isFourthLevelDisplay === 1);
// const isFourthLevelDisplayAllCount = computed(() => {
// 	return isFourthLevelDisplay.value && props.narrow?.[0].narrow.reduce((total, item) => (total += item.count), 0);
// });
const narrowMapData = computed(() => {
	return isFourthLevelDisplay.value ? props.narrow.slice(1) : props.narrow;
});
const firstNarrow = computed(() => props.narrow?.[0]?.narrow);
/**
 *
 * @param obj {selectedItem: {narrow: {narrowId: number}}, selectedIndex: number}
 * @description 选中选项后触发 点击跳转新url路由 重新更新试图
 * @returns {Promise<void>}
 */
const changeOption = async (obj: ChangeOptionProps): Promise<void> => {
	const narrowFilterId = obj.option_id;
	const newRouterQuery = JSON.parse(JSON.stringify(route.query));
	const matchKeys = obj.selectedItem.narrow[obj.selectedIndex].narrowId.toString();
	if (filterVals.value.includes(+matchKeys)) {
		if (newRouterQuery[narrowFilterId].split(",").length > 1) {
			newRouterQuery[narrowFilterId] = newRouterQuery[narrowFilterId]
				.split(",")
				.filter((item: string) => item !== matchKeys)
				.join(",");
		} else {
			delete newRouterQuery[narrowFilterId];
		}
		newRouterQuery.page && delete newRouterQuery.page;
		await navigateTo({ path: route.path, query: newRouterQuery });
	} else {
		if (filterKeys.value.includes(narrowFilterId)) {
			newRouterQuery[narrowFilterId] = newRouterQuery[narrowFilterId] + "," + matchKeys;
		} else {
			newRouterQuery[narrowFilterId] = matchKeys;
		}
	}
	newRouterQuery.page && delete newRouterQuery.page;
	await navigateTo({ path: route.path, query: newRouterQuery });
	gaEventNarrow(obj);
};

const changeFilter = async (val: number, count?: number) => {
	if (count && count === 0) return;
	const narrowFilterId = props.narrow?.[0].productsNarrowByOptionsId;
	const newRouterQuery = JSON.parse(JSON.stringify(route.query));
	newRouterQuery.page = 1;
	if (val === 99) {
		activeId.value = 0;
		delete newRouterQuery[narrowFilterId];
		await navigateTo({ path: route.path, query: newRouterQuery });
	} else {
		const currentNarrow = props.narrow?.[0].narrow?.[val];
		if (activeId.value === currentNarrow.narrowId || currentNarrow.count === 0) return;
		const matchKeys = currentNarrow.narrowId;
		activeId.value = currentNarrow.narrowId;
		newRouterQuery[narrowFilterId] = matchKeys;
		await navigateTo({ path: route.path, query: newRouterQuery });
	}
};

// 计算方法
const getDom = async (): Promise<void> => {
	let domArr: number[] = []; // 筛选项集合
	await nextTick();
	if (!refs?.length) return;
	if (!domArr.length) {
		domArr = refs.map((dom: { offsetWidth: number }) => dom.offsetWidth + 4);
	}
	let sum = 0;
	for (let i = 0; i < domArr.length; i++) {
		sum += domArr[i];
		if (sum + all.value.offsetWidth >= allWidth.value.offsetWidth && i !== 0) {
			isMaskRight.value = true;
			break;
		}
	}
};

const categoriesId = computed(() => {
	const str = String(route.params.id);
	const last = route.params.id.lastIndexOf("-");
	return str.substring(last + 1);
});

const gaEventNarrow = (obj: any) => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: `productListPage_${categoriesId.value}`,
			eventAction: "productlist_filter",
			eventLabel: `${obj.selectedItem.enTitle}_${obj.selectedItem.narrow[obj.selectedIndex].narrowId.toString()}`,
			nonInteraction: false
		});
	}
};

/**
 * @param {number} index
 * @description: 设置每个dom元素的refs
 * @return {(el: any) => void}
 */
const setRefs = (index: { toString: () => any }): ((el: any) => void) => {
	const refValue = toRef(refs, index.toString());
	return (el: any) => {
		refValue.value = el;
	};
};

onMounted(() => {
	getDom();
	console.log(props);
});
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
