.grid {
	padding: 20px;
	position: relative;

	// 图片
	&__img {
		position: relative;
		margin: 0 auto;
		margin-bottom: 12px;

		img {
			width: 180px;
			height: 180px;
			cursor: pointer;
			display: block;
			margin: 0 auto;
			mix-blend-mode: multiply;
		}

		//  购物车
		&__cart {
			// display: none;
			visibility: hidden;

			@include pad {
				visibility: visible;
				// display: block;
			}

			.fs-button {
				padding: 6px 8px;
				font-size: 12px;
				transition: none;

				.iconfont {
					color: #fff;
				}

				&::before {
					transition: none;
				}

				span {
					// padding: 2px;
				}

				@include mobile {
					height: 24px;
					width: 24px;

					::deep(.fs-button--prefix) {
						display: none;
					}

					i {
						font-size: 12px;
					}

					span {
						padding: 0;
					}
				}
			}

			&.round {
				z-index: 1;
				text-align: center;
				color: $textColor1;
				// bottom: 12px;
				// right: 24px;
				box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
				background-color: #fff;
				cursor: pointer;

				@include mobile {
					position: absolute;
				}

				&:hover {
					background: rgba(25, 25, 26, 0.04);

					:deep(span) {
						position: relative;
						opacity: 1;
					}
				}
			}

			i {
				font-size: 14px;
				width: 14px;
				height: 14px;
				display: flex;
				align-items: center;
				justify-content: center;
				line-height: 1;
				color: $textColor1;
			}

			@include mobile {
				// display: block;
				visibility: visible;

				&.round {
					background: none;
					box-shadow: none;
					height: 24px;
					width: 24px;
					top: 148px;
					right: 20px;
				}

				i {
					width: 14px;
					height: 12px;
				}
			}

			&__hidden {
				visibility: hidden;
			}

			&__loading {
				visibility: hidden;
				margin-right: 4px;
			}

			&__hover {
				position: relative;
				opacity: 1;
				z-index: 1;
			}
		}
	}

	// 标题
	&__title {
		font-style: normal;
		font-weight: 600;
		font-size: 14px;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin-bottom: 10px;
		overflow: hidden;
		line-height: 22px;
		cursor: pointer;
		color: $textColor1;

		&:hover {
			text-decoration: underline;
		}
	}

	// 复选框
	:deep(.fs-checkbox) {
		margin: 0;
		height: auto;
		position: absolute;
		top: 10px;
		right: 10px;

		.fs-checkbox-box {
			.fs-checkbox__icon {
				font-size: 20px;
				line-height: 1;
			}

			.fs-checkbox__label {
				display: none;
			}
		}

		.is-checked {
			.fs-checkbox-box {
				.fs-checkbox__icon {
					color: $textColor2;
				}
			}
		}

		&:hover {
			.fs-checkbox-box {
				.fs-checkbox__icon {
					color: $textColor2;
				}
			}
		}
	}

	//标签
	&__label {
		height: 18px;
		@include font12;
		margin-bottom: 10px;
		overflow: hidden;
		box-sizing: border-box;
		cursor: auto;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		color: $textColor2;
		line-height: 18px;

		:deep(.fs-divider) {
			height: 10px;
			border-color: #ccc;
			margin-bottom: 2px;
			margin: 0 4px;

			&:last-child {
				display: none;
			}
		}
	}

	// 亮点
	&__highlight {
		margin-bottom: 12px;

		> div {
			position: relative;
			padding-left: 12px;
			margin-bottom: 4px;

			> i {
				width: 4px;
				height: 4px;
				display: block;
				border-radius: 50%;
				background: $textColor2;
				position: absolute;
				left: 0;
				top: 7px;
			}

			> span {
				display: block;
				@include font12;
				color: $textColor3;
				word-break: break-word;
			}

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	//价格
	&__price {
		display: flex;
		align-items: baseline;
		// margin-left: 8px;
		// flex-direction: column;
		// flex-wrap: wrap;
		margin-bottom: 10px;

		@include mobile {
			align-items: flex-start;
			flex-direction: column;
		}

		&__normal {
			color: $textColor1;
			@include font16;
			font-weight: 600;
		}

		&__tax {
			color: $textColor1;
			@include font16;
			font-weight: 600;
		}

		&__taxProductsPrice {
			color: $textColor2;
			@include font12;
			font-weight: normal;
			margin-left: 8px;

			@include mobile {
				margin-left: 0;
			}
		}

		.box__tax__popper {
			margin-left: 4px;
		}

		&__taxPriceTips {
			display: flex;
			align-items: center;
		}
	}

	// 库存 交期
	&__inventory {
		// min-height: 44px;
		margin-bottom: 10px;

		span {
			color: $textColor2;
			@include font12;
			display: block;
			width: 100%;
			white-space: nowrap;
			text-overflow: ellipsis;
			-webkit-line-clamp: 1;
			overflow: hidden;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	@include mobile {
		:deep(.fs-button--prefix) {
			margin: 0;
		}
	}

	//销量 频率
	&__solidReview {
		// display: inline-block;
		// align-items: center;
		// white-space: nowrap;
		@include font12;
		margin-right: 8px;
		white-space: nowrap; // 添加这行确保文本在一行显示
		// overflow: hidden; // 添加这行处理溢出
		text-overflow: ellipsis;

		a {
			color: $textColor2;
			display: flex;
		}

		&__solid {
			&__text {
				color: #a65300;
			}
		}

		&__mobile {
			text-overflow: initial;
		}

		s {
			border-right: 1px solid $borderColor1;
			height: 10px;
			margin: 0 8px;
		}

		&__review {
			&__text {
				color: $textColor5;
			}
		}

		&__solid,
		&__review {
			display: inline-block;

			&:hover {
				cursor: pointer;
				text-decoration: underline;
			}
		}

		&__solidZero,
		&__reviewZero {
			display: none;
		}

		&__dividerZero {
			display: none !important;
		}

		.websiteIcon {
			.iconfont {
				color: $textColor2;
				margin-right: 4px;
			}
		}

		.websiteSolid {
			span {
				color: #a65300;
			}
		}
	}
}

.grid:hover {
	.grid__solidReview {
		overflow: hidden;
	}
}
