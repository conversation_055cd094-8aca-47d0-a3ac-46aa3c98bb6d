import type { CategoryProductProps, newSourceProps } from "../../types";
export interface ICategory {
	info: CategoryProductProps;
	inventory?: Array<newSourceProps>;
	cardIndex: number;
	type: "pc" | "mobile";
	categoriesId?: number;
	source?: "default" | "comparison";
	sticky?: boolean;
}

export interface ProductTextMap {
	[key: number]: string; // 键是 number 类型
}

export interface GridCardEmits {
	(e: "add", id: number): void;
	(e: "change", data: object): void;
	(e: "check", id: number): void;
}
