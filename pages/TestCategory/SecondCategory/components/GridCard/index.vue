<template>
	<div v-loading="loading" class="grid" @mouseenter="loadingEnter = true" @mouseleave="loadingEnter = false">
		<div class="grid__img">
			<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)">
				<img :src="info?.image" :alt="info.productsName" />
			</a>
		</div>
		<TextTag :tagType="info.isLabel" :tagLabel="info.label" :styles="tagStyle" />
		<BrandColor v-if="deviceStatus" v-model="loading" :categoryProduct="info" :cardIndex="cardIndex" />
		<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)">
			<h3 :title="info.productsName" class="grid__title" @click="gaEventTitle">{{ info.productsName }}</h3>
		</a>
		<div v-if="info?.productsTag" class="grid__label" :title="info.productsTag">
			<template v-for="(st, si) in tagFormat(info.productsTag)" :key="si">
				<span>{{ st }}</span
				><FsDivider direction="vertical"></FsDivider>
			</template>
		</div>
		<div v-if="!info?.isInquiry" class="grid__price">
			<!-- 含税价 -->
			<div :class="{ grid__price__taxPriceTips: info?.taxPriceTips }">
				<div v-if="info?.taxProductsPrice" class="grid__price__tax" v-html="trimString(info.taxProductsPrice)"></div>
				<template v-if="info?.isShowTax && info?.taxPriceTips">
					<FsTooltip class="box__tax__popper" :content="info?.taxPriceTips" placement="top">
						<i class="iconfont iconxinxi_info" />
					</FsTooltip>
				</template>
			</div>
			<!-- 不含税价 -->
			<div
				v-if="info?.productsPrice"
				class="grid__price__normal"
				:class="{ grid__price__taxProductsPrice: info?.productsPrice && info?.taxProductsPrice }"
				v-html="trimString(info.productsPrice)"></div>
		</div>
		<div v-if="info?.serverHighlights && info?.serverHighlights?.length" class="grid__highlight">
			<div v-for="(item, index) in info.serverHighlights" :key="index">
				<i></i>
				<span>{{ item }}</span>
			</div>
		</div>
		<div v-if="info?.isVirtualProduct" class="grid__inventory">
			<span :title="inventory?.[0]?.delivery">{{ localeLang("secondCategory.in_stock") }}{{ inventory?.length ? `, ${inventory[0]?.delivery}` : "" }}</span>
		</div>
		<div v-else-if="inventory?.length" class="grid__inventory">
			<span v-for="(v, i) in inventory" :key="i" :title="v.description">
				{{ v.description }}
			</span>
		</div>
		<div ref="solidReviewRef" class="grid__solidReviewBox" style="display: flex; align-items: center; justify-content: space-between">
			<div class="grid__solidReview" :class="{ grid__solidReview__mobile: isMobile }" :style="{ ...solidViewMax }">
				<div class="grid__solidReview__solid" :class="{ grid__solidReview__solidZero: strSplit(info.productsSales)[0].toString() === '0' }">
					<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)">
						<span class="grid__solidReview__solid__text"> {{ strSplit(info.productsSales)[0].toString() ?? 0 }}&nbsp; </span>
						<span> {{ strSplit(info.productsSales)[1] }} </span>
					</a>
				</div>
				<FsDivider
					direction="vertical"
					:class="{
						grid__solidReview__dividerZero: strSplit(info.productsSales)[0].toString() === '0' || strSplit(info.productsReviews)[0].toString() === '0'
					}"></FsDivider>
				<div class="grid__solidReview__review" :class="{ grid__solidReview__reviewZero: strSplit(info.productsReviews)[0].toString() === '0' }">
					<a :href="localeLink(`/products/${info.productsId}.html?now_cid=${categoriesId}`)">
						<span class="grid__solidReview__review__text"> {{ strSplit(info.productsReviews)[0].toString() ?? 0 }}&nbsp; </span>
						<span> {{ strSplit(info.productsReviews)[1] }} </span>
					</a>
				</div>
			</div>
			<div v-if="!isMobile" ref="round" class="grid__img__cart round">
				<FsButton iconPlacement="prefix" type="red" size="small" :loading="addButtonType && addButtonId === info.productsId" @click="changeCartStatus(info.productsId, info)">
					<template v-if="!isMobile">
						<span :class="{ grid__img__cart__hidden: addButtonType && addButtonId === info.productsId }"> {{ buttonText }}</span></template
					>
					<template #icon>
						<span class="grid__img__cart__hover" :class="{ grid__img__cart__loading: addButtonType && addButtonId === info.productsId }">
							<i v-if="info?.isVirtualProduct && info?.isInquiry" class="iconfont">&#xe675;</i>
							<i v-else class="iconfont">&#xe64c;</i>
						</span>
					</template>
				</FsButton>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip, FsDivider, FsButton } from "fs-design";
import TextTag from "../TextTag/index.vue";
import BrandColor from "../BrandColor/index.vue";
import { trimString } from "../../../utils";
import type { CategoryContextKey } from "../../types";
import type { ICategory, GridCardEmits, ProductTextMap } from "./types";
const props = defineProps<ICategory>();
const loading = ref(false);
const round = ref();
const solidReviewRef = ref();
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const emits = defineEmits<GridCardEmits>();
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile); // 修正拼写错误 isMobile -> isMobile
const tagStyle = { position: "absolute", top: "20px", left: "20px" };
const deviceStatus = ref();
const localeLink = useLocaleLink();
const localeLang = useLocaleLang();
const loadingEnter = ref(false);
const addButtonType = computed(() => injectState.addCartLoading.value);
const addButtonId = computed(() => injectState.addCartId.value);

const buttonText = computed(() => {
	const productTextMap: ProductTextMap = {
		195659: "secondCategory.start_trial",
		231981: "secondCategory.start_trial",
		258683: "secondCategory.start_trial",
		282455: "secondCategory.start_trial",
		196175: "secondCategory.launch_demo",
		178410: "secondCategory.launch_demo",
		229069: "secondCategory.download"
	};
	return localeLang(productTextMap[props.info.productsId] || "formValidate.form.add");
});
/**
 * 改变购物车状态
 * @param id 商品id
 * @description 点击展示购物车弹窗 provide 提供公共状态
 * @returns void
 */
const changeCartStatus = (id: number, info: any) => {
	if (info.isVirtualProduct || info.isCustomized === 1 || info.isInquiry === 1 || info.isCustomServer === 1 || info.isCustomServer === 2) {
		location.href = localeLink(`/products/${info.productsId}.html`);
		return;
	}
	injectState.addCartId.value = id;
	injectState.addCartStatus.value = true;
	injectState.cartAnimationstartElement.value = props.cardIndex;
};

const tagFormat = (str: string) => {
	let newStr = [];
	if (str.includes("丨")) {
		newStr = str.split("丨");
	} else {
		newStr = str.split(" | ");
	}
	return newStr;
};

const gaEventTitle = () => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: `Product List Page_${props.categoriesId}`,
			eventAction: "select_item",
			eventLabel: `text_${props.info.productsName}_${props.info.productsId}`,
			nonInteraction: false
		});
	}
};

const solidViewMax = computed(() => {
	return loadingEnter ? { "max-width": solidReviewRef?.value?.offsetWidth - round?.value?.offsetWidth - 8 + "px" } : { "max-width": "100%", "over-flow": "auto" };
});

watch(
	isMobile,
	newVal => {
		deviceStatus.value = newVal ? props.type === "mobile" : props.type === "pc";
	},
	{
		immediate: true
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
