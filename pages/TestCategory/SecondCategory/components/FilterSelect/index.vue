<template>
	<div class="filterSelect" :class="{ filterSelectActive: filterSelectActive }">
		<FsTooltip
			ref="tooltip"
			:showArrow="false"
			placement="bottom-start"
			transition="fade"
			:offsetY="8"
			:popperContentStyle="popperContentStyle"
			@visible-change="handleVisibleChange">
			<div class="filterSelect__input" :class="{ filterSelect__input__active: visible }">
				<p :title="Array.isArray(dataSource) ? '' : dataSource.title" :class="{ active: titleNum }">
					<span class="filterSelect__input__title">
						<span :class="{ filterSelect__input__title__decoration: titleNum }">{{ title2 }}</span>
						<span v-if="titleNum">{{ `&nbsp;${titleNum}` }}</span>
					</span>
					<i v-if="Array.isArray(dataSource)" class="iconfont iconfont_more">&#xe739;</i>
					<i v-else class="iconfont iconfont_down">&#xe704;</i>
				</p>
			</div>

			<template #content>
				<template v-if="Array.isArray(dataSource)">
					<ul>
						<template v-for="(j, a) in dataSource" :key="a">
							<li class="filterSelect__list__item">
								<div class="filterSelect__list__title">{{ j.title }}</div>
								<div class="filterSelect__list__content">
									<template v-for="(k, b) in j.narrow" :key="b">
										<FsCheckbox v-model="k.checked" class="filterSelect__list__checkbox" :disabled="k.disabled" :title="k.name" size="small" @change="handleChange(j, b)">
											{{ k.name }}
										</FsCheckbox>
									</template>
								</div>
							</li>
						</template>
					</ul>
				</template>
				<template v-else>
					<ul :class="wrapperNoramlList">
						<li v-for="(v, c) in dataSource?.narrow" :key="c" :class="{ special: dataSource?.enTitle === 'Inventory' }" :title="v.name">
							<FsCheckbox v-model="v.checked" :disabled="v.disabled" size="small" @change="handleChange(dataSource, c)">
								{{ v.name }}
							</FsCheckbox>
						</li>
					</ul>
				</template>
			</template>
		</FsTooltip>
	</div>
</template>

<script setup lang="ts">
import { FsTooltip, FsCheckbox } from "fs-design";
import type { FsTooltipInstance } from "fs-design";
import useRouteInfo from "../../hooks/useRouteInfo";
import type { NarrowProps, NarrowItemProps } from "../FilterSelectMenu/types";
import type { FilterSelectProps, FilterSelectEmits } from "./types";
import { reMapDataIsCheck } from "~/pages/Category/utils";
const props = defineProps<FilterSelectProps>();
const visible = ref(false);
const tooltip = ref<FsTooltipInstance>();
const localeLang = useLocaleLang();
const emits = defineEmits<FilterSelectEmits>();
const { filterVals } = useRouteInfo();
const dataSource = ref(Array.isArray(props.options) ? (reMapDataIsCheck(props.options) as NarrowProps[]) : (reMapDataIsCheck(props.options) as NarrowProps));

const popperContentStyle = {
	padding: "8px 0",
	"max-width": "320px",
	"max-height": "428px",
	"overflow-x": "hidden",
	"overflow-y": "auto",
	"border-radius": "8px",
	"z-index": "109"
};

const wrapperNoramlList = computed(() => {
	return [
		"filterSelect__list",
		!Array.isArray(dataSource.value) && dataSource.value.narrow.length <= 6 && "filterSelect__list__ones",
		!Array.isArray(dataSource.value) && dataSource.value?.enTitle === "Inventory" && "filterSelect__list__specialWidth"
		// !Array.isArray(dataSource.value) && dataSource.value.narrow.length < 3 && "filterSelect__list__twos"
	].join(" ");
});

/**
 *
 * @param data 晒选项数据源
 * @description 返回点击选中的数量
 * @returns {number | undefined}
 */
const returnMoreNum = (data: NarrowProps[] | NarrowItemProps[]): number | undefined => {
	if (data.length === 0) return;
	let filterReuslt = [];
	if (Array.isArray(dataSource.value)) {
		filterReuslt = (data as NarrowProps[])
			.flatMap(item => item.narrow)
			.filter((v: NarrowItemProps) => filterVals.value.includes(v.narrowId) || v.isCheck === 2)
			.map(item => item.narrowId);
	} else {
		filterReuslt = (data as NarrowItemProps[]).filter((item: NarrowItemProps) => filterVals.value.includes(item.narrowId) || item.isCheck === 2);
	}
	return filterReuslt.length;
};

/**
 * @description 返回标题 More Filters标题 和 常规晒选项标题
 * @returns {string}
 */
const title = computed(() => {
	if (Array.isArray(dataSource.value)) {
		return `${localeLang("secondCategory.more_filter")}  ${returnMoreNum(dataSource.value) ? `(${returnMoreNum(dataSource.value)})` : ""}`;
	} else {
		return `${dataSource.value.title} ${returnMoreNum(dataSource.value.narrow) ? `(${returnMoreNum(dataSource.value.narrow)})` : ""}`;
	}
});

const title2 = computed(() => {
	if (Array.isArray(dataSource.value)) {
		return `${localeLang("secondCategory.more_filter")}`;
	} else {
		return `${dataSource.value.title}`;
	}
});

const titleNum = computed(() => {
	if (Array.isArray(dataSource.value)) {
		return `${returnMoreNum(dataSource.value) ? `(${returnMoreNum(dataSource.value)})` : ""}`;
	} else {
		return `${returnMoreNum(dataSource.value.narrow) ? `(${returnMoreNum(dataSource.value.narrow)})` : ""}`;
	}
});

const filterSelectActive = computed(() => {
	return returnMoreNum(Array.isArray(dataSource.value) ? dataSource.value : dataSource.value.narrow)! > 0;
});

const handleChange = (v: NarrowProps, i: number) => {
	tooltip.value?.hide();
	visible.value = false;
	emits("changeOption", {
		option_id: v.productsNarrowByOptionsId,
		selectedItem: v,
		selectedIndex: i
	});
};

const handleVisibleChange = (val: boolean) => (visible.value = val);

watch(
	() => props.options,
	newVal => {
		dataSource.value = reMapDataIsCheck(newVal);
	}
);
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
