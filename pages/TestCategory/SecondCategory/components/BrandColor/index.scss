.brandColor {
	position: relative;
}

.brand {
	display: flex;
	margin-bottom: 12px;
	text-align: center;

	span,
	i {
		@include font12;
		padding: 1px 8px;
		border: 1px solid #eeeeee;
		box-sizing: border-box;
		border-radius: 4px;
		cursor: pointer;
		text-align: center;
		margin-right: 4px;
		display: inline-block;
		color: $textColor1;
		white-space: nowrap;
		text-overflow: ellipsis;

		&.active {
			border: 1px solid $textColor1;
			cursor: default;
			background: $bgColor2;
		}
		&.iHover {
			background-color: $bgColor2;
		}
		&:hover {
			background: $bgColor2;
		}

		&.blue {
			color: $textColor5;
		}
	}

	.visible {
		visibility: initial;
	}

	i {
		// background-color: $bgColor6;
		margin-right: 0;
		font-style: normal;
		visibility: hidden;
	}
}

.colorAssemble {
	display: flex;
	min-height: 35px;
	margin-bottom: 3px;
	text-align: center;
	flex-wrap: wrap;

	span {
		display: inline-block;
		width: 22px;
		height: 22px;
		padding: 4px;
		border: 1px solid #eee;
		box-sizing: border-box;
		border-radius: 4px;
		cursor: pointer;
		text-align: center;
		margin-right: 4px;
		margin-bottom: 4px;
		color: $textColor1;

		&.active {
			border: 1px solid $textColor1;
			cursor: default;
			background: #f2f2f2;
		}

		&:hover {
			background: #f2f2f2;
		}

		&:nth-last-child(1):hover {
			border-color: $borderColor3;
		}

		&.visible {
			visibility: initial;
		}

		> i {
			display: block;
			width: 100%;
			height: 100%;
			border-radius: 2px;
			&.WB {
				border: 1px solid #e5e5e5;
			}
		}
	}

	.num {
		min-width: 22px;
		height: 22px;
		padding: 1px 2px;
		@include font12;
		text-align: center;
		border: 1px solid #eee;
		box-sizing: border-box;
		border-radius: 4px;
		cursor: pointer;
		text-align: center;
		margin-bottom: 4px;
		display: inline-block;
		color: $textColor1;
		font-style: normal;
		visibility: hidden;
		&.iHover {
			background: #f2f2f2;
		}
		&.active {
			border: 1px solid $textColor1;
		}

		&.visible {
			visibility: initial;
		}

		&:hover {
			background: #f2f2f2;
		}
	}

	.noDisplay {
		display: none;
	}

	.showNum {
		visibility: inherit;
	}
}

.poper {
	position: absolute;
	top: 22px;
	width: calc(100% + 24px);
	left: -12px;
	right: 0;
	z-index: 11;

	.poper_bg {
		margin-top: 4px;
		border-radius: 4px;
		background: #fff;
		box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
		padding: 12px 0px 8px 12px;
	}

	.poper_list {
		display: flex;
		flex-wrap: wrap;
		max-height: 84px;
		padding-right: 8px;
		overflow: auto;

		.poper_item {
			@include font12;
			color: $textColor1;
			padding: 1px 8px;
			border: 1px solid #eee;
			border-radius: 4px;
			margin-right: 4px;
			margin-bottom: 4px;
			cursor: pointer;
			max-width: 90px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;

			&:hover {
				background: #f2f2f2;
			}

			&.active {
				border: 1px solid #19191a;
				cursor: default;
				background: #f2f2f2;
			}

			&.blue {
				color: #0060bf;
			}

			&.color {
				width: 22px;
				height: 22px;
				padding: 4px;

				i {
					width: 100%;
					height: 100%;
					display: block;

					&.WB {
						border: 1px solid #e5e5e5;
					}
				}
			}
		}
	}
}
