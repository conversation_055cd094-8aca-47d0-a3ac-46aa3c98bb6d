<template>
	<div class="container">
		<div v-if="isMaskLeft" class="container__beforeBtn" @click="scrollTabs('left')">
			<!-- 左滚动按钮区域 -->
		</div>
		<div ref="containerScroll" class="container__scroll" @scroll="handleScroll">
			<div ref="containerContent" class="container__content">
				<div
					v-for="(item, index) in data"
					:ref="el => (tabsRefs[index] = el)"
					:key="item.categoryId"
					:class="[activeId === item.categoryId && 'active', { 'multi-line': !isEnglish }]"
					class="container__scroll__item"
					@click="handleTabClick(item, index)">
					<img :src="isMobile ? item.mobileImageUrl : item.pcImageUrl" alt="" />
					<span :ref="el => (spanRefs[index] = el)" :style="{ height: spanHeight ? spanHeight + 'px' : 'auto' }" :title="item.name" v-html="item.name"></span>
				</div>
			</div>
		</div>
		<div v-if="isMaskRight" class="container__afterBtn" @click="scrollTabs('right')">
			<!-- 右滚动按钮区域 -->
		</div>
	</div>
</template>

<script setup lang="ts">
import type { CategoryContextKey } from "../../types";
import type { ICategoryFilter, ICategprySwiper } from "./types";
import { getCategoryId } from "~/pages/Category/utils";

// 注入全局状态
const injectState = inject<CategoryContextKey>("CategoryContextKey") as CategoryContextKey;
const props = defineProps<ICategprySwiper>();

// 状态定义
const isMaskLeft = ref(false); // 左侧遮罩显示状态
const isMaskRight = ref(false); // 右侧遮罩显示状态
const tabsRefs: any = {}; // 存储每个标签的DOM引用
const containerScroll = ref(); // 滚动容器引用
const containerContent = ref(); // 内容容器引用
const deviceStore = useDeviceStore();
const isMobile = computed(() => deviceStore.isMobile);
const data = ref(props.categoryFilter ?? []);
const activeId = ref<number>(); // 当前激活的分类ID
const route = useRoute();
const spanHeight = ref(); // 统一的span高度
const spanRefs: any = ref([]); // 存储每个span的引用
const localeLink = useLocaleLink();

// 语言相关
const websiteStore = useWebsiteStore();
const { language } = storeToRefs(websiteStore);
const isEnglish = computed(() => language.value === "English");

/**
 * 处理标签点击事件
 * @param item 点击的分类项
 * @param index 点击的索引
 */
const handleTabClick = (item: ICategoryFilter, index: number) => {
	if (activeId.value === item.categoryId) return;
	activeId.value = item.categoryId;
	injectState.loading.value = true;
	navigateTo({ path: localeLink(item.url) });
};

/**
 * 处理滚动事件，更新左右遮罩状态
 */
const handleScroll = () => {
	const { scrollLeft, clientWidth, scrollWidth } = containerScroll.value!;
	isMaskLeft.value = scrollLeft > 16;
	isMaskRight.value = isMobile.value
		? scrollLeft + clientWidth + 48 < scrollWidth
		: scrollLeft + clientWidth < scrollWidth || containerContent.value!.getBoundingClientRect().width > clientWidth;
};

/**
 * 滚动到当前激活的标签
 * @param index 标签索引
 */
const scrollToActiveTab = (index: number) => {
	let sum = 0;
	for (let i = 0; i <= index; i++) {
		sum += tabsRefs[i].getBoundingClientRect().width + 24;
	}
	const halfWidth = innerWidth / 2;
	let scrollLeft = sum - tabsRefs[index].getBoundingClientRect().width / 2 - 24 + (isMobile.value ? 16 : 0);
	if (scrollLeft > halfWidth) {
		scrollLeft -= halfWidth;
	} else {
		scrollLeft = 0;
	}
	containerScroll.value!.scrollTo({ left: scrollLeft, behavior: "smooth" });
	updateMaskVisibility(index, sum);
};

/**
 * 更新左右遮罩的显示状态
 * @param index 当前标签索引
 * @param sum 累计宽度
 */
const updateMaskVisibility = (index: number, sum: number) => {
	const { left } = tabsRefs[index].getBoundingClientRect();
	const halfWidth = innerWidth / 2;
	if (left <= halfWidth / 2 && containerScroll.value!.scrollLeft > 16) {
		isMaskRight.value = true;
	} else if (left > halfWidth / 2 && sum >= innerWidth) {
		isMaskLeft.value = true;
	} else {
		isMaskLeft.value = false;
	}
	if (containerContent.value!.scrollWidth > containerScroll.value!.getBoundingClientRect().width) {
		isMaskRight.value = true;
	} else {
		isMaskRight.value = false;
	}
};

/**
 * 滚动标签容器
 * @param direction 滚动方向：'left' 或 'right'
 */
const scrollTabs = (direction: "left" | "right") => {
	const { scrollLeft, clientWidth, scrollWidth } = containerScroll.value!;
	let totalWidth = 0;
	let tabWidthToScroll = 0;
	let nextScrollPosition = scrollLeft;
	// 计算当前可见范围内的标签
	for (let i = 0; i < Object.values(tabsRefs).length; i++) {
		const tabWidth = tabsRefs[i].getBoundingClientRect().width + 24; // 包含间隙
		totalWidth += tabWidth;
		if (totalWidth > scrollLeft + clientWidth) {
			tabWidthToScroll = tabWidth;
			break;
		}
	}
	if (direction === "left") {
		nextScrollPosition = scrollLeft - tabWidthToScroll;
		if (nextScrollPosition < 0) {
			nextScrollPosition = 0; // 防止滚动超出左边界
		}
	}
	if (direction === "right") {
		nextScrollPosition = scrollLeft + tabWidthToScroll;
		if (nextScrollPosition > scrollWidth - clientWidth) {
			nextScrollPosition = scrollWidth - clientWidth; // 防止滚动超出右边界
		}
	}

	containerScroll.value!.scrollTo({
		left: nextScrollPosition,
		behavior: "smooth"
	});
};

/**
 * 计算所有标签中最大的高度，并统一设置
 */
const calculateMaxHeight = () => {
	let maxHeight = 20; // 默认高度

	// 确保所有元素都已加载
	if (spanRefs.value && spanRefs.value.length > 0) {
		spanRefs.value.forEach((span: { offsetHeight: number }) => {
			if (span && span.offsetHeight) {
				const height = span.offsetHeight; // 获取span的实际高度
				if (height > maxHeight) {
					maxHeight = height;
				}
			}
		});

		// 设置最小高度保证
		if (maxHeight < 20) {
			maxHeight = 20;
		}

		spanHeight.value = maxHeight; // 更新所有span的高度
	} else {
		// 如果元素还未加载，设置一个默认值并在下一个渲染周期重试
		spanHeight.value = 20;
		nextTick(() => {
			calculateMaxHeight();
		});
	}
};

/**
 * 窗口大小变化时重新计算高度
 */
const handleResize = () => {
	nextTick(() => {
		calculateMaxHeight();
	});
};

onMounted(() => {
	// 从路由中获取当前分类ID
	const [id] = getCategoryId(route.path);
	activeId.value = id;

	// 使用nextTick确保DOM已经渲染
	nextTick(() => {
		calculateMaxHeight();

		// 再次使用nextTick确保高度计算后再滚动到活动标签
		nextTick(() => {
			const index = data.value.findIndex(item => item.categoryId === activeId.value);
			scrollToActiveTab(index ?? 0);
		});
	});

	// 添加窗口大小变化监听
	window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
	// 移除事件监听器
	window.removeEventListener("resize", handleResize);
});
</script>
<style lang="scss" scoped>
@import url("./index.scss");
</style>
