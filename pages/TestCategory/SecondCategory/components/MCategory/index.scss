.mCategory {
	&__grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		position: relative;
		margin: 16px 16px 0;
		gap: 12px;

		&::after {
			position: absolute;
			content: "";
			width: 100%;
			height: 1px;
			background-color: #fff;
			bottom: 0;
			left: 0;
			z-index: 1;
		}

		&__item {
			border-top: none;
			// padding: 24px 20px;
			overflow: hidden;
			position: relative;
			background-color: #fafafb;
			border-radius: 4px;

			&:hover {
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
				border-color: transparent;
			}
		}
	}

	&__secondaryTitle {
		font-size: 20px;
		font-weight: 600;
		padding-left: 16px;
		margin-bottom: 20px;
	}

	&__noProducts {
		margin: 20px auto;
		padding: 40px 48px;
		background: #fff;

		> div {
			text-align: center;
		}

		&__tit {
			@include font16;
			color: $textColor1;
			font-weight: 600;
			margin-bottom: 8px;
		}

		&__msg {
			@include font14;
			color: $textColor3;
		}
	}

	&__pagination {
		margin: 20px auto 36px;
	}
}

:deep(.grid__img) {
	margin-bottom: 12px;

	img {
		height: 100% !important;
		max-width: 180px;
		width: 100% !important;
	}

	.grid__img__cart {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 36px;
		right: 0;
		width: 36px;
		color: $textColor1;
	}
}

.splitline {
	margin: 16px;
	border-top: 1px solid #eeeeee;
}
