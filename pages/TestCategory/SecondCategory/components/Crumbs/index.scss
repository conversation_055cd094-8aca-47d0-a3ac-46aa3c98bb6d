.crumbs {
	position: relative;

	&__box {
		display: flex;
		align-items: center;
		padding: 20px 0;
		@include mobile {
			padding: 24px 16px 16px;
			display: flex;
			align-items: center;
			white-space: nowrap;
			overflow-x: auto;

			&::-webkit-scrollbar {
				display: none;
			}

			i {
				font-size: 12px;
			}
		}

		.scroll_mask {
			position: absolute;
			width: 42px;
			height: 100%;
			background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
			top: 0;
			right: 0;
			z-index: 1;

			&.left {
				transform: rotate(180deg);
				left: 0;
			}
		}

		&__item {
			display: flex;
			align-items: center;
			color: $textColor2;
			@include font12;
			text-decoration: none;
			transition: all 0.3s;
			cursor: pointer;

			a {
				@include font12;
				color: $textColor2;

				&:hover {
					color: $textColor1;
					text-decoration: none;
					transition: all 0.3s;
				}
			}

			i {
				color: $textColor2;
				font-size: 12px;
				margin: 0 4px;
			}

			&__last {
				a {
					color: $textColor1;
					cursor: default;
				}

				i {
					display: none;
				}
			}
		}

		&__title {
			display: flex;
			align-items: center;
			cursor: pointer;

			&__content {
				color: $textColor1;
				cursor: pointer;
				@include font12;
			}

			i {
				font-size: 12px;
				margin: 0 4px;
				color: $textColor1;
			}

			&--active {
				i {
					transform: rotateX(180deg);
				}
			}
		}

		&__popover {
			width: 100%;
			padding: 9px 12px;
			@include font12;
			color: $textColor2;
			overflow: hidden;
			text-decoration: none;
			text-overflow: ellipsis;
			white-space: nowrap;
			cursor: pointer;
			display: block;

			&.active {
				background: $bgColor1;
				color: $textColor1;
			}

			&:hover {
				background-color: $bgColor1;
			}
		}
	}
}
