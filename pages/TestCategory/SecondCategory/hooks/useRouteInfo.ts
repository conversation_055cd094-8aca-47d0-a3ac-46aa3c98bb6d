/***
 *  @description 获取url路由地址参数 keys集合 val集合 以及sort_order参数
 *  @returns {filterKeys: [], filterVals: [], sort_order: string}
 */
const useRouteInfo = () => {
	const route = useRoute();
	const filterKeys = ref<number[]>([]);
	const filterVals = ref<number[]>([]);
	const sort_order = ref(route.query?.sort_order as string);
	const getFilterRouterKeys = () =>
		Object.keys(route.query)
			.filter(key => filterParameters(key))
			.map(item => parseInt(item));

	const getFilterRouterVals = () =>
		Object.keys(route.query)
			.filter(key => {
				return filterParameters(key);
			})
			.flatMap(key => {
				const value = route.query[key]?.toString();
				if (value) {
					return value.split(",").map(item => parseInt(item));
				} else {
					return [];
				}
			});

	filterKeys.value = getFilterRouterKeys();
	filterVals.value = getFilterRouterVals();
	watch(
		() => route.fullPath,
		() => {
			filterKeys.value = getFilterRouterKeys();
			filterVals.value = getFilterRouterVals();
			sort_order.value = route.query?.sort_order as string;
		},
		{
			deep: true
		}
	);
	return { filterKeys, filterVals, sort_order };
};

export default useRouteInfo;
