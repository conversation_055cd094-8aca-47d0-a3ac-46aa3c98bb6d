import type { LocationQueryValue } from "vue-router";
export type CardType = "grid" | "list";
export interface CategoryApiProps {
	categoryId: number;
	sortOrder: string | LocationQueryValue[];
	filters: [];
	current: number;
	meta: object;
	isLocalWarehouse: number;
	size: number;
}

export type matchingRangeResults = {
	min: number;
	max: number;
	result: number;
};

export interface WaterFallProps {
	img: string;
	url: string;
	titleColor: string;
	title: string;
	description: string;
}

export interface newSourceProps {
	delivery: any;
	description: string;
	tip: string;
}

export interface SourceProps {
	[x: number]: newSourceProps[];
}

export interface pageConfigProps {
	pageSize: number;
	current: number;
	total: number;
}

export interface Default {
	product_id: string;
	attribute_name_str: string;
	en_attr_name: string;
	is_show: number;
}

export interface RelateProduct {
	productId: number | string;
	attributeNameStr: string;
	enAttrName: string;
	isShow: number;
	isDefault: boolean;
	type: number;
}

export interface AttributesProps {
	isColor: boolean;
	default: Default[];
	relateProducts: RelateProduct[];
}

export interface CategoryProductProps {
	productsId: number;
	image: string;
	isLabel: number;
	label: string;
	isInquiry: number;
	isVirtualProduct: boolean;
	attributes: AttributesProps;
	productsName: string;
	productsModel: string;
	productsSellingPoints: string[];
	price: string;
	productsPrice: string;
	taxProductsPrice: string;
	productsSales: string;
	productsReviews: string;
	serverHighlights: [];
	productsTag: string;
	isShowTax: number;
	inventoryDeliveryList: SourceProps | newSourceProps[];
	taxPriceTips: string;
}

export interface CrumbsProps {
	id: number;
	name: string;
	url: string;
}

export interface CategoryFilterItemProps {
	categoryId: number;
	name: string;
	url: string;
	label?: number;
	image?: string;
}

export interface CategoryFilterProps {
	title: string;
	enTitle: string;
	categories: CategoryFilterItemProps[];
}

export interface CategoryProps {
	categoryProducts: Array<CategoryProductProps>;
	waterfall: Array<WaterFallProps>;
	categoryFilter: CategoryFilterProps;
	narrowFilter: [];
	filterList: [];
	inventoryDeliveryList: any;
	crumbs: Array<CrumbsProps>;
	categoriesId: number;
	sortOrder: string;
	pageConfig: pageConfigProps;
	allCount: number;
}

export interface CategoryInfoProps {
	categoryInfo: CategoryProps;
}

export interface CategoryContextKey {
	displayType: Ref<CardType>;
	addCartLoading: Ref<boolean>;
	addCartStatus: Ref<boolean>;
	addCartId: Ref<number>;
	loading: Ref<boolean>;
	changeGoodsInfo: (v: CategoryProductProps, cardIndex: number, categoryProductInfo?: CategoryProductProps) => void;
	cartAnimationstartElement: any;
}
export const CategoryContextKey: InjectionKey<CategoryContextKey> = Symbol("CategoryContextKeyKey");

export interface MetaLilterList {
	title: string;
	val: {
		id: number;
		name: string;
	};
}

export interface MetaCrumbs {
	id: number;
	name: string;
	url: string;
}
