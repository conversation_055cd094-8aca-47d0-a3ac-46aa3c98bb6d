<template>
	<FirstCategory v-if="categoryType === 'first'"> </FirstCategory>
	<SecondCategory v-else></SecondCategory>
</template>

<script setup lang="ts">
import FirstCategory from "./FirstCategory/index.vue";
import SecondCategory from "./SecondCategory/index.vue";
import { getCategoryId } from "./utils";
definePageMeta({
	layout: "common"
});
const store = useHeaderStore();
const route = useRoute();
const localeLink = useLocaleLink();
const runtimeConfig = useRuntimeConfig();

const metaStore = useMeatStore();

const { langLink } = storeToRefs(metaStore);
useHead({
	link: [...langLink.value]
});
const categoryType = ref();
const getCategoryType = () => {
	const regPos = /^\d+(\.\d+)?$/;
	const [id] = getCategoryId(route.path);
	if (regPos.test(id.toString())) {
		categoryType.value = store.firstCateIds.includes(Number(id)) ? "first" : "sceond";
	} else {
		navigateTo(localeLink(runtimeConfig.public.VITE_NUXT_DOMAIN + "/404.html"), {
			external: true
		});
	}
};
getCategoryType();
watch(
	() => route.path,
	newVal => {
		if (newVal) {
			getCategoryType();
		}
	}
);
</script>

<style lang="scss" scoped>
.category_wrap {
	min-height: 600px;
}
</style>
