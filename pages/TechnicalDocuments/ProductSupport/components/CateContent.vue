<template>
	<div class="two_three_type">
		<div class="center_content">
			<div class="breads">
				<FsBreadcrumb separator="/">
					<FsBreadcrumbItem v-for="(item, index) in breadcrumbData" :key="index" :active="item.active" @breadcrumb-click="(e: MouseEvent) => handleClick(e, item)">{{
						item.name
					}}</FsBreadcrumbItem>
				</FsBreadcrumb>
				<div class="scroll_mask"></div>
			</div>
			<div class="inp_box">
				<FsInput
					v-model.trim="searchInp"
					class="input"
					:placeholder="localeLang('technicalDocuments.placeholder')"
					@focus="gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, 'TDocContent_search_input', 'Search Input')"
					@keydown="searchKeyword"
					@input="getResult">
					<template #suffix>
						<div class="options_box">
							<span v-if="searchInp.length > 0" class="iconfont iconfont_close" @click="clearKeywords">&#xf30a;</span>
							<span class="iconfont iconfont_search" @click.stop="searchKeyword">&#xe694;</span>
						</div>
					</template>
				</FsInput>
				<div v-show="resultShow" class="search_res">
					<ul v-if="resultList.length">
						<li v-for="(item, index) in resultList" :key="index" @click.stop="searchListGo(item)">
							<div class="tit_div">
								<p class="tit" v-html="item.resourceName"></p>
								<img v-if="item.isShowNew" class="tag_img" src="https://resource.fs.com/mall/generalImg/202407251649182wwhwk.svg" alt="image" />
							</div>
							<p v-if="item.preContent || item.postContent" class="txt" v-html="item.preContent + item.postContent"></p>
						</li>
					</ul>
					<div v-else class="no_res">{{ localeLang("technicalDocuments.productSupport.noResult") }}</div>
				</div>
			</div>

			<div id="pdfContent">
				<div class="tit_tag">
					<h3 class="tit">{{ nowData.name || nowData.styleName }}</h3>
					<img v-if="fileInfo?.isNewTag" class="tag_img" src="https://resource.fs.com/mall/generalImg/202407251649182wwhwk.svg" alt="image" />
				</div>
				<!-- 更新时间、下载按钮 -->
				<div v-if="nowData.files_id || nowData.resourceTypeId" class="down_box">
					<p class="update">
						<!-- <span>{{ localeLang("technicalDocuments.productSupport.update") }}</span> -->
						<span>{{ fileInfo?.dateString }}</span>
					</p>
					<div class="btns">
						<a v-if="fileInfo?.productUrl" class="product" :href="fileInfo?.productUrl" target="_blank">{{ localeLang("searchResult.tabs.pd") }}</a>
						<a v-if="fileInfo?.communityUrl" class="community" :href="fileInfo?.communityUrl" target="_blank">{{ localeLang("technicalDocuments.productSupport.community") }}</a>
						<div v-if="fileInfo?.communityUrl || fileInfo?.productUrl" class="line"></div>
						<div class="download" @click="download(isFeiShuResource ? '' : fileInfo?.fileUrl || nowData.file_url)">
							<i class="icon iconfont">&#xe653;</i><span class="downText">{{ localeLang("technicalDocuments.productSupport.download") }}</span>
						</div>
						<!-- <a
                            class="feedback"
                            :href="localeLink(`/products_support/feedback.html?doc_type=${fileInfo?.fileType || ''}&url=${locationOrigin}${route.fullPath}`)"
                            target="_blank"
                            @click="burryPoint">
                            <span class="iconfont">&#xe721;</span>
                            <span class="txt">{{ localeLang("technicalDocuments.productSupport.feedback") }}</span>
                        </a> -->
					</div>
				</div>
				<!-- 中间内容 -->
				<!-- level === 6 datasheet-html文档 -->
				<div v-if="(nowData.files_id && nowData.is_datasheet) || nowData.resourceTypeId">
					<DatasheetPreview v-if="fileInfo && fileInfo.datasheet && !isFeiShuResource" ref="datasheetRef" :fileInfo="fileInfo" />
					<FeishuSheet v-if="fileInfo?.content && isFeiShuResource" ref="feishuSheetRef" :fsInfo="fileInfo" />
				</div>
			</div>
			<div v-if="fileInfo?.fileUrl && isFeiShuResource" class="card" :class="{ mt: !fileInfo?.content || !fileInfo.datasheet }" @click="openPdf(fileInfo?.fileUrl, fileInfo)">
				<div class="cont">
					<!-- pdf png xls jpg docx zip rar -->
					<span v-if="fileInfo.fileUrl.slice(-3) === 'pdf'" class="iconfont">&#xe69a;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'png'" class="iconfont">&#xe68e;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'xls'" class="iconfont">&#xe695;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'jpg'" class="iconfont">&#xe696;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'docx'" class="iconfont">&#xe697;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'zip'" class="iconfont">&#xe698;</span>
					<span v-if="fileInfo.fileUrl.slice(-3) === 'rar'" class="iconfont">&#xe699;</span>
					<span v-if="fileInfo.fileUrl.slice(-4) === 'jpeg'" class="iconfont">&#xe696;</span>
					<div>
						<h4>{{ fileInfo.fileName }}</h4>
						<p>{{ fileInfo.fileSize }}</p>
					</div>
				</div>
				<span v-if="fileInfo.fileUrl && (['pdf', 'png', 'jpg'].includes(fileInfo.fileUrl.slice(-3)) || fileInfo.fileUrl.slice(-4) === 'jpeg')" class="btn">{{
					localeLang("technicalDocuments.productSupport.view")
				}}</span>
				<span v-else class="btn">{{ localeLang("technicalDocuments.productSupport.download") }}</span>
			</div>
			<div class="level_box">
				<!-- level === 6 未html化，点击跳转文档  -->
				<div v-if="nowData.files_id && !nowData.is_datasheet" class="pdfCont">
					<h4 class="pdfTit">{{ localeLang("technicalDocuments.productSupport.intruction") }}</h4>
					<p class="pdfDesc">
						{{ localeLang("technicalDocuments.productSupport.forDetail") }}
					</p>
					<div class="card" @click="openPdf(nowData.file_url, nowData)">
						<div v-if="nowData.file_url" class="cont">
							<!-- pdf png xls jpg docx zip rar -->
							<span v-if="nowData.file_url.slice(-3) === 'pdf'" class="iconfont">&#xe69a;</span>
							<span v-if="nowData.file_url.slice(-3) === 'png'" class="iconfont">&#xe68e;</span>
							<span v-if="nowData.file_url.slice(-3) === 'xls'" class="iconfont">&#xe695;</span>
							<span v-if="nowData.file_url.slice(-3) === 'jpg'" class="iconfont">&#xe696;</span>
							<span v-if="nowData.file_url.slice(-3) === 'docx'" class="iconfont">&#xe697;</span>
							<span v-if="nowData.file_url.slice(-3) === 'zip'" class="iconfont">&#xe698;</span>
							<span v-if="nowData.file_url.slice(-3) === 'rar'" class="iconfont">&#xe699;</span>
							<span v-if="nowData.file_url.slice(-4) === 'jpeg'" class="iconfont">&#xe696;</span>
							<div>
								<h4>{{ nowData.file_name }}</h4>
								<p>{{ nowData.file_size }}</p>
							</div>
						</div>
						<span v-if="['pdf', 'png', 'jpg'].includes(nowData.file_url.slice(-3)) || nowData.file_url.slice(-4) === 'jpeg'" class="btn">{{
							localeLang("technicalDocuments.productSupport.view")
						}}</span>
						<span v-else class="btn">{{ localeLang("technicalDocuments.productSupport.download") }}</span>
					</div>
				</div>
				<!-- level === 2时，展示卡片样式 -->
				<div v-else-if="nowData.level === 2 && nowData.children" class="categoryCont">
					<div
						v-for="(thirdItem, third_index) in nowData.children"
						:key="thirdItem.id"
						class="third_card"
						:class="{ last_third_card: third_index === nowData.children.length - 1 }">
						<p class="third_tit">{{ thirdItem.name }}</p>
						<ul class="forth_card">
							<li v-for="forthItem in thirdItem.is_more_select ? thirdItem.children : thirdItem.children.slice(0, 10)" :key="forthItem.id">
								<p @click="goDetail(forthItem)">{{ forthItem.name }}</p>
							</li>
						</ul>
						<FsButton v-if="thirdItem.is_show_more" type="primary" text class="more_btn" @click="thirdItem.is_more_select = !thirdItem.is_more_select">{{
							thirdItem.is_more_select ? localeLang("technicalDocuments.productSupport.showLess") : localeLang("technicalDocuments.productSupport.showMore")
						}}</FsButton>
					</div>
				</div>
				<!-- level !== 6时展示列表 -->
				<ul v-else-if="nowData.level && nowData.children" class="ulCont">
					<li v-for="item in nowData.children" :key="item.id">
						<span class="dot"></span>
						<p @click="goDetail(item)">{{ item.name }}</p>
					</li>
				</ul>
				<ul v-else-if="nowData.styleId && nowData.children" class="ulCont">
					<li v-for="item in nowData.children" :key="item.id">
						<span class="dot"></span>
						<p @click="goDetail(item)">{{ item.name }}</p>
					</li>
				</ul>
			</div>
			<div class="line"></div>
			<div class="btn_box">
				<div v-if="preArticle.name" class="pre_box" :class="{ def: !preArticle.id }" @click="getPreNext('pre')">
					<span class="iconfont">&#xe726;</span>
					<span class="txt">{{ localeLang("technicalDocuments.productSupport.previous") }}{{ preArticle.name }}</span>
				</div>
				<div v-if="nextArticle.name" class="next_box" :class="{ def: !nextArticle.id }" @click="getPreNext('next')">
					<span class="txt">{{ localeLang("technicalDocuments.productSupport.next") }}{{ nextArticle.name }}</span>
					<span class="iconfont">&#xe726;</span>
				</div>
			</div>
			<div class="feedback_info_m">
				<div class="feedback_div_m">
					<p class="tit">{{ localeLang("technicalDocuments.productSupport.documentHelp") }}</p>
					<div class="cont_m">
						<div class="yes_no_m">
							<FsTooltip
								ref="mTooltipRef"
								placement="top-start"
								trigger="click"
								manual
								:popperContentStyle="{
									width: '210px',
									maxWidth: '288px',
									padding: '20px',
									borderRadius: '4px',
									gap: '2px',
									display: 'flex',
									flexDirection: 'column'
								}"
								:class="{ backStyle: selectFlag }">
								<FsButton class="yes" @click="showTip()">{{ localeLang("technicalDocuments.productSupport.yes") }}</FsButton>
								<template #content>
									<div class="cont">
										<p class="tip_tit" v-html="localeLang('technicalDocuments.productSupport.voteDesc')"></p>
										<FsButton type="primary" class="sendFeedback" text @click="showDialog(1)">{{ localeLang("technicalDocuments.productSupport.sendFeedback") }}</FsButton>
									</div>
								</template>
							</FsTooltip>
							<FsButton class="no" :class="{ noBackStyle: mSelectNoFlag }" @click="showDialog(0)">{{ localeLang("technicalDocuments.productSupport.no") }}</FsButton>
						</div>
						<a class="feedback" target="_blank" @click="showDialog(1)">
							<span class="iconfont">&#xe721;</span>
							<span class="txt">{{ localeLang("technicalDocuments.productSupport.feedback") }}</span>
						</a>
					</div>
				</div>
				<div class="contact_fs">
					<p class="tit">{{ localeLang("technicalDocuments.productSupport.contactFS") }}</p>
					<a class="case" :href="supportCaseLink" target="_blank" @click="gaPoint">{{ localeLang("technicalDocuments.productSupport.supportCase") }}</a>
				</div>
			</div>
		</div>
		<div v-if="nowData.files_id || nowData.resourceTypeId" class="right_tit">
			<h4>{{ localeLang("technicalDocuments.productSupport.thisPage") }}</h4>
			<ul v-if="titleList.length && (nowData.is_datasheet || nowData.resourceTypeId) && fileInfo" class="tit_box">
				<template v-for="(item, index) in titleList" :key="index">
					<li v-if="item.value" class="intro" @click="clickTitle(item, index)">
						<span class="dot"></span>
						<p :class="{ activeTitIndex: item.value === activeTitIndex }">{{ item.value }}</p>
						<ul v-if="item.children && item.children.length" class="child_tit">
							<li
								v-for="(child, childIndex) in item.children"
								:key="childIndex"
								:class="{ activeTitIndex: child.value === activeTitIndex }"
								@click.stop="clickTitle(child, childIndex)"
								v-html="child.value"></li>
						</ul>
					</li>
				</template>
			</ul>
			<div v-else class="intro intros">
				<span class="dot"></span>
				<p class="activeTitIndex">{{ localeLang("technicalDocuments.productSupport.intruction") }}</p>
			</div>
			<div class="line_right"></div>
			<div class="feedback_info">
				<div class="feedback_div">
					<p class="tit">{{ localeLang("technicalDocuments.productSupport.documentHelp") }}</p>
					<div class="cont">
						<div class="yes_no">
							<FsTooltip
								ref="tooltipRef"
								placement="top-start"
								trigger="click"
								manual
								:popperContentStyle="{
									width: '210px',
									maxWidth: '288px',
									padding: '20px',
									borderRadius: '4px',
									gap: '2px',
									display: 'flex',
									flexDirection: 'column'
								}"
								:class="{ backStyle: selectFlag }">
								<FsButton class="yes" @click="showTip()">{{ localeLang("technicalDocuments.productSupport.yes") }}</FsButton>
								<template #content>
									<div class="cont">
										<p class="tip_tit" v-html="localeLang('technicalDocuments.productSupport.voteDesc')"></p>
										<FsButton type="primary" class="sendFeedback" text @click="showDialog(1)">{{ localeLang("technicalDocuments.productSupport.sendFeedback") }}</FsButton>
									</div>
								</template>
							</FsTooltip>
							<FsButton class="no" :class="{ noBackStyle: selectNoFlag }" @click="showDialog(0)">{{ localeLang("technicalDocuments.productSupport.no") }}</FsButton>
						</div>
						<a class="feedback" target="_blank" @click="showDialog(1)">
							<span class="iconfont">&#xe721;</span>
							<span class="txt">{{ localeLang("technicalDocuments.productSupport.feedback") }}</span>
						</a>
					</div>
				</div>
				<div class="contact_fs">
					<p class="tit">{{ localeLang("technicalDocuments.productSupport.contactFS") }}</p>
					<a class="case" :href="supportCaseLink" target="_blank" @click="gaPoint">{{ localeLang("technicalDocuments.productSupport.supportCase") }}</a>
				</div>
			</div>
		</div>
		<FsDialog v-model="isShowFeedBackForm" :closeModal="false" :title="localeLang('technicalDocuments.productSupport.foemTitle')" className="feedback_form" Bottom>
			<template #default>
				<div class="form-content">
					<p v-if="!isLogin" class="login_desc" v-html="loginInText"></p>
					<fs-form ref="formRef" :model="userForm" :rules="rules">
						<FsFormItem class="feedbackBox" prop="feedbackType" :label="`${localeLang('technicalDocuments.productSupport.problemsType')} *`">
							<div>
								<div class="radio_box">
									<FsRadio v-model.trim="userForm.feedbackType" :label="'1'" @change="sureType('1')">{{ localeLang("technicalDocuments.productSupport.inaccurate") }}</FsRadio>
									<FsRadio v-model.trim="userForm.feedbackType" :label="'2'" @change="sureType('2')">{{ localeLang("technicalDocuments.productSupport.productRelated") }}</FsRadio>
									<FsRadio v-model.trim="userForm.feedbackType" :label="'3'" @change="sureType('3')">{{ localeLang("technicalDocuments.productSupport.searchRelated") }}</FsRadio>
									<FsRadio v-model.trim="userForm.feedbackType" :label="'4'" @change="sureType('4')">{{ localeLang("technicalDocuments.productSupport.others") }}</FsRadio>
								</div>
							</div>
						</FsFormItem>
						<FsFormItem class="areaText" prop="content" :label="`${localeLang('technicalDocuments.productSupport.descTitle')} *`">
							<fs-input
								v-model.trim="userForm.content"
								:maxlength="5000"
								type="textarea"
								:placeholder="localeLang('technicalDocuments.productSupport.descPlaceholder')"
								newRegular />
						</FsFormItem>
						<FsFormItem class="areaShot" prop="imageUrl">
							<div class="shot_cont">
								<div class="shot_box">
									<FsCheckbox v-model="userForm.isShot" @change="shotSelected"> </FsCheckbox>
									<span class="agree" @click.stop="">{{ localeLang("technicalDocuments.productSupport.provideScreenshots") }}</span>
								</div>
								<div v-loading="isLoading" class="pic_div" :class="{ pic_div_back: userForm.isShot }">
									<span v-show="!userForm.isShot">{{ localeLang("technicalDocuments.productSupport.noPicture") }}</span>
									<img id="picture_shot" :src="userForm.isShot ? userForm.imageUrl : ''" alt="" />
									<div class="shadow_div" :class="{ shadow_back: userForm.isShot }" @click="showShotImg"></div>
									<FsButton v-show="userForm.isShot" iconPlacement="prefix" text @click="showShotImg">
										<template #default>{{ localeLang("technicalDocuments.productSupport.preview") }}</template>
										<template #icon>
											<span class="iconfont">&#xe748;</span>
										</template>
									</FsButton>
								</div>
							</div>
						</FsFormItem>

						<div class="line"></div>
						<FsFormItem class="contact_title" :label="`${localeLang('technicalDocuments.productSupport.contactTitle')}`"> </FsFormItem>
						<fs-flex class="contact_info" :gap="20" label="ffff">
							<FsFormItem prop="name">
								<fs-input v-model.trim="userForm.name" :placeholder="localeLang('technicalDocuments.productSupport.name')" newRegular></fs-input>
							</FsFormItem>
							<FsFormItem prop="email">
								<fs-input v-model.trim="userForm.email" :placeholder="localeLang('technicalDocuments.productSupport.emailAddress')" newRegular></fs-input>
							</FsFormItem>
						</fs-flex>
						<FsFormItem class="flex1" prop="policy">
							<div class="policy_box">
								<FsCheckbox v-model="userForm.policy"> </FsCheckbox>
								<span class="agree" @click.stop="" v-html="policyText"></span>
							</div>
						</FsFormItem>
					</fs-form>
				</div>
			</template>
			<template #footer>
				<div class="form_footer">
					<FsButton type="black" plain @click="handlecancel()">{{ localeLang("technicalDocuments.productSupport.cancel") }}</FsButton>
					<FsButton type="red" :loading="loadingRef" @click="handleSubmit()">{{ localeLang("technicalDocuments.productSupport.submit") }}</FsButton>
				</div>
			</template>
		</FsDialog>
		<FsDialog v-model="isSummitSuccess" className="successDialog">
			<template #default>
				<div class="success-content">
					<div class="text_div">
						<span class="iconfont icon_success">&#xf262;</span>
						<div class="cont_div">
							<p class="tit">Thank you.We have received your feedback</p>
							<p class="des">Automatic shutdown after 3 seconds</p>
						</div>
					</div>
					<div class="pic" @click="close">
						<span class="iconfont">&#xf30a;</span>
					</div>
				</div>
			</template>
		</FsDialog>
		<FsDialog v-model="isShowShotImg" :fullScreen="true" :closeModal="false" className="shotImgDialog" @update:modelValue="console.log('44444=', $event)">
			<template #default>
				<ImageMarking class="imgMarking" :imageUrl="userForm.isShot ? userForm.imageUrl : ''" :file="userForm.upload_file[0]" @editImg="editImg" />
			</template>
		</FsDialog>
	</div>
</template>

<script setup lang="ts">
import { FsBreadcrumb, FsBreadcrumbItem, FsButton, FsInput, FsTooltip, FsDialog, FsForm, FsFormItem, FsCheckbox, FsRadio, FsFlex, FsMessage } from "fs-design";
import html2canvas from "html2canvas";
import { renderToString } from "katex";
import type { TwoThreeType, SortProps, PreTreeNode, CateItem, FilesInfoType, ContentType, ResourceListItem } from "../types";
import DatasheetPreview from "./DatasheetPreview/DatasheetPreview.vue";
import FeishuSheet from "./FeishuSheet/FeishuSheet.vue";
import { flattenTree, findItemById, organizeItems, findParents } from "./methods";
import type { datasheetNowType } from "./DatasheetPreview/types";
import ImageMarking from "./ImageMarking.vue";
import exportSavePDF from "@/utils/exportSavePDF";
import { gaDocumentation } from "@/utils/burialPoint";
import { email_valdate } from "@/constants/validate";
const localeLink = useLocaleLink();
const localeLang = useLocaleLang();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const websiteStore = useWebsiteStore();
const { website } = storeToRefs(websiteStore);
const deviceStore = useDeviceStore();
const { screenWidth } = storeToRefs(deviceStore);
const setMeta = useSetMeta();

const resultShow = ref(false);
const resultList = ref<ResourceListItem[]>([]);
const { getRecaptchaToken } = useGrecaptcha();
const allProduct = ref(inject<TwoThreeType>("allProduct") as TwoThreeType);
const nowData = ref(inject<TwoThreeType | datasheetNowType>("nowData") as any);
const titleList = ref(inject<any[]>("titleList") as any[]);
const loading = ref(inject<boolean>("loading") as boolean);
const openKeys = ref(inject("openKeys") as string | number);
const emit = defineEmits<{
	(e: "goTreeNode", obj: any): void;
	(e: "upTree", id: any): void;
}>();

const datasheetRef = ref<HTMLElement | null>(null);
const currentSort = ref<SortProps>({});
const searchInp = ref("");
const fileInfo = ref<FilesInfoType>();
const isNewDatasheet = ref<number>(0);
const isFeiShuResource = ref(false);
const titleFirst = ref("");
const activeTitIndex: Ref<string | number> = ref(0);
let clickFlag = false;
let requestAnimation: number | null | undefined = null;
const titleNode = ref<HTMLElement[]>([]);
const preArticle = ref<PreTreeNode>({
	name: localeLang("technicalDocuments.productSupport.none"),
	id: 0
});
const nextArticle = ref<PreTreeNode>({
	name: localeLang("technicalDocuments.productSupport.none"),
	id: 0
});
const breadcrumbData = ref();
let secondCate: CateItem = {};
let product_model: CateItem = {};
let styleType: CateItem = {};
let fileType: CateItem = {};
let sixType: CateItem = {};
let sevenType: CateItem = {};
let eightType: CateItem = {};
const locationOrigin = ref("");
const isPica = route.query.isPica === "true";
const tooltipRef = ref();
const mTooltipRef = ref();

const selectFlag = ref(false);
const selectNoFlag = ref(false);
const mSelectNoFlag = ref(false);

const { fullPath } = router.currentRoute.value;
const { third_categories_id, products_model } = route.query as any;

function removeLettersAndUnderscores(str: string): string {
	return str.replace(/[a-zA-Z_]/g, "");
}
const deal_third_categories_id = third_categories_id ? removeLettersAndUnderscores(third_categories_id) : "";
const userForm = ref({
	feedbackType: "",
	content: "",
	pageUrl: fullPath,
	imageUrl: "",
	comments: "",
	name: "",
	email: "",
	productModel: products_model,
	thirdCategoryId: deal_third_categories_id,
	isLike: 1,
	isShot: false,
	policy: false,
	upload_file: []
} as any);
const showTip = function () {
	if (screenWidth.value > 1024) {
		tooltipRef.value?.show();
		setTimeout(() => {
			tooltipRef.value?.hide();
		}, 5000);
		selectNoFlag.value = false;
	} else {
		mTooltipRef.value?.show();
		setTimeout(() => {
			mTooltipRef.value?.hide();
		}, 5000);
		mSelectNoFlag.value = false;
	}
	selectFlag.value = true;
	userForm.value.isLike = 1;
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Resource_Documentation_$Content Page",
			eventAction: `click_Evaluate_Yes`,
			eventLabel: `Evaluate_Yes`,
			nonInteraction: false
		});
};
const isShowFeedBackForm = ref(false);
const showDialog = function (isLike: number) {
	isShowFeedBackForm.value = true;
	userForm.value.isLike = isLike;
	if (screenWidth.value > 1024) {
		tooltipRef.value?.hide();
	} else {
		mTooltipRef.value?.hide();
	}
	if (isLike == 0) {
		if (screenWidth.value > 1024) {
			selectNoFlag.value = true;
			// setTimeout(() => {
			// 	selectNoFlag.value = false;
			// }, 200);
		} else {
			mSelectNoFlag.value = true;
			// setTimeout(() => {
			// 	mSelectNoFlag.value = false;
			// }, 200);
		}
		selectFlag.value = false;
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Resource_Documentation_$Content Page",
			eventAction: `click_Evaluate_No`,
			eventLabel: `Evaluate_No`,
			nonInteraction: false
		});
	} else {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Resource_Documentation_$Content Page",
			eventAction: `click_Feedback`,
			eventLabel: `Evaluate_Feedback`,
			nonInteraction: false
		});
	}
};
const gaPoint = function (isLike: number) {
	window.dataLayer.push({
		event: "uaEvent",
		eventCategory: "Resource_Documentation_$Content Page",
		eventAction: `click_SupportCase`,
		eventLabel: `Evaluate_SupportCase`,
		nonInteraction: false
	});
};
const { isLogin } = storeToRefs(userStore);
const loginInText = computed(() => {
	return `${localeLang("technicalDocuments.productSupport.loginDesc")}`.replace("AAAA", localeLink(`/login.html?redirect=${route.fullPath}`));
});
const policyText = computed(() => {
	return `${localeLang("technicalDocuments.productSupport.aggreePolicy")}`
		.replace("AAAA", localeLink("/policies/privacy_policy.html"))
		.replace("BBBB", localeLink("/policies/terms_of_use.html"));
});
const validate_apply_email = (value: string) => {
	let validateStr = "";
	if (!email_valdate.test(value)) {
		validateStr = localeLang("formValidate.validate.email.email_valid");
	}
	return validateStr;
};
const rules = ref({
	feedbackType: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.interest_type_error"),
			trigger: "change"
		}
	],
	content: [
		{
			required: true,
			message: localeLang("formValidate.form.errors.interest_type_error"),
			trigger: "change"
		}
	],
	email: [
		{
			type: "email",
			required: false,
			message: localeLang("formValidate.validate.email.email_valid"),

			// message: validate_apply_email(userForm.value.email),
			// isFunctionValidator: true,
			trigger: "blur"
		}
	],
	policy: [
		{
			required: true,
			trigger: "change",
			message: localeLang("formValidate.form.errors.interest_type_error"),
			validator: (_: any, val: boolean) => val === true
		}
	]
});
const supportCaseLink = computed(() => {
	if (website.value == "es" || website.value == "mx") {
		return localeLink("product_and_project_inquiry.html");
	}
	return "https://www.fs.com/product_and_project_inquiry.html";
});

const formRef = ref<any>();
// 加载中
const loadingRef = ref(false);

const isSummitSuccess = ref(false);

function close() {
	isSummitSuccess.value = false;
}

const isLoading = ref(false);

function shotSelected() {
	if (userForm.value.isShot) {
		if (!userForm.value.imageUrl) {
			isLoading.value = true;
			// const element = document.body; // 截图区域
			const element1 = document.querySelector("#product_support") as HTMLElement;
			html2canvas(element1)
				.then(canvas => {
					const imgData = canvas.toDataURL();
					userForm.value.imageUrl = imgData;
					isLoading.value = false;
					canvas.toBlob(
						blob => {
							// 由于 blob 可能为 null，这里进行判断，确保只传递有效的 Blob 给 File 构造函数
							if (blob) {
								const file = new File([blob], "captured-image.png", {
									type: "image/png",
									lastModified: new Date().getTime()
								});
								userForm.value.upload_file.push(file);
							}
						},
						"image/png",
						1.0
					); // 'image/png' 是 MIME 类型，1.0 是质量（0 到 1 之间）
				})
				.catch(err => {});
		}
	}
}

const isShowShotImg = ref(false);
function showShotImg() {
	isShowFeedBackForm.value = false;
	setTimeout(() => {
		isShowShotImg.value = true;
	}, 500);
}
function sureType(type: string) {
	userForm.value.feedbackType = type;
}
// 取消
function handlecancel() {
	userForm.value.feedbackType = "";
	userForm.value.content = "";
	userForm.value.pageUrl = fullPath;
	userForm.value.name = "";
	userForm.value.email = "";
	userForm.value.imageUrl = "";
	userForm.value.upload_file = [];
	userForm.value.productModel = products_model;
	userForm.value.thirdCategoryId = deal_third_categories_id;
	userForm.value.isLike = 1;
	userForm.value.isShot = false;
	userForm.value.policy = false;
	isShowFeedBackForm.value = false;
}
function editImg({ file, imgData }: { file: File | null; imgData: string }) {
	console.log("file==", file);
	console.log("imgData==", imgData);
	userForm.value.upload_file = [];
	userForm.value.upload_file.push(file);
	isShowShotImg.value = false;
	isShowFeedBackForm.value = true;
	userForm.value.imageUrl = imgData;
}
// 提交
function handleSubmit() {
	formRef.value?.validate().then(async (val: any) => {
		if (val) {
			loadingRef.value = true;
			const { recaptchaTp = false, headers = {} } = await getRecaptchaToken();
			if (!recaptchaTp) {
				loadingRef.value = false;
				return;
			}
			const paramsData = new FormData();
			paramsData.append("feedbackType", userForm.value.feedbackType);
			paramsData.append("content", userForm.value.content);
			paramsData.append("pageUrl", localeLink(userForm.value.pageUrl));
			paramsData.append("name", userForm.value.name);
			paramsData.append("email", userForm.value.email);
			if (isPica) {
				paramsData.append("thirdCategoryId", userForm.value.thirdCategoryId);
			} else {
				paramsData.append("productModel", userForm.value.productModel);
			}
			paramsData.append("isLike", userForm.value.isLike);
			if (userForm.value.upload_file && userForm.value.upload_file.length) {
				userForm.value.upload_file.forEach((file: any) => {
					paramsData.append("imageUrl[]", file);
				});
			}
			const { data: resData, error } = await useRequest.post("/cms/api/fs/doc/feedback/submit", { data: paramsData, headers });
			console.log("resData555==", JSON.stringify(resData.value));
			if (resData.value && resData.value.code === 200) {
				isShowFeedBackForm.value = false;
				isSummitSuccess.value = true;
				handlecancel();
				setTimeout(() => {
					isSummitSuccess.value = false;
				}, 3000);
			} else {
				resData.value.message && FsMessage({ message: resData.value.message, type: "danger", duration: 3000 });
			}
			loadingRef.value = false;
		}
	});
}

// 清空
const clearKeywords = () => {
	searchInp.value = "";
};
// 搜索
const searchKeyword = () => {
	if (searchInp.value) {
		loading.value = true;
		const url = localeLink(`/products_support/search.html?keyword=${searchInp.value}&page=1`);
		location.href = url;
	}
	gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "TDocContent_search", `Normal_${searchInp.value}`);
};
// 输入框输入关键字时，搜索结果
const getResult = debounce(async () => {
	if (!searchInp.value) {
		resultShow.value = false;
		resultList.value = [];
		return;
	}
	const { data, error } = await useRequest.post("/cms/api/fs/esResource/searchList", {
		data: {
			keyword: searchInp.value,
			page: 1,
			size: 10,
			isResult: 0,
			secondCategoryIds: [],
			firstCategoryIds: []
		}
	});
	if (data && data.value) {
		resultList.value = data.value.data.resourceList;
		resultShow.value = true;
	}
}, 300);

// 点击搜索条目，跳转文章详情
const searchListGo = (item: any) => {
	let url = `/products_support.html?isPica=${item.resourceType == 2}`;
	if (item.menu.length >= 1) {
		url += `&categories_id=${item.menu[0].id}`;
		if (item.menu.length >= 2) {
			url += `&third_categories_id=${item.menu[1].id}`;
			if (item.menu.length >= 3) {
				url += `&products_model=${item.menu[2].id}`;
				if (item.menu.length >= 4) {
					url += `&style_id=${item.menu[3].id}`;
					if (item.menu.length >= 5) {
						url += `&files_id=${item.menu[4].id}`;
						if (item.menu.length >= 6) {
							url += `&six=${item.menu[5].id}`;
							if (item.menu.length >= 7) {
								url += `&seven=${item.menu[6].id}`;
								if (item.menu.length >= 8) {
									url += `&eight=${item.menu[7].id}`;
								}
							}
						}
					}
				}
			}
		}
	}
	location.href = localeLink(url);
	resultShow.value = false;
};

// 面包屑点击事件
const handleClick = (e: MouseEvent, item: any) => {
	if (item.active) {
		return;
	}
	router.push(localeLink(item.path));
};
// 点击列表跳转
const goDetail = (item: any) => {
	emit("goTreeNode", item);
	if (route.query.isPica === "true") {
		if (!item.resourceTypeId) {
			gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "unfold_menu", item.data.name);
		}
	} else {
		if (item?.level) {
			if (item.level === 3) {
				gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "unfold_menu", item.name);
			} else if (item.level === 4) {
				gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_PN", item.name);
			} else if (item.level === 5) {
				gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_docType", item.name);
			} else if (item.level === 6) {
				gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_TDoc", item.name);
			}
		}
	}
};
const openPdf = (url: string | undefined, info: any) => {
	if (!url) return;
	let timestamp;
	if (["png", "jpg"].includes(url.slice(-3)) || url.slice(-4) === "jpeg") {
		timestamp = new Date().getTime();
		url += `?timestamp=${timestamp}`;
	}
	window.open(url, "_blank");
	gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "view_TDoc", `viewButton_${route.query.isPica === "true" ? info.fileName : info.file_name}`);
};
// 面包屑方法
const getBreadList = () => {
	breadcrumbData.value = [
		{
			name: localeLang("technicalDocuments.productSupport.home"),
			path: localeLink("/"),
			active: false
		},
		{
			name: localeLang("technicalDocuments.productSupport.documentation"),
			path: localeLink("/technical_documents.html"),
			active: false
		},
		{
			name: allProduct.value.name,
			path: localeLink(`/products_support.html?isPica=${route.query.isPica}&categories_id=${route.query.categories_id}`),
			active: false
		}
	];
	let url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
	route.query.third_categories_id &&
		breadcrumbData.value.push({
			name: secondCate.name,
			path: localeLink(`${url}&third_categories_id=${isPica ? secondCate.id : secondCate.categories_id}`),
			active: false
		});
	url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
	route.query.products_model &&
		breadcrumbData.value.push({
			name: product_model.name,
			path: localeLink(`${url}&products_model=${isPica ? product_model.id : product_model.products_model}`),
			active: false
		});
	url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
	route.query.style_id &&
		breadcrumbData.value.push({
			name: styleType.name,
			path: localeLink(`${url}&style_id=${isPica ? styleType.id : styleType.style_id}`),
			active: false
		});
	url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
	route.query.files_id &&
		breadcrumbData.value.push({
			name: isPica ? fileType.name : nowData.value.name,
			path: localeLink(`${url}&files_id=${isPica ? fileType.id : nowData.value.files_id}`),
			active: false
		});
	if (isPica) {
		url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
		route.query.six &&
			breadcrumbData.value.push({
				name: sixType.name,
				path: localeLink(`${url}&six=${sixType.id}`),
				active: false
			});
		url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
		route.query.seven &&
			breadcrumbData.value.push({
				name: sevenType.name,
				path: localeLink(`${url}&seven=${sevenType.id}`),
				active: false
			});
		url = breadcrumbData.value[breadcrumbData.value.length - 1].path;
		route.query.eight &&
			breadcrumbData.value.push({
				name: eightType.name,
				path: localeLink(`${url}&eight=${eightType.id}`),
				active: false
			});
	}
	breadcrumbData.value[breadcrumbData.value.length - 1].active = true;
};
// 获取datasheet详情方法
const getDatasheet = async () => {
	const params = {
		id: isPica ? nowData.value.articleId : nowData.value.files_id
		// id: 6838
		// id: 4792
		// id: 5010
	};
	const [{ data }, { data: metaData }] = await Promise.all([
		useRequest.post("/cms/api/fs/resource/detail", { params }),
		useRequest.post(`/api/metaTag/getMetaTag`, {
			data: {
				meta_type: 7,
				link: "products_support",
				meta_id: 0,
				params: {}
			}
		})
	]);
	// const { data } = await useRequest.post("/cms/api/fs/resource/detail", { params });
	isFeiShuResource.value = data?.value?.data.isFeiShuResource || false;
	fileInfo.value = data?.value?.data;
	if (isFeiShuResource.value) {
		let titList: Array<{ value: string; id: number }> = (fileInfo.value?.content?.data || []).map((item: ContentType) => {
			let str1: string = "";
			let str2: string = "";
			item.heading1?.elements?.forEach((elem: any) => {
				str1 = str1 + elem.text_run?.content;
			});
			item.heading2?.elements?.forEach((elem: any) => {
				str2 = str2 + elem.text_run?.content;
			});

			return {
				// value: item.heading1?.elements[0]?.text_run?.content.trim() || item.heading2?.elements[0]?.text_run?.content.trim() || "",
				value: str1 || str2 || "",
				id: item.block_type
			};
		});
		const idsToDelete = new Set<string>();
		titList.forEach(item => {
			if (!item.value) {
				idsToDelete.add(String(item.id));
			}
		});
		titList = titList.filter((item: { value: string; id: number }) => !idsToDelete.has(String(item.id)));
		titleList.value = organizeItems(titList);
	} else {
		isNewDatasheet.value = data?.value?.data.isNewDatasheet;
		if (fileInfo.value?.datasheet?.title?.length) {
			titleFirst.value = fileInfo.value.datasheet.title[0];
			titleList.value = fileInfo.value.datasheet.title.map((item, index) => {
				return {
					value: item,
					id: 0
				};
			});
		}
	}

	if (metaData?.value?.data && fileInfo.value) {
		metaData.value.data.title = fileInfo.value?.metaKeyword || "";
		metaData.value.data.description = fileInfo.value?.metaDesc || "";
		setMeta(metaData.value.data);
	}
	activeTitIndex.value = titleList.value[0]?.value;
	loading.value = false;
};

const scrollTo = (dom: HTMLElement, speed = 9, cb: () => void, diff = 0) => {
	const targetOffsetTop = getElementTop(dom) + diff;
	let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
	function scroll() {
		const dis = targetOffsetTop - scrollTop;
		scrollTop = scrollTop + dis / speed;
		if (Math.abs(dis) < 1) {
			document.body.scrollTop = targetOffsetTop;
			document.documentElement.scrollTop = targetOffsetTop;
			if (cb) {
				cb();
			}
		} else {
			document.body.scrollTop = scrollTop;
			document.documentElement.scrollTop = scrollTop;
			if (window.requestAnimationFrame) {
				requestAnimation = window.requestAnimationFrame(scroll);
			}
		}
	}
	if (window.requestAnimationFrame) {
		requestAnimation = window.requestAnimationFrame(scroll);
	}
};

const clickTitle = (item: any, index: number | string = "default", str: string) => {
	if (index === "default") {
		titleList.value.forEach((v, i) => {
			if (v.value == titleFirst.value) {
				clickFlag = true;
				activeTitIndex.value = v.value;
			}
		});
		titleNode.value.forEach(val => {
			if (titleFirst.value === val.innerText) {
				scrollTo(
					val,
					9,
					() => {
						clickFlag = false;
					},
					-9
				);
			}
		});
	} else {
		if (requestAnimation) {
			window.cancelAnimationFrame(requestAnimation);
			requestAnimation = null;
		}
		titleFirst.value = item.value;
		activeTitIndex.value = item.value;
		clickFlag = true;
		titleNode.value.forEach(val => {
			if (item.value === val.innerText.trim()) {
				if (screenWidth.value <= 1024) {
					scrollTo(
						val,
						9,
						() => {
							clickFlag = false;
						},
						-130
					);
				} else {
					scrollTo(
						val,
						9,
						() => {
							clickFlag = false;
						},
						-5
					);
				}
			}
		});
	}
};

const onScroll = () => {
	// 监听滚动事件
	const right = document.querySelector(".two_three_type .datasheet_preview");
	if (right) {
		if (isFeiShuResource.value) {
			titleNode.value = Array.from(right.querySelectorAll(".title-title-view"));
		} else {
			isNewDatasheet.value == 1
				? (titleNode.value = Array.from(right.querySelectorAll("div.title-title-view")))
				: (titleNode.value = Array.from(right.querySelectorAll("h2")) as HTMLElement[]);
		}
	}

	if (!clickFlag) {
		const arr: any = [];
		titleList.value.forEach(item => {
			arr.push(item.value);
			if (item.children?.length) {
				item.children.forEach((val: { value: any }) => {
					arr.push(val.value);
				});
			}
		});
		titleNode.value.forEach((item, index) => {
			if (screenWidth.value <= 960) {
				if (item.getBoundingClientRect().top <= 96 && arr.includes(item.innerText)) {
					activeTitIndex.value = item.innerText.trim();
				}
			} else if (screenWidth.value <= 1024 && arr.includes(item.innerText)) {
				if (item.getBoundingClientRect().top <= 54) {
					activeTitIndex.value = item.innerText.trim();
				}
			} else if (screenWidth.value > 1024 && arr.includes(item.innerText)) {
				if (item.getBoundingClientRect().top <= 6 && arr.includes(item.innerText)) {
					activeTitIndex.value = item.innerText.trim();
				}
			}
		});
	}
};

// 筛选所有父级并跳转
const filterAndJump = (item: { files_id?: any; id: any }) => {
	let url = `/products_support.html?isPica=${route.query.isPica}&categories_id=${route.query.categories_id}`;
	if (item.id === allProduct.value.id) {
		router.push(localeLink(url));
		return;
	}
	const allParNode: any = findParents(allProduct.value.children || [], item);
	const currentNode: any = findItemById(allProduct.value, item.id);
	allParNode.push(currentNode);

	const query = {};
	// if (allParNode.length >= 1 && !route.query.files_id) {
	if (allParNode.length >= 1) {
		url += `&third_categories_id=${isPica ? allParNode[0].id : allParNode[0].categories_id}`;
		if (allParNode.length >= 2) {
			url += `&products_model=${isPica ? allParNode[1].id : allParNode[1].products_model}`;
			if (allParNode.length >= 3) {
				url += `&style_id=${isPica ? allParNode[2].id : allParNode[2].style_id}`;
				if (allParNode.length >= 4) {
					url += `&files_id=${isPica ? allParNode[3].id : allParNode[3].files_id}`;
					if (isPica && allParNode.length >= 5) {
						url += `&six=${allParNode[4].id}`;
						if (allParNode.length >= 6) {
							url += `&seven=${allParNode[5].id}`;
						}
					}
				}
			}
		}
		router.push(localeLink(url));
		// } else if (route.query.files_id) {
		//  url = route.path;
		//  query = { ...route.query, files_id: item.files_id };
		//  router.push({ path: url, query });
	}
};
// 上一页、下一页数据
const getPreNext = (type: string = "") => {
	const allArr = flattenTree(allProduct.value.children, isPica);
	const id = nowData.value.id;
	const none = {
		name: localeLang("technicalDocuments.productSupport.none"),
		id: 0
	};
	if (route.query.third_categories_id) {
		allArr.forEach((item, index) => {
			if (allArr[index].id === id) {
				preArticle.value = allArr[index - 1]?.name ? allArr[index - 1] : none;
				nextArticle.value = allArr[index + 1]?.name ? allArr[index + 1] : none;
			}
			if (allArr[index].id === id && index === 0) {
				preArticle.value = {
					name: allProduct.value.name || localeLang("technicalDocuments.productSupport.none"),
					id: allProduct.value.id || 0
				};
			}
		});

		if (type === "pre" && preArticle.value.id) {
			filterAndJump(preArticle.value);
			gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "click_Previous", `Previous`);
		} else if (type === "next" && nextArticle.value.id) {
			filterAndJump(nextArticle.value);
			gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "click_Next", `Next`);
		}
	} else {
		preArticle.value = none;
		nextArticle.value = allArr[0]?.name ? allArr[0] : none;
		if (type === "next") {
			nextArticle.value.id
				? router.push(
						localeLink(
							`/products_support.html?isPica=${isPica}&categories_id=${route.query.categories_id}&third_categories_id=${isPica ? nextArticle.value.id : nextArticle.value.categories_id}`
						)
					)
				: "";
		}
	}
};
const toTop = () => {
	if (process.client) {
		const iframe = document.querySelector("#product_support .tree_box #tree_iframe") as HTMLElement;
		// 访问 iframe 的文档
		// const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
		const iframeDoc = (iframe as HTMLIFrameElement).contentDocument || (iframe as HTMLIFrameElement).contentWindow?.document;
		// 操作 iframe 内部的 DOM 元素
		const iframeElement = iframeDoc?.querySelector("#tree-container ul .selected .li_item");
		// console.log(123123111, iframeDoc, iframeElement);
		iframeElement &&
			iframeElement.scrollIntoView({
				behavior: "auto",
				block: "center"
			});
		const product_support = document.querySelector("#product_support") as HTMLElement;
		const top = product_support.offsetTop;
		const screenTop = product_support.getBoundingClientRect();
		if (screenWidth.value > 1024 && screenTop.top !== 0) {
			window.scrollTo(0, top);
		} else if (screenWidth.value <= 1024 && screenWidth.value > 768 && screenTop.top !== 0) {
			window.scrollTo(0, top - 48);
		}
	}
};
// const watchScroll = () => {
// 	// 监听滚动事件
// 	const right = document.querySelector(".two_three_type .datasheet_preview");
// 	if (right) {
// 		if (isFeiShuResource.value) {
// 			titleNode.value = Array.from(right.querySelectorAll(".title-title-view"));
// 		} else {
// 			isNewDatasheet.value == 1
// 				? (titleNode.value = Array.from(right.querySelectorAll("div.title-title-view")))
// 				: (titleNode.value = Array.from(right.querySelectorAll("h2")) as HTMLElement[]);
// 		}
// 		onScroll();
// 		window.addEventListener("scroll", onScroll);
// 	}
// };
const getNowData = async () => {
	loading.value = true;
	const { isPica, third_categories_id, products_model, style_id, files_id, six, seven, eight } = route.query;
	fileInfo.value = undefined;
	if (!third_categories_id && !products_model && !style_id && !files_id) {
		nowData.value = allProduct.value;
		openKeys.value = 0;
		loading.value = false;
		// 初始化二级分类目录卡片的展开与收起状态
		if (nowData.value.level === 2 && nowData.value.children) {
			nowData.value.children = nowData.value.children.map(function (item: TwoThreeType) {
				if (item.children && item.children?.length > 10) {
					item.is_show_more = true;
					item.is_more_select = false;
				} else {
					item.is_show_more = false;
					item.is_more_select = false;
				}
				return item;
			});
		}
	} else {
		if (isPica === "true") {
			openKeys.value = ((eight || seven || six || files_id || style_id || products_model || third_categories_id) as string) || 0;
			nowData.value = findItemById(allProduct.value, openKeys.value);
			if (third_categories_id) {
				secondCate = findItemById(allProduct.value, third_categories_id);
			}
			if (products_model) {
				product_model = findItemById(allProduct.value, products_model);
			}
			if (style_id) {
				styleType = findItemById(allProduct.value, style_id);
			}
			if (files_id) {
				fileType = findItemById(allProduct.value, files_id);
			}
			if (six) {
				sixType = findItemById(allProduct.value, six);
			}
			if (seven) {
				sevenType = findItemById(allProduct.value, seven);
			}
			if (eight) {
				eightType = findItemById(allProduct.value, eight);
			}
			if (nowData.value.resourceTypeId) {
				// 获取datasheet数据
				await getDatasheet();
				if (fileInfo.value) {
					nextTick(() => {
						useHeadMeta();
						// watchScroll();
						onScroll();
					});
				}
			}

			nowData.value.resourceTypeId
				? ""
				: setTimeout(() => {
						loading.value = false;
					}, 500);
		} else {
			// 当前右侧展示的内容
			if (third_categories_id) {
				allProduct.value.children?.forEach(item => {
					if (item.categories_id === Number(third_categories_id)) {
						secondCate = { ...item };
					}
				});
				openKeys.value = secondCate.id ?? 0;
				nowData.value = secondCate;
			}
			if (products_model) {
				secondCate.children?.forEach(item => {
					if (item?.products_model === products_model) {
						product_model = { ...item };
					}
				});
				openKeys.value = product_model.id ?? 0;
				nowData.value = product_model;
			}

			if (style_id) {
				product_model.children?.forEach(item => {
					if (item?.style_id === Number(style_id)) {
						styleType = { ...item };
					}
				});
				openKeys.value = styleType.id ?? 0;
				currentSort.value = { ...styleType };
				nowData.value = currentSort.value;
			}
			if (files_id) {
				currentSort.value.children?.forEach(async item => {
					if (item.files_id === Number(files_id)) {
						nowData.value = item;
						openKeys.value = nowData.value.id ?? 0;
						await getDatasheet();
						if (nowData.value.files_id && nowData.value.is_datasheet) {
							// 获取datasheet数据
							await getDatasheet();
							if (fileInfo.value?.datasheet) {
								nextTick(() => {
									useHeadMeta();
									// watchScroll();
									onScroll();
								});
							}
						}
					}
				});
			}
			files_id && nowData.value.is_datasheet
				? ""
				: setTimeout(() => {
						loading.value = false;
					}, 500);
		}
	}
};
const download = (url: any) => {
	if (!url) {
		// if (fileInfo.value?.fileUrl) {
		//  window.open(fileInfo.value?.pdfUrl || fileInfo.value?.fileUrl);
		//  // loading.value = true;
		//  // downloadPDF(fileInfo.value?.fileUrl, fileInfo.value?.fileName);
		// } else {
		//  exportSavePDF(fileInfo.value?.fileName || "");
		// }
		if (website.value === "cn" && fileInfo.value?.cnPdfUrl) {
			window.open(fileInfo.value?.cnPdfUrl);
		} else if (fileInfo.value?.pdfUrl) {
			window.open(fileInfo.value?.pdfUrl);
		} else if (fileInfo.value?.fileUrl) {
			window.open(fileInfo.value?.fileUrl);
		} else {
			exportSavePDF(fileInfo.value?.fileName || "");
		}
	} else {
		window.open(url);
	}
	gaDocumentation(
		`Resource_Documentation_${route.query.categories_id}`,
		"Download_TDoc",
		`downloadButton_${route.query.isPica === "true" ? fileInfo.value?.fileName : fileInfo.value?.file_name || ""}`
	);
};

function downloadPDF(url: any, filename: any) {
	fetch(url)
		.then(response => {
			if (!response.ok) {
				loading.value = false;
				throw new Error("Network response was not ok " + response.statusText);
			}
			return response.blob();
		})
		.then(blob => {
			// 创建一个隐藏的 a 标签
			const link = document.createElement("a");
			link.href = URL.createObjectURL(blob);
			link.download = filename || "download.pdf";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link); // 清理
			loading.value = false;
		})
		.catch(error => {
			loading.value = false;
			console.error("There has been a problem with your fetch operation:", error);
		});
}

const burryPoint = () => {
	window.dataLayer &&
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: "Resource_Technical Documents Detail Page",
			eventAction: "TDoc_operate",
			eventLabel: "Feedback",
			nonInteraction: false
		});
};

defineExpose({ clickTitle });

// await getNowData();
// getBreadList();
// getPreNext();
// onMounted(() => {
//  if (route.query.third_categories_id) {
//      toTop();
//  }
// });

const handleClickOutside = (event: any) => {
	// 点击输入框以外的区域隐藏结果列表
	if (!event.target.className.includes("input")) {
		resultShow.value = false;
	}
};

onMounted(() => {
	locationOrigin.value = window.location.origin;
	document.addEventListener("click", handleClickOutside);
	window.addEventListener("scroll", onScroll);
});

watch(
	() => route.query,
	async newVal => {
		console.log("watch_ppppp");
		if (newVal) {
			await getNowData();
			nextTick(() => {
				emit("upTree", openKeys.value);
				getPreNext();
				getBreadList();
				toTop();
			});
		}
	},
	{ immediate: true, deep: true }
);
watch(
	() => userStore.userInfo,
	val => {
		if (val) {
			const { customers_firstname = "", customers_email_address = "", customers_lastname = "", customers_telephone = "", customer_country_id = 0, customer_state = 0 } = val;
			userForm.value.name = customers_firstname + customers_lastname;
			userForm.value.email = customers_email_address;
		}
	},
	{
		immediate: true
	}
);
onBeforeUnmount(() => {
	document.removeEventListener("click", handleClickOutside);
	window.removeEventListener("scroll", onScroll);
	if (requestAnimation) {
		window.cancelAnimationFrame(requestAnimation);
		requestAnimation = null;
	}
});
</script>

<style lang="scss" scoped>
.cont {
	.sendFeedback {
		@include font13;
	}
}
.form-content {
	.login_desc {
		@include font13;
		color: #737272;
		margin-bottom: 16px;
	}
	.radio_box {
		display: flex;
		gap: 8px 24px;
		flex-wrap: wrap;
		align-content: flex-start;
		align-self: stretch;
		:deep(.fs-radio) {
			margin-right: 0;
			height: 20px;
			.fs-radio-box {
				gap: 8px;
				color: #19191a;
				.fs-radio__label {
					padding-left: 0;
					@include font13;
				}
				.fs-radio__icon {
					font-size: 14px !important;
					line-height: 20px !important;
				}
			}
		}
	}
	.feedbackBox {
		:deep(.fs-form__item--label) {
			margin-bottom: 8px;
		}
	}
	.areaText {
		margin-bottom: 16px;
	}
	.areaShot {
		:deep(.fs-form__item--label) {
			margin-bottom: 8px;
			.fs-checkbox {
				margin-right: 0;
				height: 20px;
			}
		}
		:deep(.fs-form__item--content) {
			.pic_div {
				position: relative;
				width: 100%;
				height: 80px;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 8px 12px;
				/* gap: 12px; */
				border-radius: 4px;
				background: #fafafb;
				box-sizing: border-box;
				border: 1px solid #e5e5e5;
				overflow: hidden;
				span {
					@include font13;
					color: $textColor2;
				}
				img {
					max-width: 100%;
				}
				.fs-button {
					span {
						@media (max-width: 768px) {
							color: $textColor2;
						}
					}
					background: #ccc;
					position: absolute;
					right: 0;
					top: 0;
					border-radius: 0px 4px 0px 0px;
					box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
					height: 24px;
					padding: 0px 4px;
					gap: 4px;
					background: #fff;
					color: #707070;
					.iconfont {
						font-size: 16px;
					}
					.fs-button--prefix {
						@include font12;
						margin-left: 0;
					}
					// &:hover {
					// 	opacity: 1;
					// 	span {
					// 		text-decoration: none;
					// 		color: $textColor1;
					// 	}
					// }
				}
				&:hover {
					.fs-button {
						background: rgba(25, 25, 26, 0.04);
						box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
						span {
							text-decoration: none;
							color: $textColor1;
						}
					}
				}
			}
			.pic_div_back {
				background: #fff;
			}
			.shadow_div {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				display: none;
			}
			.shadow_back {
				background: linear-gradient(0deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.08));
				display: block;
			}
		}
		.shot_cont {
			display: flex;
			flex-direction: column;
			gap: 8px;
			.shot_box {
				display: flex;
				flex-direction: row;
				align-items: center;
				.fs-checkbox {
					margin-right: 0;
					height: 20px;
				}
				.agree {
					@include font13;
				}
			}
		}
	}
	.line {
		width: 100%;
		height: 1px;
		background: #e5e6eb;
		margin-bottom: 16px;
	}
	.contact_title {
		margin-bottom: 4px;
	}
	.contact_info {
		:deep(.fs-form__item) {
			.fs-form__item--content {
				.is-newRegular {
					background-color: #f6f6f8;
				}
			}
		}
	}
	.flex1 {
		margin-bottom: 0;
		.policy_box {
			display: flex;
			flex-direction: row;
			align-items: center;
			.fs-checkbox {
				margin-right: 0;
				height: 20px;
			}
			.agree {
				@include font13;
				color: #707070;
			}
		}
	}
}
.success-content {
	display: flex;
	flex-direction: row;
	gap: 20px;
	.text_div {
		display: flex;
		padding: 0px;
		gap: 8px;
		flex-grow: 1;
		span {
			font-size: 20px;
			color: #10a300;
		}
		.cont_div {
			display: flex;
			flex-direction: column;
			gap: 12px;
			flex-grow: 1;
			.tit {
				@include font16;
			}
			.des {
				@include font14;
				color: $textColor2;
			}
		}
	}
	.pic {
		width: 16px;
		height: 24px;
		span {
			font-size: 16px;
		}
	}
}
.form_footer {
	:deep(.is-plain) {
		border: 0;
		color: #707070;
		&:hover {
			background: #f2f2f2;
			color: #19191a;
		}
	}
}
.backStyle {
	background: rgba(25, 25, 26, 0.04);
	.fs-button {
		color: $textColor2 !important;
	}
	.sendFeedback {
		@media (max-width: 768px) {
			color: #0060bf !important;
		}
	}
}
.noBackStyle {
	background: rgba(25, 25, 26, 0.04);
	color: $textColor2 !important;
}
.tip_tit {
	@include font13;
	color: $textColor2;
}
.two_three_type {
	width: 100%;
	height: 100%;
	display: flex;
	border-left: 1px solid #e5e5e5;
	.center_content {
		width: calc(100% - 258px);
		height: 100%;
		padding: 20px 24px 24px;
		border-right: 1px solid #e5e5e5;
		.breads {
			width: 100%;
			position: relative;
			:deep(.fs-breadcrumb) {
				flex-wrap: wrap;
				overflow: auto;
				.fs-breadcrumb-item {
					flex-shrink: 0;
					display: flex;
					flex-wrap: nowrap;
					&:hover {
						cursor: default;
					}
					&:not(:last-child) {
						span:first-child {
							cursor: pointer;
						}
					}
					span:first-child:hover {
						color: #19191a;
					}
					.fs-breadcrumb-item__separator {
						width: 12px;
						text-align: center;
					}
				}
			}
			.scroll_mask {
				display: none;
			}
		}
		.inp_box {
			position: relative;
			max-width: 520px;
			margin-top: 20px;
			:deep(.fs-input) {
				.fs-input__wrapper {
					padding: 10px 7px 10px 12px;
				}
				.options_box {
					display: flex;
					align-items: center;
					.iconfont_close {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 28px;
						height: 28px;
						font-size: 16px;
						line-height: 1;
						cursor: pointer;
						color: #646466;
						padding: 6px;
						border-radius: 4px;
						&:hover {
							background-color: rgba(25, 25, 26, 0.04);
							color: $textColor1;
						}
					}
					.iconfont_search {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 28px;
						height: 28px;
						font-size: 16px;
						line-height: 1;
						margin-left: 12px;
						padding: 6px;
						border-radius: 4px;
						cursor: pointer;
						color: #646466;
						&:hover {
							background-color: rgba(25, 25, 26, 0.04);
							color: $textColor1;
						}
					}
				}
			}
			.search_res {
				position: absolute;
				top: 42px;
				left: 0;
				width: 100%;
				max-height: 480px;
				border-radius: 0px 0px 4px 4px;
				border: 1px solid $borderColor1;
				border-top: 0;
				padding: 8px 0;
				background: #fff;
				overflow-y: scroll;
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
				z-index: 2;

				li {
					color: $textColor1;
					padding: 8px 16px;
					cursor: pointer;
					&:hover {
						background: #f7f7f7;
					}
					.tit_div {
						.tit {
							@include font14;
							display: inline;
						}
						.tag_img {
							display: inline-flex;
							margin-left: 8px;
							vertical-align: middle;
						}
					}
					.txt {
						@include font12;
						margin-top: 8px;
						@include textClampOverflow(2);
					}
					:deep(span) {
						background: #eee;
					}
				}
				.no_res {
					@include font14;
					color: $textColor2;
					padding: 4px 12px;
					margin: 9px 0;
					text-align: center;
				}
			}
		}
		.tit_tag {
			margin-top: 20px;
		}
		.tit {
			@include font24;
			font-weight: 600;
			color: $textColor1;
			display: inline;
			word-break: break-word;
		}
		.tag_img {
			display: inline-flex;
			width: 30px;
			height: 22px;
			vertical-align: sub;
			margin-left: 8px;
		}
		.down_box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			margin-top: 20px;
			.update {
				@include font12;
				color: $textColor2;
			}
			.btns {
				display: flex;
				@include font13;
				margin-left: 20px;
				.product {
					color: $textColor1;
				}
				.community {
					color: $textColor1;
					margin-left: 20px;
				}
				.line {
					width: 1px;
					height: 12px;
					background: $bgColor7;
					margin: auto 12px;
				}
				color: $textColor5;
				.download {
					cursor: pointer;
					span {
						&:hover {
							text-decoration: underline;
						}
					}
				}
				.download {
					display: flex;
					align-items: center;
					.iconfont {
						display: inline-block;
						width: 16px;
						height: 16px;
						line-height: 16px;
						font-size: 16px;
						margin-right: 4px;
					}
				}
			}
		}
		.categoryCont {
			border-top: 1px solid #e5e5e5;
			padding-top: 20px;
			padding-bottom: 4px;
			margin-top: 40px;
			column-count: 2;
			column-gap: 20px;
			.third_card {
				border-radius: 8px;
				border: 1px solid #e5e5e5;
				margin-bottom: 20px;
				padding: 16px 24px;
				break-inside: avoid;
				.third_tit {
					@include font16;
					font-weight: 600;
					color: $textColor1;
					margin-bottom: 12px;
				}
				.forth_card {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					gap: 8px;
					li {
						p {
							width: fit-content;
							@include font14;
							color: $textColor2;
							&:hover {
								text-decoration: underline;
								cursor: pointer;
							}
						}
					}
				}
				.more_btn {
					margin-top: 12px;
					:deep(span) {
						z-index: auto;
					}
				}
			}
		}
		.ulCont {
			padding: 40px 0 24px;
			li {
				position: relative;
				padding: 0 12px 10px 24px;
				width: max-content;
				max-width: 100%;
				&:hover {
					p {
						text-decoration: underline;
					}
				}
				.dot {
					position: absolute;
					top: 9px;
					left: 12px;
					width: 4px;
					height: 4px;
					border-radius: 50%;
					background: #707070;
				}
				a,
				p {
					@include font14;
					color: $textColor5;
					cursor: pointer;
				}
			}
		}
		.card {
			display: flex;
			justify-content: space-between;
			align-items: center;
			max-width: 480px;
			padding: 24px;
			border: 1px solid #e5e5e5;
			border-radius: 4px;
			&.mt {
				margin-top: 16px;
			}
			&:hover {
				cursor: pointer;
				transition: 0.3s all;
				box-shadow: 0px 15px 15px -10px rgba(0, 0, 0, 0.1);
				.btn {
					text-decoration: underline;
				}
			}
			.cont {
				display: flex;
				.iconfont {
					display: flex;
					width: 32px;
					height: 32px;
					font-size: 32px;
					margin-top: 4px;
					margin-right: 16px;
				}
				h4 {
					@include font14;
					font-weight: 600;
					margin-bottom: 6px;
				}
				p {
					@include font13;
					color: #89898c;
				}
			}
			.btn {
				flex-shrink: 0;
				@include font14;
				color: #0060bf;
				margin-left: 22px;
			}
		}
		.pdfCont {
			padding: 24px 0;
			color: $textColor1;
			.pdfTit {
				@include font20;
				font-weight: 600;
			}
			.pdfDesc {
				@include font14;
				margin: 16px 0;
			}
		}
		.line {
			border-top: 1px solid #e5e5e5;
			margin: 16px 0;
		}
		.btn_box {
			display: flex;
			justify-content: space-between;
			> div {
				display: flex;
				align-items: center;
				@include font14;
				color: $textColor1;
				cursor: pointer;
				&:hover .txt {
					text-decoration: underline;
				}
				&.def {
					cursor: text;
					&:hover .txt {
						text-decoration: none;
					}
				}
			}
			.iconfont {
				display: flex;
				justify-content: center;
				align-items: center;
				@include font16;
				width: 16px;
				height: 16px;
			}
			.pre_box {
				.txt {
					margin-left: 8px;
				}
				.iconfont {
					transform: rotateY(-180deg);
				}
			}
			.next_box {
				.txt {
					margin-right: 8px;
				}
			}
		}
		.feedback_info_m {
			display: none;
			flex-direction: column;
			gap: 20px;
			padding: 24px 0;
			margin-top: 16px;
			@media (max-width: 768px) {
				margin-top: 8px;
			}
			background: #f7f7f7;
			.feedback_div_m {
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin: 0 16px;
				gap: 12px;
				.tit {
					@include font14;
					color: $textColor1;
					font-weight: normal;
					@media (max-width: 768px) {
						margin-top: 0;
					}
				}
				.cont_m {
					display: flex;
					align-items: center;
					.yes_no_m {
						display: flex;
						.yes,
						.no {
							display: flex;
							justify-content: center;
							align-items: center;
							padding: 6px 12px;
							height: 32px;
							color: #19191a;
							border: 1px solid #cccccc;
							@include font12;
						}
						.no {
							margin-left: 12px;
						}
					}
					.feedback {
						cursor: pointer;
						display: flex;
						align-items: center;
						color: $textColor5;
						text-decoration: none;
						margin-left: 24px;
						.iconfont {
							display: inline-block;
							width: 16px;
							height: 16px;
							line-height: 16px;
							font-size: 16px;
							margin-right: 4px;
						}
						.txt {
							@include font13;
						}
						&:hover {
							.txt {
								text-decoration: underline;
							}
						}
					}
				}
			}
			.contact_fs {
				display: flex;
				flex-direction: column;
				gap: 4px;
				margin: 0 16px;
				.tit {
					@include font14;
					color: $textColor13;
					font-weight: normal;
				}
				.case {
					@include font13;
					color: $textColor5;
				}
			}
		}
	}
	.right_tit {
		position: sticky;
		top: 0;
		padding: 20px 24px;
		height: max-content;
		flex: 1;
		flex-shrink: 0;
		width: 258px;
		h4 {
			@include font16;
			font-weight: 600;
		}
		.tit_box {
			@include font14;
			color: $textColor2;
			margin-top: 16px;
		}
		.intro {
			position: relative;
			cursor: pointer;
			&:not(:first-child) {
				padding-top: 12px;
			}
			&:first-child {
				span {
					top: 9px;
				}
			}
			&:hover p {
				color: #0060bf;
			}
			p {
				margin-left: 12px;
				word-break: break-word;
			}
			span {
				position: absolute;
				top: 21px;
				left: 0;
				width: 4px;
				height: 4px;
				background: #707070;
				border-radius: 50%;
			}
			.activeTitIndex {
				color: $textColor5;
			}
			&.intros {
				cursor: auto;
				p {
					@include font14;
				}
			}
		}
		.line_right {
			width: 100%;
			height: 1px;
			background: #e5e5e5;
			margin-top: 24px;
		}
		.feedback_info {
			display: flex;
			flex-direction: column;
			gap: 20px;
			margin-top: 24px;
			.feedback_div {
				display: flex;
				flex-direction: column;
				justify-content: center;
				gap: 12px;
				.tit {
					@include font14;
					color: $textColor1;
				}
				.cont {
					display: flex;
					align-items: center;
					.yes_no {
						display: flex;
						.yes,
						.no {
							display: flex;
							justify-content: center;
							align-items: center;
							padding: 6px 12px;
							height: 32px;
							color: #19191a;
							border: 1px solid #cccccc;
							@include font12;
						}
						.no {
							margin-left: 12px;
						}
					}
					.feedback {
						cursor: pointer;
						display: flex;
						align-items: center;
						color: $textColor5;
						text-decoration: none;
						margin-left: 24px;
						.iconfont {
							display: inline-block;
							width: 16px;
							height: 16px;
							line-height: 16px;
							font-size: 16px;
							margin-right: 4px;
						}
						.txt {
							@include font13;
						}
						&:hover {
							.txt {
								text-decoration: underline;
							}
						}
					}
				}
			}
			.contact_fs {
				display: flex;
				flex-direction: column;
				gap: 4px;
				.tit {
					@include font14;
					color: $textColor13;
				}
				.case {
					@include font13;
					color: $textColor5;
				}
			}
		}
		.child_tit {
			border-left: 1px solid #e5e5e5;
			padding-left: 24px;
			margin-top: 4px;
			li {
				padding: 6px 0;
				&:first-child {
					padding-top: 4px;
				}
				&:last-child {
					padding-bottom: 4px;
				}
			}
		}
	}

	@media (max-width: 1024px) {
		.center_content {
			width: 100%;
			.feedback_info_m {
				display: flex;
			}
		}
		.right_tit {
			display: none;
		}
	}
	@media (max-width: 768px) {
		border: none;
		.center_content {
			padding: 16px;
			border: none;
			.tit {
				@include font24;
				margin-top: 16px;
			}
			.down_box {
				flex-direction: column;
				align-items: flex-start;
				margin-top: 12px;
				.btns {
					margin-left: 0;
					margin-top: 12px;
				}
			}
			.categoryCont {
				border-top: none;
				padding-top: 0;
				padding-bottom: 0px;
				margin-top: 16px;
				column-count: 1;
				.third_card {
					margin-bottom: 16px;
					padding: 16px;
					&.last_third_card {
						margin-bottom: 0;
					}
					.third_tit {
						@include font14;
					}
				}
			}
			.ulCont {
				margin-top: 16px;
				padding: 16px 0;
			}
			.btn_box {
				flex-direction: column;
				> div {
					@include font12;
					padding: 4px 0;
				}
				.iconfont {
					display: none;
				}
				.pre_box {
					.txt {
						margin-left: 0;
					}
				}
			}
			.breads {
				scrollbar-width: none; // 火狐
				-ms-overflow-style: none; // ie
				&::-webkit-scrollbar {
					display: none;
				}
				.fs-breadcrumb {
					flex-wrap: nowrap;
					scrollbar-width: none; // 火狐
					-ms-overflow-style: none; // ie
					&::-webkit-scrollbar {
						display: none;
					}
				}
				.scroll_mask {
					background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.6) 68%, rgba(255, 255, 255, 0) 100%);
					display: block;
					pointer-events: none;
					position: absolute;
					right: -1px;
					top: 0;
					bottom: 0;
					width: 40px;
					z-index: 20;
				}
			}
			.pdfCont {
				.card {
					flex-direction: column;
					align-items: flex-start;
					padding: 16px;
					.cont {
						h4 {
							margin-bottom: 8px;
						}
					}
					.btn {
						margin-top: 12px;
						margin-left: 48px;
					}
				}
			}
		}
		.inp_box {
			display: none;
		}
	}
}
</style>
<style lang="scss">
.fs-dialog {
	.successDialog {
		border-radius: 3px;
		.fs-dialog__content--header {
			display: none;
		}
	}
	.shotImgDialog {
		width: auto !important;
		height: calc(100% - 240px) !important;
		border-radius: 0;
		@media (max-width: 768px) {
			width: 100% !important;
			max-height: 100% !important;
			height: auto !important;
		}
		.fs-dialog__contentM--header {
			margin: 0;
		}
		.fs-dialog__contentM--box {
			padding: 0;
		}
		.fs-dialog__content--header {
			display: none;
		}
		.fs-dialog__content--box {
			padding: 0;
			width: 100%;
			height: 100%;
			overflow-y: hidden;
			.imgMarking {
				> img {
					width: 100%;
					height: auto;
				}
				.btn_box {
					width: 216px;
					height: 36px;
					display: flex;
					position: fixed;
					justify-content: center;
					align-items: center;
					margin-top: 10px;
					justify-self: anchor-center;
					background: #fff;
				}
			}
		}
	}
	.feedback_form {
		.fs-dialog__contentM--box {
			padding: 20px 16px 16px 16px !important;
		}
		.fs-dialog__footerM {
			padding: 16px;
		}
	}
	.fs-dialog__footerM {
		.form_footer {
			display: flex;
			flex-direction: column-reverse;
			gap: 12px;
			.fs-button {
				margin-left: 0;
			}
			.is-plain {
				border-radius: 4px;
				background: #f2f2f2;
			}
		}
	}
}
</style>
