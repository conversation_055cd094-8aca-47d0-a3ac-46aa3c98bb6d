<template>
	<div class="left_tree">
		<div class="title_top">
			<div class="titbox">
				<h3 title="Documentation" @click="goTechnical">
					{{ localeLang("technicalDocuments.productSupport.documentation") }}
				</h3>
			</div>
			<span class="iconbox" @mouseenter="showMenuList" @mouseleave="hideMenuList">
				<span class="iconfont menuIcont">&#xe725;</span>
				<div v-show="menuListShow" class="menu_list" :style="menuListStyle">
					<div class="menu_cont">
						<div class="inp_box">
							<FsInput
								v-model.trim="menuSearch"
								:placeholder="localeLang('technicalDocuments.productSupport.menuHolder')"
								@focus="gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, 'hamburgerMenus_search_input', 'Hamburger Menus_Search Input')" />
							<span class="iconfont">&#xe694;</span>
						</div>
						<div class="page-main">
							<div class="card">
								<div v-for="(item, index) in filterMenuList" :key="index" class="card-item">
									<h4 class="text">{{ item.name || item.styleName }}</h4>
									<div
										v-for="(t, i) in item.children"
										:key="i"
										@click="
											gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, 'click_hamburgerMenus_category', `Hamburger Menus_${t.styleId ? t.styleName : t.name}`)
										">
										<template v-if="t.styleId">
											<a :href="localeLink(`/products_support.html?isPica=true&categories_id=${t.styleId}`)">{{ t.styleName }}</a>
										</template>
										<template v-else>
											<a :href="localeLink(`/products_support.html?isPica=false&categories_id=${t.categories_id}`)">{{ t.name }}</a>
										</template>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</span>
		</div>
		<div class="secondcat">
			<h3 :title="allProduct.name || allProduct.styleName" @click="goToSecond">
				{{ allProduct.name || allProduct.styleName }}
			</h3>
		</div>
		<div class="inp_box left_search">
			<!-- <FsInput
				v-model="searchInp"
				:placeholder="localeLang('technicalDocuments.productSupport.treeHolder')"
				@input="leftSearch"
				@focus="gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, 'menu_search_input', 'menu_search Input')"
				@blur="(leftSearchBlur(), gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, 'menu_search', `Normal_${searchInp}`))" /> -->
			<FsInput v-model="searchInp" :placeholder="localeLang('technicalDocuments.productSupport.treeHolder')" @input="leftSearch" />
			<span class="iconfont">&#xe694;</span>
			<div v-show="leftSearchShow" class="search_res">
				<ul v-if="leftResList.length">
					<li v-for="(item, index) in leftResList" :key="index" @click="searchListGo(item)" v-html="item.name"></li>
				</ul>
				<div v-else class="no_res">{{ localeLang("technicalDocuments.productSupport.noResult") }}</div>
			</div>
		</div>
		<div v-loading="treeLoading" class="tree_box">
			<iframe id="tree_iframe" ref="iframe" src="/tree.html" style="width: 100%; height: 100%"></iframe>
		</div>
		<div v-show="menuListShow" class="header_mask" :style="menuListStyle"></div>
	</div>
</template>

<script setup lang="ts">
import { FsInput } from "fs-design";
import { ref } from "vue";
import useLeftTree from "./useLeftTree";
import fixScroll from "@/utils/fixScroll";
import { gaDocumentation } from "@/utils/burialPoint";
const { allProduct, menuListStyle, menuSearch, filterMenuList, menuListShow, searchInp, leftResList, leftSearchShow, leftSearch, leftSearchBlur, searchListGo, isPica } =
	useLeftTree();

const localeLink = useLocaleLink();
const localeLang = useLocaleLang();
const route = useRoute();
const router = useRouter();

const openKeys = ref(inject("openKeys") as number);
const iframe = ref();

const treeLoading = ref(false);

// const emit = defineEmits<{
// 	(e: "goTreeNode", obj: any): void;
// }>();
const emits = defineEmits(["goTreeNode", "mouseenterMenu", "mouseleaveMenu"]);

// pc端展示hover右侧内容
const showMenuList = () => {
	menuListShow.value = true;
	fixScroll.fixed();
	emits("mouseenterMenu");
	gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "click_hamburgerMenus", "Hamburger Menus");
};
const hideMenuList = () => {
	menuListShow.value = false;
	fixScroll.unfixed();
	emits("mouseleaveMenu");
};
const goToSecond = () => {
	if (!route.query.third_categories_id) return;
	const url = `/products_support.html?isPica=${isPica}&categories_id=${route.query.categories_id}`;
	router.push(localeLink(url));
};
const goTechnical = () => {
	location.href = localeLink("/technical_documents.html");
	gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "click_homeTitle", "homeTitle");
};

const updateTreeData = (data: any, openKeys = 0) => {
	iframe.value.contentWindow.postMessage(JSON.stringify({ type: "updateTreeData", data, openKeys }), "*");
};

const initializeTree = (openKeys: any) => {
	updateTreeData(allProduct.value, openKeys);
};
// watch(openKeys, newId => {
// 	nextTick(() => {
// 		updateTreeData(allProduct.value, newId);
// 	});
// });

onMounted(() => {
	treeLoading.value = true;
	// initializeTree(0);
	// 监听来自 iframe 的消息
	window.addEventListener("message", event => {
		if (event.origin !== window.location.origin) return;
		// 		// 处理接收到的消息
		if (event.data.type === "nodeClick") {
			emits("goTreeNode", event.data.data);
			if (route.query.isPica === "true") {
				if (!event.data.data.resourceTypeId) {
					gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "unfold_menu", event.data.data.name);
				}
			} else {
				if (event.data.data?.level) {
					if (event.data.data.level === 3) {
						gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "unfold_menu", event.data.data.name);
					} else if (event.data.data.level === 4) {
						gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_PN", event.data.data.name);
					} else if (event.data.data.level === 5) {
						gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_docType", event.data.data.name);
					} else if (event.data.data.level === 6) {
						gaDocumentation(`Resource_Documentation_${route.query.categories_id}`, "select_TDoc", event.data.data.name);
					}
				}
			}
			console.log(9527999);
		}
		if (event.data.type === "loadingComplete") {
			treeLoading.value = false;
		}
		// console.log(123123, event.data, "loadingComplete", treeLoading.value);
	});
});
defineExpose({
	initializeTree
});
</script>

<style lang="scss" scoped>
.left_tree {
	height: 100%;
	.title_top {
		width: 100%;
		position: relative;
		.titbox {
			width: 100%;
			padding: 20px 70px 20px 24px;
			border-bottom: 1px solid #e5e5e5;
			h3 {
				@include font14;
				font-weight: 600;
				cursor: pointer;
			}
		}
		.iconbox {
			position: absolute;
			top: 0;
			bottom: 0;
			right: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0 24px;
			&:hover {
				cursor: pointer;
				.menuIcont {
					color: $textColor5;
					transform: rotate(90deg);
				}
			}
			.menuIcont {
				color: $textColor2;
				font-size: 22px;
				width: 22px;
				height: 22px;
				transition: all 0.3s linear;
			}
		}
		a {
			@include font18;
			font-weight: 600;
			color: $textColor1;
			&:hover {
				text-decoration: none;
			}
		}
	}
	.secondcat {
		padding: 20px 24px 4px;
		h3 {
			@include font16;
			font-weight: 600;
			cursor: pointer;
		}
	}
	.inp_box {
		padding: 0 24px;
		position: relative;
		:deep(.fs-input) {
			.fs-input__wrapper {
				padding-left: 24px;
				border-radius: 0;
			}
			&.is-focus {
				.fs-input__wrapper {
					border-bottom: 1px solid #0060bf;
				}
			}
		}
		.iconfont {
			position: absolute;
			top: 13px;
			left: 25px;
			width: 16px;
			height: 16px;
			font-size: 16px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: $textColor2;
		}
	}
	.inp_box.left_search {
		:deep(.fs-input) {
			.fs-input__wrapper {
				padding-left: 0;
				padding-right: 36px;
			}
			&.is-focus {
				.fs-input__wrapper {
					border-bottom: 1px solid #0060bf;
				}
			}
			.fs-input__wrapper {
				border-color: transparent;
				border-bottom: 1px solid #e5e5e5;
			}
		}
		.iconfont {
			top: 7px;
			left: auto;
			right: 24px;
			width: 28px;
			height: 28px;
			padding: 6px;
			&:hover {
				cursor: default;
			}
		}
		:deep(.search_res) {
			position: absolute;
			top: 42px;
			left: 24px;
			width: 252px;
			max-height: 280px;
			padding: 8px 0;
			background: #fff;
			overflow-y: scroll;
			box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
			z-index: 2;
			li {
				@include font13;
				color: $textColor1;
				padding: 6px 20px;
				cursor: pointer;
				&:hover {
					background: #f7f7f7;
				}
			}
			.highlight {
				background: #eee;
			}
			.no_res {
				@include font14;
				color: $textColor2;
				padding: 4px 12px;
				margin: 9px 0;
				text-align: center;
			}
		}
	}
	.tree_box {
		height: calc(100% - 153px);
		:deep(.fs-tree) {
			.fs-treeNode {
				padding-left: 16px;
				&.layer__0 {
					padding-left: 0;
				}
			}
			.layer__3 {
				padding-left: 12px;
			}
			.fs-treeNode__box {
				padding: 10px 8px 10px 4px;
				.iconfont {
					color: #ccc;
				}
				&.selected {
					.iconfont {
						color: $textColor5;
					}
				}
			}
		}
		&.spe_tree {
			:deep(.fs-tree) {
				.fs-treeNode {
					&.layer__0 {
						> li > .fs-treeNode__box > .iconfont {
							display: none;
						}
					}
				}
			}
		}
	}
	.menu_list {
		position: fixed;
		top: 101px;
		left: 300px;
		width: 65vw;
		max-width: 1200px;
		height: calc(100vh - 101px);
		transition: all 130ms ease-in-out;
		border-left: 1px solid #e5e5e5;
		background: #fff;
		z-index: 60;
		.menu_cont {
			padding: 16px 32px;
			height: 100%;
			overflow-y: scroll;
			position: relative;
			cursor: default;
			.inp_box {
				padding: 0;
				.iconfont {
					left: 1px;
				}
				:deep(.fs-input) {
					.fs-input__wrapper {
						border-color: transparent;
						border-bottom: 1px solid #e5e5e5;
					}
					&.is-focus {
						.fs-input__wrapper {
							border-bottom: 1px solid #0060bf;
						}
					}
				}
			}
			.page-main {
				margin-top: 20px;
				background: #ffffff;
				.card {
					column-count: 3; // 定义三列
					column-gap: 40px; // 列与列的距离为20px
					.card-item {
						max-width: 352px;
						width: 100%;
						grid-row-start: auto;
						margin-bottom: 16px;
						break-inside: avoid; // 不被截断
						.text {
							@include font14;
							padding: 4px 0;
							color: $textColor1;
							font-weight: 600;
							margin-bottom: 8px;
						}
						div {
							padding: 5px 0;
						}
						a {
							@include font12;
							color: $textColor2;
							font-weight: 400;
							&:hover {
								text-decoration: underline;
							}
						}
					}
				}
			}
		}
	}
	.header_mask {
		position: fixed;
		top: 101px;
		left: 300px;
		width: calc(100vw - 300px);
		height: calc(100vh - 101px);
		z-index: 59;
		background: rgba(51, 51, 51, 0.3);
	}
	@media (max-width: 1024px) {
		.tree_box {
			height: calc(100% - 134px);
		}
		.menu_list {
			width: calc(100vw - 340px);
			height: calc(100vh - 82px);
			top: 82px;
		}
		.header_mask {
			top: 82px;
			height: calc(100vh - 82px);
		}
	}
}
@media (max-width: 768px) {
	.left_tree {
		display: none;
	}
}
</style>
./useLeftTree
