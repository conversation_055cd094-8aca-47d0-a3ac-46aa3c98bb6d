<template>
	<div v-loading.fullscreen="loading" class="technical_banner">
		<div class="banner_main">
			<div class="title">{{ localeLang("technicalDocuments.title") }}</div>
			<div class="input_box">
				<FsInput
					v-model.trim="keywords"
					class="input"
					:placeholder="localeLang('technicalDocuments.placeholder')"
					@keydown="searchKeyword"
					@input="getResult"
					@focus="gaDocumentation('Resource_Documentation Page', 'TDoc_search', 'Search Input')">
					<template #suffix>
						<div class="options_box">
							<span v-if="keywords.length > 0" class="iconfont iconfont_close" @click="clearKeywords">&#xf30a;</span>
							<span class="iconfont iconfont_search" @click.stop="searchKeyword">&#xe694;</span>
						</div>
					</template>
				</FsInput>
				<div v-show="resultShow" class="search_res">
					<ul v-if="resultList.length">
						<li v-for="(item, index) in resultList" :key="index" @click.stop="searchListGo(item)">
							<div class="tit_div">
								<p class="tit" v-html="item.resourceName"></p>
								<img v-if="item.isShowNew" class="tag_img" src="https://resource.fs.com/mall/generalImg/202407251649182wwhwk.svg" alt="image" />
							</div>
							<p v-if="item.preContent || item.postContent" class="txt" v-html="item.preContent + item.postContent"></p>
						</li>
					</ul>
					<div v-else class="no_res">{{ localeLang("technicalDocuments.productSupport.noResult") }}</div>
				</div>
				<GptButton />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { FsInput } from "fs-design";
import type { ResourceListItem } from "../../ProductSupport/types";
import GptButton from "./GptButton.vue";
import { gaDocumentation } from "@/utils/burialPoint";
defineOptions({
	name: "Banner"
});
const websiteStore = useWebsiteStore();

const { website } = storeToRefs(websiteStore);

const isShowGpt = computed(() => ["en", "au", "sg", "de-en", "uk"].includes(website.value));

const localeLink = useLocaleLink();
const localeLang = useLocaleLang();
const props = defineProps({
	searchValue: {
		type: String,
		required: false,
		default: ""
	}
});
// 加载状态
const loading = ref(false);
// 关键词
// 创建一个本地响应式数据
const keywords = ref(props.searchValue);
const resultList = ref<ResourceListItem[]>([]);
const resultShow = ref(false);
// 监听 props.message 的变化，同步到本地副本
// watch(
// 	() => props.searchValue,
// 	newVal => {
// 		keywords.value = newVal;
// 	}
// );

// 点击输入框上的叉叉图标清空
const clearKeywords = () => {
	keywords.value = "";
};
// 点击输入框上的搜索图标，或者按下回车键
const searchKeyword = () => {
	if (keywords.value) {
		loading.value = true;
		const url = localeLink(`/products_support/search.html?keyword=${encodeURIComponent(keywords.value)}&page=1`);
		location.href = url;
	}
	gaDocumentation("Resource_Documentation Page", "TDoc_search", `Normal_${keywords.value}`);
};

// 添加全局点击事件监听器
onMounted(() => {
	document.addEventListener("click", handleClickOutside);
});
onBeforeUnmount(() => {
	document.removeEventListener("click", handleClickOutside);
});
const handleClickOutside = (event: any) => {
	// 点击输入框以外的区域隐藏结果列表
	if (!event.target.className.includes("input")) {
		resultShow.value = false;
	}
};

// 输入框输入关键字时，搜索结果
const getResult = debounce(async () => {
	if (!keywords.value) {
		resultShow.value = false;
		resultList.value = [];
		return;
	}
	const { data, error } = await useRequest.post("/cms/api/fs/esResource/searchList", {
		data: {
			keyword: keywords.value,
			page: 1,
			size: 10,
			isResult: 0,
			secondCategoryIds: [],
			firstCategoryIds: []
		}
	});
	if (data && data.value) {
		// resourceType  1通用文章  2专属文章
		resultList.value = data.value.data.resourceList;
		resultShow.value = true;
	}
}, 300);

// 点击搜索条目，跳转文章详情
const searchListGo = (item: any) => {
	let url = `/products_support.html?isPica=${item.resourceType == 2}`;
	if (item.menu.length >= 1) {
		url += `&categories_id=${item.menu[0].id}`;
		if (item.menu.length >= 2) {
			url += `&third_categories_id=${item.menu[1].id}`;
			if (item.menu.length >= 3) {
				url += `&products_model=${item.menu[2].id}`;
				if (item.menu.length >= 4) {
					url += `&style_id=${item.menu[3].id}`;
					if (item.menu.length >= 5) {
						url += `&files_id=${item.menu[4].id}`;
						if (item.menu.length >= 6) {
							url += `&six=${item.menu[5].id}`;
							if (item.menu.length >= 7) {
								url += `&seven=${item.menu[6].id}`;
								if (item.menu.length >= 8) {
									url += `&eight=${item.menu[7].id}`;
								}
							}
						}
					}
				}
			}
		}
	}
	location.href = localeLink(url);
	resultShow.value = false;
};
</script>
<style scoped lang="scss">
@import url("./Banner.scss");
</style>
