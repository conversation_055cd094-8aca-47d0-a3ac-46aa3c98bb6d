<template>
	<!-- <header> -->
	<header class="fs_header_container" :class="{ fix_top: isFixTop }">
		<PFsHeader />
		<MFsHeader />
	</header>
</template>

<script setup lang="ts">
import PFsHeader from "./components/PFsHeader.vue";
import <PERSON><PERSON>sHeader from "./components/MFsHeader.vue";

const headerStore = useHeaderStore();
const countryStore = useCountryStore();

const route = useRoute();

const isFixTop = ref(false);

const initHeader = () => {
	const url = window.location.href;
	// const regex = /\/products\/|\/specials\/|\/solutions\/|\/case-study\/|\/products_support.html|\/blog.html/;
	const regex = /\/products\/|\/specials\/|\/solutions\/|\/case-study\/|\/products_support.html|\/blog.html|\/glossary.html|\/site_map.html/;

	if (!regex.test(url)) {
		isFixTop.value = true;
	} else {
		const filenames = ["2000.html", "2001.html", "2002.html", "2003.html", "2004.html", "2005.html", "2006.html", "2007.html"];
		isFixTop.value = filenames.some(filename => url.includes(filename));
	}
};

onMounted(async () => {
	await nextTick();
	await initHeader();
	setTimeout(() => {
		headerStore.getHoliday();
		headerStore.getSearchHot();
		countryStore.getAreaData();
		// countryStore.getCountryName();
		// useRequest.post("/cms/api/fs/header");
	}, 0);
});
</script>
<style scoped lang="scss">
.fix_top {
	@include pc {
		position: sticky;
		top: 0;
		z-index: 110 !important;
	}
}
</style>
