<!-- eslint-disable prettier/prettier -->
<template>
	<FsTooltip
		ref="searchTooltipRef"
		v-loading.fullscreen="loading"
		:visible="header_search_show"
		placement="bottom-end"
		transition=""
		trigger="hover"
		manual
		mobileForcePc
		:offsetY="device === 'pc' ? 14 : 0"
		:show-arrow="false"
		:popperContentStyle="{
			width: '100%',
			maxWidth: '100%',
			padding: '0',
			zIndex: device === 'pc' ? '109' : '99',
			height: `${device === 'pc' ? 'auto' : '100%'}`
		}">
		<div ref="searchIconRef" class="search_icon" @click="openSearchBox">
			<span class="iconfont iconfont_search">&#xe694;</span>
		</div>
		<template #content>
			<form class="search_box" @submit.prevent="searchSubmit(4, searchData.search)">
				<div class="search_content">
					<div ref="search_result" class="search_input_box">
						<div class="search_input">
							<div class="input_box">
								<button type="submit" class="iconfont iconfont_search">&#xe694;</button>
								<!-- <FsInput
									ref="searchInputRef"
									v-model="searchData.search"
									class="search_input_text"
									:placeholder="localeLang('header.search.pcPlaceholder')"
									@input="getSearchResult" /> -->
								<input
									ref="searchInputRef"
									v-model="searchData.search"
									class="search_input_text"
									:placeholder="localeLang('header.search.pcPlaceholder')"
									type="text"
									@input="getSearchResult" />
								<div v-show="showSearchList" v-loading="searchLoading" class="search_result">
									<ul v-if="searchData.search && searchData.searchList && searchData.searchList.length">
										<li v-for="(v, i) in searchData.searchList" :key="i" tabindex="0" @keyup.enter="searchSubmit(1, v.name)" @click.stop="searchSubmit(1, v.name)">
											<a v-html="highLight(v.name)"></a>
										</li>
									</ul>
									<!-- <div v-else-if="searchData.search && !searchLoading" class="empty-result">
										{{ localeLang("header.noCountryResult") }}
									</div> -->
								</div>
							</div>
							<div class="submit_box">
								<div v-show="searchData.search" class="close_box" @click="clearKeyword">
									<span class="iconfont iconfont_close">&#xf30a;</span>
								</div>
								<FsButton v-if="device === 'pc'" native-type="submit" class="submit" type="red">{{ localeLang("header.search.pcPlaceholder") }}</FsButton>
							</div>
						</div>
						<div class="search_cancel" @click.prevent.stop="closeSearchBox">
							{{ localeLang("header.search.cancel") }}
						</div>
					</div>
					<div v-show="showSearchList" v-loading="searchLoading" class="search_result_box">
						<ul v-if="searchData.search && searchData.searchList && searchData.searchList.length">
							<li v-for="(v, i) in searchData.searchList" :key="i" tabindex="0" @keyup.enter="searchSubmit(1, v.name)" @click.stop="searchSubmit(1, v.name)">
								<a v-html="highLight(v.name)"></a>
							</li>
						</ul>
						<div v-else-if="searchData.search && !searchLoading" class="empty-result">
							{{ localeLang("header.noCountryResult") }}
						</div>
					</div>
					<div v-show="searchData.historyList && searchData.historyList.length && !showSearchList" class="recent">
						<!-- <div class="title">{{ localeLang("header.search.recentSearch") }}</div> -->
						<div class="title">
							<p>{{ localeLang("header.search.recentSearch") }}</p>
							<p v-if="false && searchData.historyList && searchData.historyList.length > 1" class="change_btn" tabindex="0" @keyup.enter="clearAll" @click.stop="clearAll">
								<i class="iconfont iconfont_change">&#xe65f;</i>
								<span>{{ localeLang("header.search.clean") }}</span>
							</p>
						</div>
						<div class="list">
							<div v-for="(v, i) in searchData.historyList" :key="i" tabindex="0" :title="v" class="label" @keyup.enter="searchSubmit(2, v)" @click.stop="searchSubmit(2, v)">
								<div>
									<span>{{ v }}</span>
									<i class="iconfont icon" tabindex="0" @keyup.enter.stop="deleteSearch(i)" @click.stop="deleteSearch(i)">&#xf30a;</i>
								</div>
							</div>
						</div>
					</div>
					<div v-show="hotSearchList && hotSearchList.length && !showSearchList" class="hot">
						<div class="title">
							<p>{{ localeLang("header.search.hotSearch") }}</p>
							<p class="change_btn" tabindex="0" @keyup.enter="getHotSearch" @click.stop="getHotSearch">
								<i class="iconfont iconfont_change" :class="{ iconfont_change_rotate: hotChangePending }">&#xe705;</i>
								<span>{{ localeLang("header.search.change") }}</span>
							</p>
						</div>
						<div class="list">
							<div v-for="(v, i) in hotSearchList" :key="i" :title="v.name" tabindex="0" class="label" @keyup.enter="searchSubmit(3, v.name)" @click.stop="searchSubmit(3, v.name)">
								<div>{{ v.name }}</div>
							</div>
						</div>
					</div>
				</div>
				<span v-if="device === 'pc'" class="iconfont search_box_close" @click="closeSearchBox">&#xf30a;</span>
			</form>
			<Teleport to="body">
				<transition name="fade">
					<div v-if="device === 'pc'" class="header_mask" :style="{ top: `${pc_header_height}px` }" @click.stop="closeSearchBox"></div>
				</transition>
			</Teleport>
		</template>
	</FsTooltip>
</template>
<script setup lang="ts">
import xss from "xss";
import { ref } from "vue";
import { FsTooltip, FsInput, FsButton } from "fs-design";
import fixScroll from "@/utils/fixScroll";
import type { SearchData } from "@/component/FsHeader/header.types";
import { removeLabel } from "@/utils/utils";
const headerStore = useHeaderStore();
const { pc_header_height, header_search_show, m_header_height, hotSearchPage, hotChangePending, hotSearchList, isShowHoliday } = storeToRefs(headerStore);

const localeLink = useLocaleLink();
const localeLang = useLocaleLang();
const gaStore = useGaStore();
const { pageGroup } = storeToRefs(gaStore);
const router = useRouter();
const route = useRoute();
// loading
const loading = ref(false);
const searchLoading = ref(false);

// 是否开启搜索模块
// const isSearch = ref(false);
// 定义搜索框
const searchTooltipRef = ref();
const searchInputRef = ref();
const search_result = ref();
// 打开搜索框
const openSearchBox = async () => {
	searchBuried("click_search", "top search icon");
	header_search_show.value ? headerStore.hideSearchBox() : headerStore.showSearchBox();
	await nextTick();
	if (header_search_show.value) {
		setTimeout(() => {
			if (searchInputRef.value) {
				searchInputRef.value.focus();
			}
		}, 10);
	}
};
// 关闭搜索框
const closeSearchBox = () => {
	headerStore.hideSearchBox();
};
// 定义数据
const searchData = ref<SearchData>({
	search: (route.query.keyword as string) || "",
	searchList: [],
	historyList: []
	// hotList: [],
	// hotChangePending: false,
	// hotSearchPage: 0
});
// 清除关键字
const clearKeyword = () => {
	searchData.value.search = "";
};
// 获取设备信息
const deviceStore = useDeviceStore();
const { device } = storeToRefs(deviceStore);
// 高亮
function highLight(str: string): string {
	if (!str) {
		return "";
	}
	if (searchData.value.search && searchData.value.search.length > 0) {
		if (str.includes(searchData.value.search)) {
			const replaceReg = new RegExp(searchData.value.search, "g");
			// 高亮替换v-html值
			const replaceString = '<span style="background:#E5E5E5;">' + searchData.value.search + "</span>";
			// 开始替换
			str = str.replace(replaceReg, replaceString);
		}
	}
	return str;
}
// 获取数据
function getHotSearch() {
	searchBuried("search_change", "Hot Search");
	headerStore.getSearchHot();
}
// 点击关键词搜索
function searchSubmit(type: number, str: string) {
	/**
	 * type   1 搜索结果点击   2 最近搜索点击   3 热门推荐   4 输入框点击
	 */

	if (!str) {
		loading.value = false;
		return;
	}
	closeSearchBox();
	loading.value = true;
	if (type == 1) {
		// searchBuried("recommender", this.pageGroup, n)
	} else if (type == 2) {
		searchBuried("search", `Recent_${str}`);
	} else if (type == 3) {
		searchBuried("search", `Hot_${str}`);
	} else if (type == 4) {
		searchBuried("click_search", `SearchButton_${str}`);
	}
	if (str === route.query.keyword) {
		router.go(0);
	}

	searchData.value.search = str;
	setSearchHistory(str);
	hideSearchResult();
	location.href = localeLink(`/search_result?keyword=${encodeURIComponent(removeLabel(str))}`);
}

const searchBuried = (action: string, label: string) => {
	if (window.dataLayer) {
		window.dataLayer.push({
			event: "uaEvent",
			eventCategory: `${pageGroup.value}_Top Search`,
			eventAction: action,
			eventLabel: label,
			nonInteraction: false
		});
	}
};

// 下拉列表
const showSearchList = ref(false);
// 监听列表关闭
useClickOutside(search_result, showSearchList);
// 隐藏搜索下拉框
function hideSearchResult(): void {
	showSearchList.value = false;
}
// 提示词
const getSearchResult = debounce(async () => {
	if (!searchData.value.search) {
		return;
	}
	// searchLoading.value = true;
	// showSearchList.value = true;

	const { data, error } = await useRequest.get("/api/search/searchWords", {
		data: { keyword: xss(searchData.value.search) }
	});

	searchLoading.value = false;

	if (data && data.value) {
		searchData.value.searchList = data.value.data.info;
		if (data.value.data.info.length) {
			showSearchList.value = true;
		} else {
			showSearchList.value = false;
		}
	}
}, 300);

// 设置搜索历史记录
function setSearchHistory(str: string): void {
	if (searchData.value.historyList.length && searchData.value.historyList.includes(str)) {
		searchData.value.historyList.splice(searchData.value.historyList.indexOf(str), 1);
	}
	searchData.value.historyList.unshift(str);

	// if (searchData.value.historyList.length > 7) {
	// 	searchData.value.historyList.splice(8);
	// }
	localStorage.setItem("history", JSON.stringify(searchData.value.historyList));
}

// 删除历史搜索记录
function deleteSearch(i: number): void {
	searchBuried("search_remove", searchData.value.historyList[i]);
	searchData.value.historyList.splice(i, 1);
	localStorage.setItem("history", JSON.stringify(searchData.value.historyList));
}
const clearAll = function () {
	searchData.value.historyList = [];
	localStorage.setItem("history", "[]");
};

const m_header_height_fotmat = ref("");

const searchIconRef = ref();

watch(
	() => isShowHoliday.value,
	newVal => {
		if (!newVal) {
			console.log(searchTooltipRef.value.popperInstance.update());
		}
	}
);
watch(
	() => searchData.value.search,
	newVal => {
		if (!newVal) {
			showSearchList.value = false;
		}
	}
);
// searchData.value.search
const scrollHandle = () => {
	if (searchIconRef.value) {
		const rect = searchIconRef.value.getBoundingClientRect().bottom;
		console.log(rect);
		m_header_height_fotmat.value = rect + "px";
	}
};
// = ref(m_header_height.value + "px");
onMounted(() => {
	if (searchIconRef.value) {
		const rect = searchIconRef.value.getBoundingClientRect().bottom;
		console.log(rect);
		m_header_height_fotmat.value = rect + "px";
	}
	document.addEventListener("scroll", scrollHandle);
	nextTick(() => {
		searchData.value.historyList = localStorage.getItem("history") ? JSON.parse(localStorage.getItem("history")) : [];
	});
});

onBeforeUnmount(() => {
	// document.removeEventListener("click", handleClickOutside);
	document.removeEventListener("scroll", scrollHandle);
});
</script>
<style lang="scss" scoped>
.search_icon {
	width: 40px;
	height: 40px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	.iconfont_search {
		font-size: 20px;
		line-height: 20px;
		width: 20px;
		height: 20px;
		color: $textColor1;
	}
	&:hover {
		background-color: $bgColor2;
		@include padMobile {
			background-color: transparent;
		}
	}
}
.search_box {
	position: relative;
	padding: 28px 0;
	min-height: 360px;
	background-color: $bgColor6;
	.search_content {
		width: 84vw;
		@include newPcHeaderWidth;
		margin: 0 auto;
		padding-left: 156px;
		.search_input_box {
			max-width: 900px;
			@include padMobile {
				max-width: unset;
			}
			.search_input {
				position: relative;
				// :deep(.fs-input__wrapper) {
				// 	background: #f6f6f8;
				// }
				.input_box {
					position: relative;

					border-radius: 4px;
					> input {
						padding: 10px 138px 10px 12px;
						background: #f6f6f8;
						border-radius: 4px;
						border: none;
						transition: none;
						&:focus {
							background: #f6f6f8;
							border: 1px solid #eeeeee;
							&::placeholder {
								opacity: 0;
							}
						}
						&:focus:hover {
							background: #f6f6f8;
						}
						&::placeholder {
							color: #707070;
						}
						&:hover {
							background: linear-gradient(0deg, rgba(25, 25, 26, 0.04), rgba(25, 25, 26, 0.04)), #f6f6f8;
							@include padMobile {
								background: #f6f6f8;
							}
						}
						@include padMobile {
							padding-left: 42px;
						}
					}
					.iconfont_search {
						display: none;
						position: absolute;
						padding: 6px;
						border-radius: 3px;
						font-size: 16px;
						line-height: 1;
						color: $textColor2;
						z-index: 1;
						top: 7px;
					}
					:deep(.fs-input__inner) {
						&:hover {
							cursor: text;
						}
					}
					.fs-input {
						@include font14;
						color: $textColor1;
					}
					.search_result {
						width: 100%;
						margin-top: 4px;
						border-radius: 4px;
						overflow: hidden;
						border: 1px solid $borderColor1;
						// border-top: 0;
						height: 316px;
						background-color: $bgColor6;
						box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
						border-radius: 4px;
						position: relative;
						@include padMobile() {
							display: none;
							position: relative;
							left: auto;
							top: auto;
							box-shadow: none;
							border: none;
							border-radius: 0;
						}

						> ul {
							padding: 8px 0;
							border-radius: 4px;
							> li {
								transition: all 0.3s;

								&:hover {
									background-color: $bgColor1;
								}
								&:focus-visible {
									@include focusVisibleIn;
								}

								> a {
									@include font14;
									color: $textColor1;
									display: block;
									padding: 4px 12px;
									text-decoration: none;
									cursor: pointer;
								}
							}
							@include padMobile {
								background-color: #fff;
								> li {
									padding: 8px 0;
									> a {
										@include font14;
									}
								}
							}
						}
						:deep(.fs-loading) {
							background-color: $bgColor6;
							.fs-circle {
								width: 32px;
								height: 32px;
							}
						}
						.empty-result {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							@include font14;
							color: $textColor2;
						}
					}
				}
				.submit_box {
					position: absolute;
					z-index: 1;
					top: 0;
					right: 0;
					display: flex;
					align-items: center;
					height: 40px;
					margin: 1px 1px 0 0;
					:deep(.fs-button) {
						@include font14;
						border-radius: 0 4px 4px 0;
					}
					.close_box {
						// padding: 0 12px;
						// background-color: $bgColor6;
						margin-right: 12px;
						height: 28px;
						display: flex;
						.iconfont_close {
							padding: 6px;
							border-radius: 3px;
							font-size: 16px;
							line-height: 1;
							color: $textColor2;
							cursor: pointer;
							&:hover {
								color: $textColor1;
								background-color: $bgColor1;
							}
						}
					}
				}
			}
			.search_cancel {
				display: none;
				@include font14;
				color: $textColor1;
				padding: 10px 0 10px 16px;
				cursor: pointer;
			}
		}
		.search_result_box {
			display: none;
			width: 100%;
			margin-top: 4px;
			border-radius: 4px;
			overflow: hidden;
			border: 1px solid $borderColor1;
			// border-top: 0;
			height: 316px;
			background-color: $bgColor6;
			box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 4px;
			position: relative;
			@include padMobile() {
				display: block;
				position: relative;
				left: auto;
				top: auto;
				box-shadow: none;
				border: none;
				border-radius: 0;
			}

			> ul {
				padding: 8px 0;
				border-radius: 4px;
				> li {
					transition: all 0.3s;

					&:hover {
						background-color: $bgColor1;
					}
					&:focus-visible {
						@include focusVisibleIn;
					}

					> a {
						@include font14;
						color: $textColor1;
						display: block;
						padding: 4px 12px;
						text-decoration: none;
						cursor: pointer;
					}
				}
				@include padMobile {
					background-color: #fff;
					> li {
						padding: 8px 0;
						> a {
							@include font14;
						}
					}
				}
			}
			:deep(.fs-loading) {
				background-color: $bgColor6;
				.fs-circle {
					width: 32px;
					height: 32px;
				}
			}
			.empty-result {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				@include font14;
				color: $textColor2;
			}
		}

		.recent {
			> .list {
				max-height: 108px;
				overflow: hidden;
			}
		}
		.recent,
		.hot {
			max-width: 900px;
			@include padMobile {
				max-width: unset;
			}
			margin-top: 20px;
			> .title {
				@include font16;
				color: $textColor1;
				font-weight: 600;
			}
			> .list {
				.label {
					display: inline-flex;
					@include font12;
					border: 1px solid #eeeeee;
					border-radius: 999px;
					padding: 3px 10px;
					background-color: $bgColor6;
					cursor: pointer;
					margin: 8px 8px 0 0;
					position: relative;
					z-index: 1;
					max-width: 100%;
					&:before {
						content: " ";
						display: block;
						width: 100%;
						height: 100%;
						top: 0;
						left: 0;
						bottom: 0;
						position: absolute;
						right: 0;
						background: #19191a;
						border-radius: 999px;
						opacity: 0;
						z-index: -1;
						transition: all 0.3s;
					}
					> div {
						color: $textColor1;
						display: flex;
						align-items: center;
						max-width: 100%;
						> i {
							color: $textColor2;
							font-size: 12px;
							line-height: 1;
							margin-left: 8px;
							// padding: 4px;
							border-radius: 3px;
							&:hover {
								color: $textColor1;
							}
						}
						> span {
							display: inline-block;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							width: 100%;
						}
					}
					&:last-child {
						margin-right: 0;
					}
					&:hover {
						&:before {
							opacity: 0.04;
						}
						> div {
							color: $textColor1;
						}
						// background-color: $bgColor2;
					}
				}
			}
		}
		.hot,
		.recent {
			.title {
				display: flex;
				justify-content: space-between;
				.change_btn {
					cursor: pointer;
					color: $textColor5;
					display: flex;
					align-items: center;
					@include font14;
					font-weight: 400;
					&:hover {
						span {
							text-decoration: underline;
						}
					}
				}

				.iconfont_change {
					font-weight: 400;
					color: $textColor5;
					font-size: 14px;
					display: block;
					margin-right: 4px;
				}

				.iconfont_change_rotate {
					animation: searchLoading 1s linear infinite;
				}

				@keyframes searchLoading {
					0% {
						transform: rotate(0deg);
					}

					25% {
						transform: rotate(90deg);
					}

					50% {
						transform: rotate(180deg);
					}

					75% {
						transform: rotate(270deg);
					}

					100% {
						transform: rotate(360deg);
					}
				}
			}
		}
	}
	.search_box_close {
		position: absolute;
		top: 16px;
		right: 16px;
		padding: 6px;
		border-radius: 3px;
		font-size: 20px;
		line-height: 1;
		color: $textColor2;
		cursor: pointer;
		&:hover {
			color: $textColor1;
			background: rgba(25, 25, 26, 0.04);
		}
	}
	@include padMobile {
		height: 100%;
		padding: 28px 0;
		.search_content {
			margin: 0;
			width: 100%;
			max-width: unset;
			.search_input_box {
				display: flex;
				.search_input {
					flex: 1;
					.input_box {
						.fs-input {
							:deep(.fs-input__wrapper) {
								padding-left: 42px;
							}
							:deep(.is-focus) {
								border: 1px solid $borderColor4;
							}
						}
						.iconfont_search {
							left: 7px;
							display: block;
						}
					}
				}
				.search_cancel {
					display: block;
					height: fit-content;
				}
			}
		}
	}
}
.header_mask {
	position: absolute;
	top: auto;
	bottom: 0;
	left: 0;
	bottom: 0;
	z-index: 100;
	left: 0;
	right: 0;
	background: rgba(51, 51, 51, 0.3);
}
// 修改组件样式
.fs-tooltip {
	:deep(.fs-tooltip__mobile--content) {
		padding: 0 !important;
		top: 85px;
		left: 0;
		height: 100%;
		width: 100% !important;
		background-color: $bgColor6;
		transform: none;
		// z-index: -1;
		> .iconfont {
			display: none;
		}
		.fs-tooltip__mobile--context {
			margin: 0;
		}
		@include padMobile {
			top: v-bind(m_header_height_fotmat);
		}
	}
	:deep(.fs-tooltip__mobile--mask) {
		display: none;
	}
}
:deep(.fs-tooltip__popper) {
	box-shadow: none;
	margin: 1.5px 0 0 0;
}
@include padMobile {
	.search_icon {
		width: 48px;
		height: 48px;
		.iconfont_search {
			font-size: 20px;
			line-height: 20px;
			padding: 2px;
			width: 24px;
			height: 24px;
		}
	}
}
</style>
