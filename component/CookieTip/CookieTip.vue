<template>
	<div>
		<!-- 欧洲国家tips -->
		<div v-if="showCookieTipNew" class="cookie_box">
			<div class="m_close">
				<i class="iconfont icon-close" @click="closeCookieTip">&#xf30a;</i>
			</div>
			<div class="main-EU main europe">
				<p>
					<span class="policy" v-html="replaceCookieLinks(getCookieText('txt01'))"></span>
				</p>
				<div class="footer_box">
					<fs-button class="cookiesPop" type="gray" size="mediumSmall" plain @click="manageSetting($event)">{{ getCookieText("cookieSetting") }}</fs-button>
					<fs-button class="declineAll" type="gray" size="mediumSmall" plain @click="handleDisAgree">{{ getCookieText("rejectAll") }}</fs-button>
					<fs-button class="accept" type="gray" size="mediumSmall" @click="agreeCookie">{{ getCookieText("acceptCookies") }}</fs-button>
				</div>
			</div>
		</div>
		<!-- 美国站点登录用户cookie授权提示 -->
		<!-- <div v-else-if="showCookieTipUS" class="cookie_box">
			<div class="m_close">
				<i class="iconfont icon-close" @click="closeCookieTip">&#xf30a;</i>
			</div>
			<div class="main-EU main">
				<p>
					We've made important updates to our Cookie Notice effective [July 19,2025], to clarify how we collect, use, and share personal information. Please take a moment to review
					the updated Cookie Notice. By selecting "Acknowledge and Continue", you acknowledge these changes.
				</p>
				<div class="btn_box">
					<FsButton class="accept" tabindex="0" type="gray" size="mediumSmall" @click="acknowledgeCookieUS">Acknowledge and Continue</FsButton>
				</div>
			</div>
		</div> -->
		<!-- 非欧洲国家tips -->
		<div v-else-if="showCookieTipCommon && showCookieTip && isFetch" class="cookie_box">
			<div class="m_close">
				<i class="iconfont icon-close" @click="closeCookieTip">&#xf30a;</i>
			</div>
			<div class="main-EU main">
				<template v-if="isLogin">
					<template v-if="iso_code === 'US' && userInfo.cookie_authorization === 0">
						<p class="p_us" v-html="replaceCookieLinks(usaCookieText)"></p>
						<div class="btn_box">
							<FsButton class="accept" tabindex="0" type="gray" size="mediumSmall" @click="acknowledgeCookieUS">Acknowledge and Continue</FsButton>
						</div>
					</template>
					<template v-else>
						<p v-html="replaceCookieLinks(getCookieText('txt01'))"></p>
						<div class="btn_box">
							<FsButton class="cookiesPop" type="gray" size="mediumSmall" plain @click="manageSetting($event)">{{ getCookieText("cookieSetting") }}</FsButton>
							<FsButton class="accept" tabindex="0" type="gray" size="mediumSmall" @click="agreeCookie">{{ getCookieText("acceptCookies") }}</FsButton>
						</div>
					</template>
				</template>
				<template v-else>
					<p v-html="replaceCookieLinks(getCookieText('txt01'))"></p>
					<div class="btn_box">
						<FsButton class="cookiesPop" type="gray" size="mediumSmall" plain @click="manageSetting($event)">{{ getCookieText("cookieSetting") }}</FsButton>
						<FsButton class="accept" tabindex="0" type="gray" size="mediumSmall" @click="agreeCookie">{{ getCookieText("acceptCookies") }}</FsButton>
					</div>
				</template>
			</div>
		</div>
		<CookiePopup v-model:show="showPopup" :is-europe-country="isEuropeCountry" @agree-cookie="agreeCookie"></CookiePopup>
	</div>
</template>

<script setup lang="ts">
import { FsButton } from "fs-design";
import CookiePopup from "../../popup/CookiePopup/CookiePopup.vue";

const headerStore = useHeaderStore();
const websiteStore = useWebsiteStore();
const userStore = useUserStore();
const route = useRoute();

const localeLink = useLocaleLink();
const localeLang = useLocaleLang();

const { website, iso_code } = storeToRefs(websiteStore);
const { showCookieTipNew, showCookieTip, showCookieTipCommon } = storeToRefs(headerStore);
const { isLogin, userInfo, isFetch } = storeToRefs(userStore);
const showPopup = ref(false);

// 计算属性：判断是否是欧洲国家
const isEuropeCountry = computed(() => {
	const EuropeSiteMap = ["de-en", "de", "fr", "es", "it", "nl", "uk"];
	return EuropeSiteMap.includes(website.value as string);
});

const manageSetting = (e: Event) => {
	showPopup.value = true;
};

// 美国站点cookie授权确认方法
const acknowledgeCookieUS = async () => {
	try {
		// 调用cookie授权接口
		const { data, error } = await useRequest.post("/api/cookie/authorization");
		// 授权成功后关闭弹窗
		closeCookieTip();
	} catch (err) {
		console.error("Cookie authorization error:", err);
		// 即使出错也关闭弹窗
		closeCookieTip();
	}
};

// mx的语言包
const cookieTipNew_mx = {
	txt01: `FS utiliza cookies y otras tecnologías para que nuestro sitio web funcione correctamente y para que la publicidad y el contenido sean más relevantes para ti. Para obtener más información, consulta nuestra <a href="XXXX">Política de Privacidad y Cookies</a>. Puedes gestionar tus preferencias de cookies en cualquier momento seleccionando "<a href="XXXX">Administrar cookies</a>".`,
	cookieSetting: `Configuración de cookies`,
	acceptCookies: `Aceptar todas las cookies`,
	rejectAll: `Reject All`
};
// 美国站点Cookie文本
const usaCookieText = `We've made important updates to our <a href="YYYY">Cookie Notice</a> effective [July 19,2025], to clarify how we collect, use, and share personal information. Please take a moment to review the updated <a href="YYYY">Cookie Notice</a>. By selecting "Acknowledge and Continue", you acknowledge these changes.`;

// 添加获取Cookie文本的方法
const getCookieText = (key: string) => {
	if (website.value === "mx") {
		return cookieTipNew_mx[key as keyof typeof cookieTipNew_mx] || "";
	}
	return localeLang(`cookieTipNew.${key}`);
};

// const loadG = () => {
// 	if (["uk", "de", "de-en", "fr", "it", "es"].includes(website.value)) {
// 		loadGTM();
// 	}
// };

const disAgreeGoogle = (e: MouseEvent) => {
	if ((e.target as HTMLElement).className === "disAgreeGoogle") {
		console.log("disagree__disagree__disagree__");
		const googleAnalytics = useCookies("_ga");
		if (googleAnalytics.value) {
			const _ga = useCookies("_ga");
			const _gid = useCookies("_gid");
			const AMP_TOKEN = useCookies("AMP_TOKEN");
			const _ym_isad = useCookies("_ym_isad");
			const _ym_uid = useCookies("_ym_uid");
			const _ym_visorc_48770636 = useCookies("_ym_visorc_48770636");
			_ga.value = null;
			_gid.value = null;
			AMP_TOKEN.value = null;
			_ym_isad.value = null;
			_ym_uid.value = null;
			_ym_visorc_48770636.value = null;
		}
	}
	// if (["uk", "de", "de-en", "fr", "it", "es"].includes(website.value)) {
	if (window?.gtag) {
		window.gtag("consent", "update", {
			region: [
				"AT",
				"BE",
				"BG",
				"CY",
				"CZ",
				"DE",
				"DK",
				"EE",
				"ES",
				"FI",
				"FR",
				"GR",
				"HR",
				"HU",
				"IE",
				"IS",
				"IT",
				"LI",
				"LT",
				"LU",
				"LV",
				"MT",
				"NL",
				"NO",
				"PL",
				"PT",
				"RO",
				"SE",
				"SI",
				"SK"
			],
			ad_storage: "denied",
			ad_user_data: "denied",
			ad_personalization: "denied",
			analytics_storage: "denied"
		});
	}
	headerStore.$patch(state => {
		state.showCookieTip = false;
	});
};

const handleDisAgree = () => {
	// 清除营销相关cookie
	const fs_marketing_sdk = useCookie("fs_marketing_sdk");
	fs_marketing_sdk.value = null;

	// 设置cookie提示隐藏标识
	const cookies_tip_hidden = useCookies("cookies_tip_hidden", {
		maxAge: 60 * 60 * 24 * 365
	});
	cookies_tip_hidden.value = "yes";

	// 清除同意cookie的相关标识
	const fs_google_analytics = useCookies("fs_google_analytics");
	const cookieconsent_dismissed = useCookies("cookieconsent_dismissed");
	fs_google_analytics.value = null;
	cookieconsent_dismissed.value = null;

	// 如果是登录用户且cookie_authorization为0，清除所有相关cookie
	if (isLogin.value && userInfo.value?.cookie_authorization === 0) {
		// 清除Google Analytics相关cookie
		const _ga = useCookies("_ga");
		const _gid = useCookies("_gid");
		const AMP_TOKEN = useCookies("AMP_TOKEN");
		const _ym_isad = useCookies("_ym_isad");
		const _ym_uid = useCookies("_ym_uid");
		const _ym_visorc_48770636 = useCookies("_ym_visorc_48770636");

		_ga.value = null;
		_gid.value = null;
		AMP_TOKEN.value = null;
		_ym_isad.value = null;
		_ym_uid.value = null;
		_ym_visorc_48770636.value = null;
	}

	// 更新gtag consent状态为denied
	if (window?.gtag) {
		window.gtag("consent", "update", {
			region: [
				"AT",
				"BE",
				"BG",
				"CY",
				"CZ",
				"DE",
				"DK",
				"EE",
				"ES",
				"FI",
				"FR",
				"GR",
				"HR",
				"HU",
				"IE",
				"IS",
				"IT",
				"LI",
				"LT",
				"LU",
				"LV",
				"MT",
				"NL",
				"NO",
				"PL",
				"PT",
				"RO",
				"SE",
				"SI",
				"SK"
			],
			ad_storage: "denied",
			ad_user_data: "denied",
			ad_personalization: "denied",
			analytics_storage: "denied"
		});
	}

	// 更新header store状态
	headerStore.$patch(state => {
		state.showCookieTip = false;
		state.showCookieTipNew = false;
	});

	// 刷新页面以确保状态同步
	window && window.location.reload();
};

const handleAgreeCookie = () => {
	const fs_google_analytics = useCookies("fs_google_analytics", {
		maxAge: 60 * 60 * 24 * 365
	});

	const cookieconsent_dismissed = useCookies("cookieconsent_dismissed", {
		maxAge: 60 * 60 * 24 * 365
	});

	fs_google_analytics.value = "yes";
	cookieconsent_dismissed.value = "yes";

	headerStore.$patch(state => {
		state.showCookieTip = false;
	});
	if (window.gtag) {
		window.gtag("consent", "update", {
			region: [
				"AT",
				"BE",
				"BG",
				"CY",
				"CZ",
				"DE",
				"DK",
				"EE",
				"ES",
				"FI",
				"FR",
				"GR",
				"HR",
				"HU",
				"IE",
				"IS",
				"IT",
				"LI",
				"LT",
				"LU",
				"LV",
				"MT",
				"NL",
				"NO",
				"PL",
				"PT",
				"RO",
				"SE",
				"SI",
				"SK"
			],
			ad_storage: "granted",
			ad_user_data: "granted",
			ad_personalization: "granted",
			analytics_storage: "granted"
		});
	}
};

// agreeCookie方法保持不变，已经存在
const agreeCookie = () => {
	handleAgreeCookie();
	// 更新cookies
	const fs_marketing_sdk = useCookie("fs_marketing_sdk", {
		maxAge: 60 * 60 * 24 * 365
	});
	fs_marketing_sdk.value = "yes";

	const cookies_tip_hidden = useCookies("cookies_tip_hidden", {
		maxAge: 60 * 60 * 24 * 365
	});
	cookies_tip_hidden.value = "yes";
	window && window.location.reload();
};

onMounted(() => {
	// 检查cookieconsent_dismissed状态
	const cookieconsent_dismissed = useCookies("cookieconsent_dismissed", {
		maxAge: 60 * 60 * 24 * 365
	});

	// 如果用户已登录且cookie_authorization为0，需要清除之前的同意状态
	if (isLogin.value && userInfo.value?.cookie_authorization === 0) {
		// 清除之前的同意cookie
		const fs_google_analytics = useCookies("fs_google_analytics");
		fs_google_analytics.value = null;
		cookieconsent_dismissed.value = null;

		// 强制显示cookie提示
		headerStore.$patch(state => {
			state.showCookieTip = true;
		});
	} else {
		// 正常逻辑：根据cookieconsent_dismissed状态决定是否显示
		headerStore.$patch(state => {
			state.showCookieTip = !cookieconsent_dismissed.value;
		});
	}
});

// 添加关闭Cookie提示的方法
const closeCookieTip = () => {
	const cookies_tip_hidden = useCookies("cookies_tip_hidden", {
		maxAge: 60 * 60 * 24 * 365
	});
	cookies_tip_hidden.value = "yes";

	headerStore.$patch(state => {
		state.showCookieTip = false;
		state.showCookieTipNew = false;
	});
};

// 添加处理多个占位符替换的函数
const replaceCookieLinks = (text: string) => {
	return text.replace(/XXXX/g, localeLink("/policies/privacy_policy.html")).replace(/YYYY/g, localeLink("/policies/cookie_notice.html"));
};
</script>

<style lang="scss" scoped>
.cookie_box {
	width: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	// background-color: #fff;
	// box-shadow: 0px -8px 20px 0px rgba(0, 0, 0, 0.1);
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 99; //现在先改成99，改之前是10，如果后面发现有问题，再还原，因为设计说要将右下角的FsGlobalBtn(z-index为98)放在Cookie的下面
	padding: 20px 0;
	.m_close {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 0 20px;
		position: relative;
		.iconfont {
			position: absolute;
			top: 0;
			right: 20px;
			color: $textColor6;
			font-size: 20px;
			line-height: 1;
			display: block;
			padding: 4px;
			cursor: pointer;
			&::before {
				display: block;
			}
		}
	}
	.main {
		// width: 100%;
		// max-width: 1200px;
		// margin: 0 auto;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		column-gap: 24px;
		height: 100%;
		@include newPcHeaderWidth;
		// @media (max-width: 1024px) {
		// 	padding-left: 24px;
		// 	padding-right: 24px;
		// }
		// @media (max-width: 768px) {
		// 	padding-left: 16px;
		// 	padding-right: 16px;
		// }
		&.europe {
			> p {
				max-width: 735px;
			}
		}
		.btn_box {
			display: flex;
			.cookiesPop,
			.declineAll {
				color: $textColor6;
			}
			@media (max-width: 768px) {
				gap: 12px;
				width: 100%;
				margin-top: 16px;
				flex-direction: column-reverse;
				.fs-button {
					height: 42px;
				}
			}
		}
	}

	.main-EU {
		justify-content: space-between;
		:deep(.fs-button) {
			@media (max-width: 768px) {
				width: 100%;
				margin-left: 0;
			}
		}
		p {
			max-width: 840px;
			@include font14;
			// line-height: 20px;
			color: $textColor6;
			:deep(a) {
				text-decoration: underline;
				color: #ffffff;
			}
			.policy {
				//text-decoration: none;
				:deep(a) {
					color: $textColor6;
					text-decoration: underline;
				}
			}
			.manage {
				color: $textColor3;
				cursor: pointer;
				.iconfont {
					font-size: 13px;
					margin-left: 4px;
				}
			}
			&.p_us {
				max-width: 938px;
			}
		}
		.accept {
			color: $textColor1;
			padding: 0 12px;
		}
		@media (max-width: 768px) {
			flex-direction: column;
			.accept {
				margin-left: 0;
			}
			&.europe {
				p {
					span {
						display: block;
						margin-top: 16px;
					}
				}
			}
		}
	}
	@media (max-width: 1024px) {
		padding: 16px 0 24px;
		border-radius: 8px 8px 0 0;
		.m_close {
			padding: 0 24px 16px;
			.iconfont {
				position: static !important;
			}
		}
		@media (max-width: 768px) {
			.m_close {
				padding: 0 16px 16px;
			}
		}
	}
}
.footer_box {
	display: flex;
	max-width: max-content;
	.fs-button {
		padding: 0 12px;
	}
	@media (max-width: 768px) {
		margin-top: 24px;
		flex-direction: column-reverse;
		width: 100%;
		max-width: none;
		margin-left: 0;
		row-gap: 12px;
		.fs-button {
			width: 100%;
			height: 42px;
		}
	}
	.cookiesPop,
	.declineAll {
		color: $textColor6;
	}
}
</style>
