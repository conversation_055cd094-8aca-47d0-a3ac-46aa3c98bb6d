<template>
	<ClientOnly>
		<div>
			<Teleport to="body">
				<div class="fs_global_btn_box" :class="{ fs_global_btn_box_scroll: isScroll }" :style="{ bottom: globalBtnBottom }">
					<div class="back_top_btn btn_box" :class="{ active: showBackTopBtn }" @click.stop="backTop">
						<span class="iconfont iconfont_top">&#xe630;</span>
					</div>
					<div class="service_btn_wrap">
						<div class="service_btn_box" :class="{ service_btn_hide: showFoldBtn }">
							<div id="bdtj_lxwm" class="livechat_btn btn_box btn_box_line" @click.stop="liveChatClick">
								<template v-if="!unreadMsgStatus">
									<span class="iconfont iconfont_chat">&#xe648;</span>
								</template>
								<img v-else src="https://resource.fs.com/mall/generalImg/20250704142455q0c82q.svg" alt="" />
							</div>
							<div class="consult_btn btn_box btn_box_line" @click.stop="showContact">
								<span class="iconfont iconfont_consult">&#xe728;</span>
							</div>
						</div>
						<div class="fold_btn btn_box" :class="{ btn_folded: showFoldBtn }" tabindex="0" @click="toogleFold">
							<span class="iconfont iconfont_fold">&#xe708;</span>
						</div>
					</div>
				</div>

				<div class="fs_global_btn_box_padMobile" :class="{ fs_global_btn_box_padMobile_cookie: showCookieTip }" :style="{ bottom: globalBtnBottom }">
					<div id="bdtj_lxwm" class="padMobile_btn livechat_btn" @click.stop="liveChatClick">
						<template v-if="!unreadMsgStatus">
							<span class="iconfont iconfont_chat">&#xe648;</span>
						</template>
						<img v-else src="https://resource.fs.com/mall/generalImg/20250704142455q0c82q.svg" alt="" />
					</div>
					<div class="padMobile_btn consult_btn" @click.stop="showContact">
						<span class="iconfont iconfont_consult">&#xe728;</span>
					</div>
				</div>
			</Teleport>
			<ContactSales v-model="showContactSales" :bottom="globalBtnBottom" />
		</div>
	</ClientOnly>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import ContactSales from "@/popup/ContactSales/index.vue";
const headerStore = useHeaderStore();
const bdRequest = useBdRequest();
const showBackTopBtn = ref(false);
const showContactSales = defineModel({ type: Boolean, default: false });
const route = useRoute();
const showFoldBtn = ref(false);
const deviceStore = useDeviceStore();
const { screenWidth } = storeToRefs(deviceStore);
const liveChatStore = useLiveChatStore();
const unreadMsgStatus = computed(() => {
	return liveChatStore.unreadMsgStatus;
});
console.log("liveChatStore", liveChatStore);

const isScroll = ref(false);
let timer: number | null = null;
let resizeTimer: number | null = null;
const { showCookieTipNew, showCookieTip, showCookieTipCommon } = storeToRefs(headerStore);
const cookieTipHeight = ref(0);

// 防抖处理的 resize 事件处理函数
const handleResize = () => {
	// 防抖处理，避免频繁调用
	if (resizeTimer) {
		clearTimeout(resizeTimer);
	}
	resizeTimer = window.setTimeout(updateCookieTipHeight, 150);
};
const gaPoint = function () {
	usePoint({ eventAction: "floating_button", eventLabel: "Live Chat" });
};
function updateCookieTipHeight() {
	// 使用 MutationObserver 和 Promise 结合的方式来可靠地获取元素高度
	const getCookieBoxHeight = (): Promise<number> => {
		return new Promise(resolve => {
			const checkElement = () => {
				const el = document.querySelector(".cookie_box") as HTMLElement;
				if (el && el.offsetHeight > 0) {
					console.log(el, "elel - found with height:", el.offsetHeight);
					resolve(el.offsetHeight);
					return true;
				}
				return false;
			};

			// 首先尝试直接获取
			if (checkElement()) {
				return;
			}

			// 如果元素不存在或高度为0，使用 MutationObserver 监听DOM变化
			const observer = new MutationObserver(mutations => {
				// 检查是否有相关的DOM变化
				const hasRelevantChange = mutations.some(mutation => {
					if (mutation.type === "childList") {
						// 检查新增的节点中是否包含 cookie_box
						const addedNodes = Array.from(mutation.addedNodes);
						return addedNodes.some(node => {
							if (node.nodeType === Node.ELEMENT_NODE) {
								const element = node as Element;
								return element.classList?.contains("cookie_box") || element.querySelector?.(".cookie_box");
							}
							return false;
						});
					}
					return false;
				});

				if (hasRelevantChange && checkElement()) {
					observer.disconnect();
				}
			});

			// 开始观察DOM变化
			observer.observe(document.body, {
				childList: true,
				subtree: true
			});

			// 设置超时机制，避免无限等待
			const timeoutId = setTimeout(() => {
				observer.disconnect();
				console.warn("Cookie box element not found within timeout, using height 0");
				resolve(0);
			}, 5000); // 5秒超时

			// 使用 requestAnimationFrame 进行轮询检查，确保在元素渲染完成后获取高度
			const pollCheck = () => {
				if (checkElement()) {
					clearTimeout(timeoutId);
					observer.disconnect();
					return;
				}
				requestAnimationFrame(pollCheck);
			};

			// 延迟启动轮询，给DOM一些时间进行初始渲染
			setTimeout(pollCheck, 50);
		});
	};

	// 执行异步获取高度
	getCookieBoxHeight()
		.then(height => {
			cookieTipHeight.value = height;
		})
		.catch(error => {
			console.error("Error getting cookie box height:", error);
			cookieTipHeight.value = 0;
		});
}

const globalBtnBottom = computed(() => {
	if (showCookieTipNew.value || (showCookieTipCommon.value && showCookieTip.value)) {
		return screenWidth.value <= 1024 ? `${cookieTipHeight.value + 20}px` : `${cookieTipHeight.value + 48}px`;
	} else {
		return `40px`;
	}
});
function showContact() {
	hideLiveChat();
	showContactSales.value = true;
	bdRequest([
		{
			logidUrl: location.href,
			newType: 5
		}
	]);
}

const scrollToElement = useScrollToElement();

const { showLiveChat, hideLiveChat } = useLiveChat();

function backTop() {
	scrollToElement({ ele: document.body });
}

const liveChatClick = () => {
	gaPoint();
	showContactSales.value = false;
	showLiveChat();
	bdRequest([
		{
			logidUrl: location.href,
			newType: 1
		}
	]);
};

const onScroll = () => {
	const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
	const clientHeight = window.screen.availHeight;
	const page = /\/products\/\d+\.html/.test(location.pathname) ? 1 : 2;
	if (scrollTop >= clientHeight * page) {
		showBackTopBtn.value = true;
	} else {
		showBackTopBtn.value = false;
	}
	isScroll.value = true;
	if (timer) {
		clearTimeout(timer);
		timer = null;
	}
	timer = window.setTimeout(() => {
		isScroll.value = false;
	}, 200);
};

function toogleFold() {
	showFoldBtn.value = !showFoldBtn.value;
	localStorage.setItem("suspendFold", `${showFoldBtn.value}`);
}

useEventListener("window", "scroll", onScroll);

onMounted(async () => {
	// 初始化时更新 cookie 提示高度
	updateCookieTipHeight();

	// 监听窗口大小变化，重新计算高度
	window.addEventListener("resize", handleResize);

	await nextTick();
	showFoldBtn.value = localStorage.getItem("suspendFold") === "true";

	// 监听 LiveChat iframe 的加载，调整其位置
	const liveChatObserver = new MutationObserver(() => {
		const chatEl = document.querySelector("iframe.fsLiveChat") as HTMLElement;
		if (chatEl) {
			// 解析 globalBtnBottom 的数值部分
			const bottomNum = Number(globalBtnBottom.value.replace("px", "")) || 0;
			if (screenWidth.value >= 768) {
				chatEl.style.bottom = bottomNum - 16 + "px";
			} else {
				chatEl.style.bottom = 0 + "px";
			}
			console.log("chatEl.style.bottom==", chatEl.style.bottom, "globalBtnBottom==", globalBtnBottom.value);
			liveChatObserver.disconnect();
		}
	});
	liveChatObserver.observe(document.body, { childList: true, subtree: true });
});

// 组件卸载时清理资源
onUnmounted(() => {
	// 清理定时器
	if (timer) {
		clearTimeout(timer);
		timer = null;
	}
	if (resizeTimer) {
		clearTimeout(resizeTimer);
		resizeTimer = null;
	}

	// 移除事件监听器
	window.removeEventListener("resize", handleResize);
});

watch([showCookieTipCommon, showCookieTip], updateCookieTipHeight);

// 如果 globalBtnBottom 变化时也要同步 livechat 的 bottom，可以 watch
watch(globalBtnBottom, async val => {
	await nextTick();
	const chatEl = document.querySelector("iframe.fsLiveChat") as HTMLElement;
	if (chatEl) {
		const bottomNum = Number(val.replace("px", "")) || 0;
		if (screenWidth.value >= 768) {
			chatEl.style.bottom = bottomNum - 16 + "px";
		} else {
			chatEl.style.bottom = 0 + "px";
		}

		console.log("chatEl==", chatEl.style.bottom, bottomNum);
	}
});
</script>

<style lang="scss" scoped>
.fs_global_btn_box {
	position: fixed;
	display: flex;
	flex-direction: column;
	right: 20px;
	bottom: 100px;
	z-index: 98;

	@include padMobile() {
		display: none;
		right: 16px;
	}

	&.fs_global_btn_box_scroll {
		@include padMobile() {
			opacity: 0.5;
		}
	}
}

.service_btn_wrap {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	// box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
	// border-radius: 3px;
}

.service_btn_box {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	gap: 8px;
	// height: 96px;
	transition: all 0.3s;
	// overflow: hidden;

	&.service_btn_hide {
		@include padMobile() {
			height: 0;
		}
	}
}

.btn_box {
	width: 48px;
	height: 48px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	transition: all 0.3s;
	cursor: pointer;
	user-select: none;
	position: relative;

	&.back_top_btn {
		opacity: 0;
		margin-bottom: 16px;
		border-radius: 50%;
		// box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);
		transition: all 0.3s;
		cursor: pointer;

		&.active {
			opacity: 1;
		}
	}

	&.livechat_btn {
		border-radius: 50%;
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);

		img {
			width: 24px;
		}
	}

	&.consult_btn {
		border-radius: 50%;
		background-color: #707070;
		box-shadow: 0px 1.71px 6.86px 0px rgba(0, 0, 0, 0.1);

		@include padMobile() {
			background-color: #fff;
			border-radius: 0;
		}

		.iconfont {
			color: #fff;

			&:hover {
				color: #fff;
			}

			@include padMobile() {
				color: $textColor2;

				&:hover {
					color: $textColor1;
				}
			}
		}
	}

	&.btn_box_line {
		@include padMobile() {
			&::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 7px;
				right: 7px;
				background-color: #e5e5e5;
				height: 1px;
				z-index: 2;
			}
		}
	}

	&.fold_btn {
		display: none;

		@include padMobile() {
			display: flex;
			border-radius: 0 0 4px 4px;

			&.btn_folded {
				border-radius: 4px;
			}
		}
	}

	&.btn_folded {
		.iconfont_fold {
			transform: rotateZ(180deg);
		}
	}

	.iconfont {
		font-size: 24px;
		display: block;
		width: 24px;
		height: 24px;
		line-height: 24px;
		color: $textColor2;
		transition: all 0.3s;

		@include padMobile() {
			font-size: 21px;
			display: block;
			width: 21px;
			height: 21px;
			line-height: 21px;

			&:hover {
				color: $textColor2;
			}
		}

		&:hover {
			color: $textColor1;
		}

		&.iconfont_fold {
			font-size: 16px;
			display: block;
			width: 16px;
			height: 16px;
			line-height: 16px;
		}
	}
}

.fs_global_btn_box_padMobile {
	position: fixed;
	display: none;
	flex-direction: column;
	right: 10px;
	bottom: 16px;
	z-index: 98;
	display: none;

	@include padMobile() {
		display: flex;
		right: 20px;
	}

	.padMobile_btn {
		width: 48px;
		height: 48px;
		border-radius: 30px;
		display: flex;
		justify-content: center;
		align-items: center;

		img {
			width: 24px;
		}

		&.consult_btn {
			background: #707070;
			color: #fff;
			margin-top: 8px;
			.iconfont {
				width: 24px;
				height: 24px;
				font-size: 24px;
			}
		}

		&.livechat_btn {
			box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
			background: #ffffff;
			color: #707070;
			.iconfont {
				width: 24px;
				height: 24px;
				font-size: 24px;
			}
		}
	}
}
</style>
