.fs-banner {
	width: 100%;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;

	.common {
		@include contentWidth;
		display: flex;
		height: 100%;
		align-items: center;
		justify-content: center;
		color: $textColor11;

		.banner_txt {
			max-width: 960px;

			h1 {
				@include font32;
				font-weight: 600;
				text-align: center;
			}

			p {
				@include font16;
				text-align: center;
				font-weight: 400;
				line-height: 24px;
				margin-top: 16px;
			}

			.btn {
				text-align: center;

				:deep(.fs-button) {
					&:hover {
						.fs-button--suffix {
							text-decoration: none;
						}

						background-color: #ffffff39;
					}

					margin-top: 24px;
					border: 1px solid #fff;
					border-radius: 4px;
					cursor: pointer;
					display: inline-block;
					position: relative;
					height: 40px;
					line-height: 40px;
					padding: 0 20px 0 24px;
					text-align: center;
					transform: all 0.3s;
				}

				:deep(.fs-button--suffix) {
					margin-right: 0;
					font-size: 13px;
					color: white;
				}

				:deep(.iconfont) {
					color: white;
					font-size: 12px;
					font-weight: 400;
					line-height: 1;
					margin-left: 4px;
					text-align: left;
				}
			}
		}
	}
}

@include pad {
	.fs-banner .common {
		// justify-content: flex-start;

		.banner_txt {
			max-width: 800px;

			h1,
			p {
				padding-left: 16px;
				text-align: center;
			}
		}
	}
}

@include mobile {
	.fs-banner {
		height: 233px;

		.common .banner_txt {
			max-width: 366px;

			// padding: 0 8px;
			h1 {
				@include font24;
				text-align: center;
			}

			p {
				text-align: center;
				@include font14;
				:deep(br) {
					display: none;
				}
			}
		}
	}
}
