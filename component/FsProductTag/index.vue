<template>
	<div class="fs-product-tag" @click.prevent>
		<FsFlex vertical :align="'center'" justify="center">
			<div v-if="tag.text && showTagTitle" class="fs-product-tag__title" v-html="tag.text"></div>
			<div class="fs-product-tag__content">
				<img class="fs-product-tag__img" :src="tag.images_url" alt="tag" />
				<div
					v-for="(item, index) in points"
					:key="`${index}${item.points_left}${item.points_top}`"
					class="fs-product-tag__pointBox"
					:style="{ left: item.points_left + '%', top: item.points_top + '%' }">
					<!--show_type 为0或者2 产品信息类型 -->
					<template v-if="item.show_type === 0 || item.show_type === 2">
						<SingleProduct
							:tag="item"
							:index="index"
							:type="type"
							:mobileStyle="mobileStyle"
							:popperContentStyle="popperContentStyle"
							:closeable="closeable"
							:placement="getDirection(item.direction)" />
					</template>
					<!-- todo -->
					<template v-if="item.show_type === 1"></template>
					<!--show_type 为3 多产品信息类型 -->
					<template v-if="item.show_type === 3">
						<MultiProduct
							:tag="item"
							:mobileStyle="mobileStyle"
							:popperContentStyle="popperMultiProduct"
							:closeable="closeable"
							:type="type"
							:placement="getDirection(item.direction)" />
					</template>
				</div>
			</div>
		</FsFlex>
		<!-- <AddCart :id="addCartId" v-model="addCartStatus" /> -->
		<AddCartSuccessPopup v-model="showAddCartPopup" :loading="addCartLoading" :productInfo="productInfo" />

		<!-- 新增：错误提示弹窗 -->
		<FsMessagePopup v-model:show="showErrorPopup" width="480px" :title="localeLang('error.insufficientQtyAvailable')" :message="errorMessage" />
	</div>
</template>

<script setup lang="ts">
import { ref, provide, computed } from "vue";
import { FsFlex } from "fs-design";
import { placementList, fsProductContextKey } from "./types";
import type { FsTagProps, PointsDatum } from "./types";
import SingleProduct from "./components/SingleProduct.vue";
import MultiProduct from "./components/MultiProduct.vue";
import { website_prefix } from "@/constants/validate";
// import AddCart from "@/popup/AddCartPopup/index.vue";
import AddCartSuccessPopup from "@/popup/AddCartSuccessPopup/index.vue";
// 新增：引入FsMessagePopup组件和useLocaleLang
import FsMessagePopup from "@/component/FsMessagePopup/FsMessagePopup.vue";

/**
 * show_type 为0或者2 产品信息类型
 * show_type 为1 提示语类型
 * show_type 为3 多产品信息类型
 */
defineOptions({
	name: "FsProductTag"
});
const points = ref();
const props = withDefaults(defineProps<FsTagProps>(), {
	transfer: true,
	closeable: true,
	type: ""
});
const addCartId = ref(0);
const cartStore = useCartStore();
const bdRequest = useBdRequest();
const addCartLoading = ref(false);
const productInfo = ref("");
const addCartStatus = ref(false);
const separateList = ref([]);
// 新增：错误处理相关状态
const showErrorPopup = ref(false);
const errorMessage = ref("");
const localeLang = useLocaleLang();
const showAddCartPopup = computed({
	get: () => {
		return addCartStatus.value && separateList.value?.some((item: { show_add_window: number }) => item.show_add_window === 1);
	},
	set: (val: boolean) => {
		addCartStatus.value = val;
	}
});
const route = useRoute();
const mobileStyle = ref({
	padding: "0 20px 20px 20px"
});

const popperContentStyle = computed(() => {
	return {
		padding: "12px",
		maxWidth: "250px"
	};
});

const popperMultiProduct = computed(() => {
	return {
		padding: "8px",
		minWidth: "300px"
	};
});
onMounted(() => {
	const { points_data, scene_explain_points, multi_points } = props.tag;
	if (points_data?.length || scene_explain_points?.length || multi_points?.length) {
		points.value = [...(points_data || []), ...(scene_explain_points || []), ...(multi_points || [])];
	}
});

const windowPlayerFnc = (data: PointsDatum) => {
	if (window.dataLayer) {
		let route_path = route.path;
		route_path = route_path.replace(/^\//, "");
		if (website_prefix.test(route_path)) {
			route_path = route_path.replace(website_prefix, "");
		}
		if (/^products/.test(route_path)) {
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory: `productDetailPage_${route.params.id || ""}`,
				eventAction: "product_tree_tag",
				eventLabel: `Upload_${props.type ? `${props.type}_` : ``}${data?.products_tag_info && data?.products_tag_info.length ? data?.products_tag_info?.[0].products_id : ""}`,
				nonInteraction: false
			});
		} else if (/^solutions/.test(route_path)) {
			const path = route.path.substring(route.path.indexOf("/"), route.path.lastIndexOf("/"));
			const solutionType = path.substr(path.lastIndexOf("/") + 1);
			window.dataLayer.push({
				event: "uaEvent",
				eventCategory: `Solution Page_${solutionType}`,
				eventAction: "product_tree_tag",
				eventLabel: `Upload_${props.type ? `${props.type}_` : ``}${data?.products_tag_info && data?.products_tag_info?.length ? data?.products_tag_info?.[0].products_id : ""}`,
				nonInteraction: false
			});
		}
	}
};

const getDirection = (direction: string) => (direction ? placementList[direction] : "top");
provide(fsProductContextKey, {
	addCartId,
	addCartStatus,
	addCartLoading,
	windowPlayerFnc,
	productInfo
});

const addToCart = async () => {
	try {
		bdRequest([
			{
				logidUrl: location.href,
				newType: 46
			}
		]);
		addCartLoading.value = true;
		const obj = {
			products: [
				{
					products_id: addCartId,
					qty: 1,
					isChecked: 1,
					attributes: {}
				}
			]
		};
		separateList.value = [];
		const { data, error } = await useRequest.post(`/api/cart/addNew`, { data: obj });

		// 新增：添加422错误状态处理
		if (error.value?.data && error.value.data?.code === 422) {
			if (Object.keys(error.value?.data?.errors).length) {
				errorMessage.value = error.value.data.errors[Object.keys(error.value?.data.errors)[0]];
				showErrorPopup.value = true;
			}
			// 重置addCartStatus，确保按钮可以再次点击
			addCartStatus.value = false;
			return; // 提前返回，不执行后续逻辑
		}

		if (data.value?.data) {
			cartStore.setAddCartData(data.value.data);
			cartStore.getCart();
			separateList.value = data.value.data?.cartList;
			// 只有当没有 show_add_window 为 1 的商品时才重置 addCartStatus
			const hasShowAddWindow = data.value.data.cartList?.some((item: { show_add_window: number }) => item.show_add_window === 1);
			if (!hasShowAddWindow) {
				addCartStatus.value = false;
			}

			addCartLoading.value = false;
		}
	} finally {
		addCartLoading.value = false;
	}
};

watch(addCartStatus, (newVal: boolean) => {
	if (newVal) {
		addToCart();
	}
});
</script>
<style lang="scss" scoped>
@import url("./styles/style.scss");
</style>
