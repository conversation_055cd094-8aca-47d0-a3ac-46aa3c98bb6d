interface CountryState {
	areaList: any[]; // 需要业务相关人员补充，比如下列any类型，需要替换成具体类型
	isAreaFetching: boolean;
	countryList: any[]; // 需要业务相关人员补充，比如下列any类型，需要替换成具体类型
	isCountryFetching: boolean;
	sortLength: number;
	isStateFetching: boolean;
	stateList: any;
	countryNameList: any;
	countryListJava: any[];
	isCountryJavaFetching: boolean;
}
// 将java数据源字段名称转化为php数据源字段名称
const javaCountryTransPhp = (data: any[]) => {
	return (
		data.map((i: any) => ({
			...i,
			countries_name: i.name,
			iso_code: i.code,
			tel_prefix: i.telPrefix,
			countries_id: i.id,
			states:
				i.stateList?.map((j: any) => ({
					...j,
					states: j.label,
					states_code: j.code
				})) || [],
			citys:
				i.cityList?.map((j: any) => ({
					...j,
					city: j.label,
					city_code: j.code
				})) || []
		})) || []
	);
};

export const useCountryStore = defineStore("country", {
	state: (): CountryState => {
		return {
			areaList: [],
			isAreaFetching: false,
			countryList: [],
			isCountryFetching: false,
			sortLength: 0, // 有些情况下 热门国家和普通国家之间会有分割线，分割线所在length
			isStateFetching: false,
			stateList: {},
			countryNameList: {},
			countryListJava: [],
			isCountryJavaFetching: false
		};
	},
	getters: {},
	actions: {
		async getAreaData() {
			if (!this.areaList.length && !this.isAreaFetching) {
				this.isAreaFetching = true;
				// const { data, error } = await useRequest.post(`/static`, {
				// 	data: {
				// 		method: "GET",
				// 		url: `/api/website/siteRelationNew`,
				// 		filterId: "siteRelationNew", // 标识字段
				// 		moduleName: "siteRelationNew" // 模块名称
				// 	}
				// });

				const { data, error } = await useRequest.get("/api/website/siteRelationNew");

				console.log("siteRelationNew__");
				// console.log(data.value.data);
				// console.log(error);
				if (data?.value?.data?.list?.length) {
					const { list } = data.value.data;
					this.areaList = list?.length ? list : [];
					this.isAreaFetching = false;
				}
			}
		},
		// async getAreaData() {
		// 	if (!this.areaList.length && !this.isAreaFetching) {
		// 		this.isAreaFetching = true;
		// 		// const { data, error } = await useRequest.post(`/static`, {
		// 		// 	data: {
		// 		// 		method: "GET",
		// 		// 		url: `/api/website/siteRelationNew`,
		// 		// 		filterId: "siteRelationNew", // 标识字段
		// 		// 		moduleName: "siteRelationNew" // 模块名称
		// 		// 	}
		// 		// });

		// 		const { data, error } = await useRequest.get("/api/website/siteRelationNew");

		// 		console.log("siteRelationNew__");
		// 		// console.log(data.value.data);
		// 		// console.log(error);
		// 		if (data?.value?.data?.list?.length) {
		// 			const { list } = data.value.data;
		// 			this.areaList = list?.length ? list : [];
		// 			this.isAreaFetching = false;
		// 		}
		// 	}
		// },
		async getCountryName() {
			const { data, error } = await useRequest.get("/api/website/getCountries");
			if (data?.value?.data) {
				this.countryNameList = data?.value?.data;
			}
		},
		async getState() {
			if (!this.countryList.length && !this.isStateFetching) {
				this.isStateFetching = true;
				const { data, error } = await useRequest.post("/api/website/getStatesOfCountries");
				if (data && data.value) {
					this.stateList = data.value.data;
					this.isStateFetching = false;
				}
			}
		},
		async getCountryData() {
			if (!this.countryList.length && !this.isCountryFetching) {
				this.isCountryFetching = true;

				const { data, error } = await useRequest.get("/api/website/siteMain");

				if (data && data.value) {
					const { countryList } = data.value.data;
					this.countryList = countryList.source || [];
					this.sortLength = countryList.sortLength || 0;
					this.isCountryFetching = false;
				}
			}
		},

		async getCountryDataJava() {
			if (this.countryListJava.length === 0 && !this.isCountryJavaFetching) {
				this.isCountryJavaFetching = true;
				const { data, error } = await useRequest.get("/order-api/v1/countryInfo/listCountryState");
				const res = data.value;
				if (res && res.data) {
					this.countryListJava = javaCountryTransPhp(res.data || []);
				}
				this.isCountryJavaFetching = false;
			}
		}
	}
});
