import { Base64 } from "js-base64";
import { useCookies } from "../composables/useCookies";
import { Traditional2Simplified } from "@/utils/Traditional2Simplified";
import { removeQueryParams } from "@/utils/utils";

interface WebsiteState {
	chinese_country_name: string;
	id: number;
	website: string;
	urlWebsite: string;
	locale: string;
	iso_code: string;
	currency: string;
	language: string;
	country_name: string;
	countries_id: number;
	language_id: number;
	warehouse: string;
	tel_prefix: string;
	symbol: string;
	clientIp: string;
	timezone: string;
	isEuUnion: boolean;
	gpcEnabled: boolean;
}

export interface IUpdateWebsiteParams {
	currency: string;
	iso_code: string;
	language: string;
}

export const useWebsiteStore = defineStore("website", {
	state: (): WebsiteState => {
		// const website = useCookies("website");
		// const iso_code = useCookies("iso_code");
		// const currency = useCookies("currency");
		// const language = useCookies("language");
		// // console.log(website);
		return {
			chinese_country_name: "",
			id: 0,
			website: "",
			urlWebsite: "",
			iso_code: "",
			currency: "",
			language: "",
			locale: "",
			country_name: "",
			countries_id: 0,
			language_id: 0,
			warehouse: "",
			tel_prefix: "",
			symbol: "",
			clientIp: "",
			timezone: "",
			isEuUnion: false,
			gpcEnabled: false
		};
	},
	getters: {
		isCn: state => state.website === "cn",
		isCnTr: state => ["hk", "tw", "mo"].includes(state.website),
		isSg: state => state.website === "sg",
		isJp: state => state.website === "jp",
		isJpEn: state => !["jp"].includes(state.website) && state.countries_id === 107,
		isRussia: state => state.countries_id === 176,
		isSingaporeCountry: state => state.countries_id === 188, // 新加坡国家
		isAustraliaCountry: state => state.countries_id === 13, // 澳大利亚国家
		isJpCountry: state => state.countries_id === 107, // 日本国家
		communityWebsite: state => {
			let str = "";
			if (state.website) {
				if (["en", "uk", "au", "sg", "de-en", "eu-en"].includes(state.website)) {
					str = "";
				} else if (state.website === "mx") {
					str = `es`;
				} else if (["tw", "mo"].includes(state.website)) {
					str = `hk`;
				} else {
					str = `${state.website}`;
				}
			}
			return str;
		},
		// 欧洲站点，用户是否同意营销类sdk加载   如果是美国且开启了gpc，设置为false
		isLoadAdSdk: state => {
			console.log("isLoadAdSdk");
			console.log(state.website);
			const fs_marketing_sdk = useCookies("fs_marketing_sdk");
			// 处理欧洲站点cookies合规问题
			const EuropeSiteMap = ["de-en", "de", "fr", "es", "it"];
			return state.iso_code === "US" && state.gpcEnabled ? false : !(EuropeSiteMap.includes(state.website as string) && !fs_marketing_sdk.value);
		}
	},
	actions: {
		setWebsiteInfo(payload: any) {
			this.id = payload?.id || "";
			this.iso_code = payload?.iso_code || "";
			this.currency = payload?.currency || "";
			this.country_name = payload?.country_name || "";
			this.website = payload?.website || "";
			this.language = payload?.language || "";
			this.countries_id = payload?.countries_id || "";
			this.language_id = payload?.language_id || "";
			this.warehouse = payload?.warehouse || "";
			this.isEuUnion = typeof payload?.isEuUnion === "boolean" ? payload.isEuUnion : false;
			this.locale = payload?.locale || "";
			this.tel_prefix = payload?.tel_prefix || "";
			this.timezone = payload?.timezone || "";
			this.symbol = payload?.symbol || "";
		},
		async updateWebsite(payload: any, isFetch: boolean = true) {
			/**
			 * payload 	必传三个参数   iso_code,language,currency
			 *      cllback  接口请求成功后回调函数，选传
			 */

			const headers = useRequestHeaders();
			const url = useRequestURL();
			const runtimeConfig = useRuntimeConfig();
			const { $i18n, $switchLocalePath } = useNuxtApp();
			const { locale, setLocale } = $i18n;
			const localeLink = useLocaleLink();
			const route = useRoute();
			const metaStore = useMeatStore();
			// console.log("000000000000")
			// console.log($i18n)
			// console.log(url)
			// console.log("0000000000000000000")

			// const website = useCookies("website");
			// const iso_code = useCookies("iso_code");
			// const currency = useCookies("currency");
			// const language = useCookies("language");
			const fs_websiteinfo = useCookies("fs_websiteinfo");
			const preWebsiteInfo = useCookies("preWebsiteInfo");
			// const zx_update_websiteinfo = useCookies("zx_update_websiteinfo");
			// const oldWebsite = this.website;

			let responseWebsite: any = {};
			if (isFetch) {
				let obj = {};
				if (payload.iso_code && payload.language && payload.currency) {
					obj = {
						iso_code: payload.iso_code,
						language: payload.language,
						// language: payload.language,
						currency: payload.currency
					};
				}

				// const { data, error } = await useRequest.post("/api/website/updateSiteInfoNew", {
				// 	headers: {
				// 		"Update-Website-Param": Base64.encode(JSON.stringify(obj))
				// 	}
				// });
				const { data, error } = await useRequest.post("/api/website/updateSiteInfo", {
					data: obj
				});
				console.log("1122233__uuu");
				console.log(data);
				console.log(error);
				if (data?.value?.data) {
					responseWebsite = data.value.data;
				}
			} else {
				responseWebsite = payload;
			}

			// responseWebsite = {
			// 	"countries_id": 222,
			// 	"country_name": "United Kingdom",
			// 	"currency": "GBP",
			// 	"id": 301,
			// 	"isCookie": false,
			// 	"isEuUnion": "false",
			// 	"iso_code": "GB",
			// 	"language": "English",
			// 	"language_id": 9,
			// 	"locale": "uk",
			// 	"symbol": "£",
			// 	"tel_prefix": "+44",
			// 	"timezone": "Europe/London",
			// 	"warehouse": "UK",
			// 	"website": "uk"
			// }

			if (responseWebsite) {
				const updateWebsiteInfoFlag: any = useCookies("updateWebsiteInfoFlag");
				updateWebsiteInfoFlag.value = 1;
				nextTick(() => {
					// setLocale(responseWebsite.website);
					let p = "";

					if (["PROD", "PROD_INDEX"].includes(runtimeConfig.public.VITE_NUXT_ENV) && runtimeConfig.public.NODE_ENV === "production") {
						if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
							// p = `https://cn.fs.com/${responseWebsite.website}`;
							p = `https://cn.fs.com${responseWebsite.website === "cn" ? "" : `/${responseWebsite.website}`}`;
						}
					} else if (["PROD_CN"].includes(runtimeConfig.public.VITE_NUXT_ENV) && runtimeConfig.public.NODE_ENV === "production") {
						if (!["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
							p = `https://www.fs.com/${responseWebsite.website === "en" ? "" : `/${responseWebsite.website}`}`;
						}
					}

					// 判断是否是主站
					// if (
					// 	(["PROD_CN"].includes(runtimeConfig?.public?.VITE_NUXT_ENV) &&
					// 		this.website === "cn" &&
					// 		this.website !== responseWebsite.website) ||
					// 	(!["PROD_CN"].includes(runtimeConfig?.public?.VITE_NUXT_ENV) &&
					// 		this.website === "en" &&
					// 		this.website !== responseWebsite.website)
					// ) {
					// 	p += route.fullPath;
					// } else {
					// 	if (this.website !== responseWebsite.website) {
					// 		p += route.fullPath;
					// 	}
					// }

					p += route.fullPath;
					// if (runtimeConfig.public.VITE_NUXT_ENV && ["PROD", "PROD_CN"].includes(runtimeConfig.public.VITE_NUXT_ENV)) {
					// 	if (["PROD"].includes(runtimeConfig.public.VITE_NUXT_ENV)) {
					// 		if (responseWebsite.website === oldWebsite) {
					// 			p = location.href
					// 				.replace(/country=[^&]*&?/g, "")
					// 				.replace(/currency=[^&]*&?/g, "")
					// 				.replace(/languages=[^&]*&?/g, "");
					// 		} else {
					// 			if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
					// 				p = `https://cn.fs.com`;
					// 				if (responseWebsite.website !== "cn") {
					// 					p += `/${responseWebsite.website}`;
					// 				}
					// 				p += route.fullPath;
					// 				console.log("111111111111");
					// 				console.log(p);
					// 				p = localeLink(p, responseWebsite.website);
					// 				// window.location.href = p;
					// 			} else {
					// 				p = localeLink(route.fullPath, responseWebsite.website);
					// 				// window.location.href = p;
					// 			}
					// 		}
					// 	} else {
					// 		if (responseWebsite.website === oldWebsite.value) {
					// 			p = location.href
					// 				.replace(/country=[^&]*&?/g, "")
					// 				.replace(/currency=[^&]*&?/g, "")
					// 				.replace(/languages=[^&]*&?/g, "");
					// 			// window.location.href = p;
					// 		} else {
					// 			if (["cn", "hk", "tw", "mo"].includes(responseWebsite.website)) {
					// 				p = localeLink(route.fullPath, responseWebsite.website);
					// 				// window.location.href = p;
					// 			} else {
					// 				p = `https://www.fs.com`;
					// 				if (responseWebsite.website !== "en") {
					// 					p += `/${responseWebsite.website}`;
					// 				}
					// 				p += route.fullPath;
					// 				p = localeLink(p, responseWebsite.website);

					// 				// window.location.href = p;
					// 			}
					// 		}
					// 	}
					// } else {
					// 	if (responseWebsite.website !== oldWebsite.value) {
					// 		p = localeLink(route.fullPath, responseWebsite.website);
					// 		// console.log("pppppppppppppppp");
					// 		// console.log(route.fullPath);
					// 		// console.log(responseWebsite.website);
					// 		// console.log(localeLink(route.fullPath, responseWebsite.website));
					// 		// window.location.href = p;
					// 	} else {
					// 		p = location.href
					// 			.replace(/country=[^&]*&?/g, "")
					// 			.replace(/currency=[^&]*&?/g, "")
					// 			.replace(/languages=[^&]*&?/g, "");
					// 		// console.log("==============");
					// 		// console.log(website.value);
					// 		// console.log(iso_code.value);
					// 		// console.log(language.value);
					// 		// console.log(currency.value);
					// 		// window.location.href = p;
					// 	}
					// }
					console.log("uuuuuuuuuuu");
					console.log(p);
					// zx_update_websiteinfo.value = null;
					fs_websiteinfo.value = responseWebsite;
					preWebsiteInfo.value = responseWebsite;
					// website.value = responseWebsite.website;
					// iso_code.value = responseWebsite.iso_code;
					// currency.value = responseWebsite.currency;
					// language.value = responseWebsite.language;
					// this.website = responseWebsite.website;
					// this.locale = responseWebsite?.locale;
					// this.iso_code = responseWebsite.iso_code;
					// this.currency = responseWebsite.currency;
					// this.language = responseWebsite.language;
					// this.country_name = responseWebsite.country_name;
					// this.countries_id = responseWebsite.countries_id;
					// this.language_id = responseWebsite.language_id;
					// this.warehouse = responseWebsite.warehouse;
					// this.isEuUnion = responseWebsite.isEuUnion;
					// this.tel_prefix = responseWebsite.tel_prefix;
					// this.timezone = responseWebsite.timezone;
					// this.symbol = responseWebsite.symbol;
					this.setWebsiteInfo(responseWebsite);
					p = localeLink(removeQueryParams(p, ["country", "currency", "languages"]), responseWebsite.website);
					setTimeout(() => {
						console.log("new_change_website");
						console.log(p);
						window.location.href = p;
					}, 1);
				});
			}
		}
	}
});
